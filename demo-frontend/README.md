# 量化分析系统演示前端

这是一个简单的前端演示项目，用于展示量化分析后端API的功能。

## 功能特点

- 股票K线数据可视化（支持日K/周K/月K）
- 各种技术指标展示：
  - MACD指标
  - KDJ指标
  - RSI指标
  - 成交量分析
- 响应式设计，适配不同屏幕尺寸
- 简洁直观的用户界面

## 快速开始

### 安装依赖

```bash
npm install
```

### 开发环境运行

```bash
npm run dev
```

### 生产环境构建

```bash
npm run build
```

## 后端API

该演示前端项目依赖于量化分析后端API，主要使用以下接口：

- `/kline/{stock_code}` - 获取股票K线数据
- `/indicators/macd/{stock_code}` - 获取MACD指标数据
- `/indicators/kdj/{stock_code}` - 获取KDJ指标数据
- `/indicators/rsi/{stock_code}` - 获取RSI指标数据
- `/indicators/volume/{stock_code}` - 获取成交量分析数据

## 技术栈

- Vue 3
- Vue Router
- Element Plus
- ECharts
- Axios
