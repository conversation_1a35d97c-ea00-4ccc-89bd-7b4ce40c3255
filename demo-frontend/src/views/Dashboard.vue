<template>
  <div class="dashboard-container container">
    <h1 class="page-title">量化分析系统演示</h1>
    
    <!-- 股票选择器 -->
    <div class="card mb-20">
      <div class="flex-between mb-10">
        <h2 class="section-title">选择股票</h2>
        <div>
          <el-input
            v-model="searchQuery"
            placeholder="输入股票代码或名称"
            clearable
            style="width: 200px"
            @input="filterStocks"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
      </div>
      
      <el-table :data="filteredStocks" style="width: 100%" @row-click="handleSelectStock">
        <el-table-column prop="code" label="股票代码" width="120"></el-table-column>
        <el-table-column prop="name" label="股票名称"></el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="scope">
            <el-button type="primary" size="small" @click.stop="viewDetail(scope.row.code)">查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <!-- 当前选中股票 -->
    <div v-if="currentStock" class="card mb-20">
      <h2 class="section-title">{{ currentStock.name }} ({{ currentStock.code }})</h2>
      
      <!-- 日期选择器和频率选择 -->
      <div class="mb-20 flex-between">
        <div>
          <el-radio-group v-model="freqType" size="small" @change="fetchKlineData">
            <el-radio-button label="D">日线</el-radio-button>
            <el-radio-button label="W">周线</el-radio-button>
            <el-radio-button label="M">月线</el-radio-button>
          </el-radio-group>
        </div>
        
        <div>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            size="small"
            @change="fetchKlineData"
          ></el-date-picker>
        </div>
      </div>
      
      <!-- K线图 -->
      <div v-loading="loading.kline" class="chart-container">
        <kline-chart 
          v-if="klineData.length > 0" 
          :data="klineData" 
          :height="400" 
          :title="`${currentStock.name} (${currentStock.code}) - K线图`"
        ></kline-chart>
        <div v-else class="no-data-placeholder">暂无数据，请选择其他日期或股票</div>
      </div>
      
      <!-- 指标选择 -->
      <div class="mb-20 mt-20">
        <h3>技术指标</h3>
        <el-checkbox-group v-model="selectedIndicators" @change="handleIndicatorChange">
          <el-checkbox label="macd">MACD</el-checkbox>
          <el-checkbox label="kdj">KDJ</el-checkbox>
          <el-checkbox label="rsi">RSI</el-checkbox>
          <el-checkbox label="volume">成交量</el-checkbox>
        </el-checkbox-group>
      </div>
      
      <!-- 技术指标图表 -->
      <div v-for="indicator in selectedIndicators" :key="indicator" class="mb-20">
        <div v-loading="loading[indicator]" class="chart-container">
          <indicator-chart 
            v-if="indicatorData[indicator] && indicatorData[indicator].length > 0"
            :data="indicatorData[indicator]"
            :indicator-type="indicator"
            :height="300"
            :title="getIndicatorTitle(indicator)"
          ></indicator-chart>
          <div v-else class="no-data-placeholder">加载中...</div>
        </div>
      </div>
      
      <!-- 全部指标按钮 -->
      <div class="text-center">
        <el-button type="primary" @click="viewDetail(currentStock.code)">查看完整分析</el-button>
      </div>
    </div>
    
    <!-- 未选择股票时的提示 -->
    <div v-if="!currentStock" class="card mb-20 text-center">
      <el-empty description="请从上方列表中选择一只股票进行分析"></el-empty>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { Search } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { klineService, indicatorService } from '../api';
import { demoStockList, formatDate } from '../utils';
import KlineChart from '../components/KlineChart.vue';
import IndicatorChart from '../components/IndicatorChart.vue';

export default {
  name: 'Dashboard',
  components: {
    KlineChart,
    IndicatorChart,
    Search
  },
  setup() {
    const router = useRouter();
    const stockList = ref(demoStockList);
    const searchQuery = ref('');
    const filteredStocks = ref([...demoStockList]);
    const currentStock = ref(null);
    const freqType = ref('D');
    
    // 设置默认日期范围：最近100天
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 100);
    
    const dateRange = ref([]);
    
    const klineData = ref([]);
    const selectedIndicators = ref([]);
    const indicatorData = reactive({});
    
    const loading = reactive({
      kline: false,
      macd: false,
      kdj: false,
      rsi: false,
      volume: false
    });
    
    // 过滤股票列表
    const filterStocks = () => {
      if (!searchQuery.value) {
        filteredStocks.value = [...stockList.value];
        return;
      }
      
      const query = searchQuery.value.toLowerCase();
      filteredStocks.value = stockList.value.filter(stock => 
        stock.code.includes(query) || stock.name.toLowerCase().includes(query)
      );
    };
    
    // 选择股票
    const handleSelectStock = (row) => {
      currentStock.value = row;
      fetchKlineData();
    };
    
    // 获取K线数据
    const fetchKlineData = async () => {
      if (!currentStock.value) return;
      
      loading.kline = true;
      try {
        const params = {
          freq: freqType.value,
          start_date: dateRange.value[0],
          end_date: dateRange.value[1]
        };
        
        const result = await klineService.getKlineData(currentStock.value.code, params);
        klineData.value = result.data || [];
        
        // 如果选择了指标，重新获取指标数据
        if (selectedIndicators.value.length > 0) {
          fetchSelectedIndicators();
        }
      } catch (error) {
        console.error('获取K线数据失败:', error);
        ElMessage.error('获取K线数据失败，请稍后重试');
        klineData.value = [];
      } finally {
        loading.kline = false;
      }
    };
    
    // 获取选中的技术指标数据
    const fetchSelectedIndicators = () => {
      selectedIndicators.value.forEach(indicator => {
        fetchIndicatorData(indicator);
      });
    };
    
    // 获取指标数据
    const fetchIndicatorData = async (indicator) => {
      if (!currentStock.value) return;
      
      loading[indicator] = true;
      try {
        const params = {
          start_date: dateRange.value[0],
          end_date: dateRange.value[1],
          freq: freqType.value
        };
        
        let result;
        switch (indicator) {
          case 'macd':
            result = await indicatorService.getMACD(currentStock.value.code, params);
            indicatorData.macd = result.data || [];
            break;
          case 'kdj':
            result = await indicatorService.getKDJ(currentStock.value.code, params);
            indicatorData.kdj = result.data || [];
            break;
          case 'rsi':
            result = await indicatorService.getRSI(currentStock.value.code, params);
            indicatorData.rsi = result.data || [];
            break;
          case 'volume':
            result = await indicatorService.getVolumeAnalysis(currentStock.value.code, params);
            indicatorData.volume = result.data || [];
            break;
        }
      } catch (error) {
        console.error(`获取${indicator}指标数据失败:`, error);
        ElMessage.error(`获取${indicator}指标数据失败，请稍后重试`);
        indicatorData[indicator] = [];
      } finally {
        loading[indicator] = false;
      }
    };
    
    // 处理指标选择变化
    const handleIndicatorChange = () => {
      Object.keys(indicatorData).forEach(key => {
        if (!selectedIndicators.value.includes(key)) {
          indicatorData[key] = [];
        }
      });
      
      fetchSelectedIndicators();
    };
    
    // 查看股票详情
    const viewDetail = (code) => {
      router.push(`/stock/${code}`);
    };
    
    // 获取指标标题
    const getIndicatorTitle = (indicator) => {
      const titles = {
        macd: 'MACD指标 (DIFF/DEA/MACD)',
        kdj: 'KDJ指标 (超买/超卖)',
        rsi: 'RSI指标 (相对强弱)',
        volume: '成交量分析'
      };
      return titles[indicator] || indicator.toUpperCase();
    };
    
    // 组件挂载时，如果URL中有股票代码，直接加载该股票
    onMounted(() => {
      const pathParts = window.location.pathname.split('/');
      const code = pathParts[pathParts.length - 1];
      if (code && /^\d{6}$/.test(code)) {
        const stock = stockList.value.find(s => s.code === code);
        if (stock) {
          currentStock.value = stock;
          fetchKlineData();
        }
      }
    });
    
    // 监听当前股票变化
    watch(currentStock, () => {
      if (currentStock.value) {
        selectedIndicators.value = []; // 重置选中的指标
        indicatorData = reactive({}); // 重置指标数据
      }
    });
    
    return {
      stockList,
      searchQuery,
      filteredStocks,
      currentStock,
      freqType,
      dateRange,
      klineData,
      selectedIndicators,
      indicatorData,
      loading,
      filterStocks,
      handleSelectStock,
      fetchKlineData,
      handleIndicatorChange,
      viewDetail,
      getIndicatorTitle
    };
  }
};
</script>

<style scoped>
.dashboard-container {
  padding-bottom: 50px;
}

.section-title {
  font-size: 16px;
  margin-bottom: 15px;
}

.no-data-placeholder {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #909399;
  font-size: 14px;
  background: #f5f7fa;
  border-radius: 4px;
}

.mt-20 {
  margin-top: 20px;
}
</style>
