<template>
  <div class="stock-detail-container container">
    <div class="flex-between mb-20">
      <h1 class="page-title">{{ stockInfo.name }} ({{ stockCode }}) - 详细分析</h1>
      <el-button @click="goBack">返回首页</el-button>
    </div>

    <!-- 配置面板 -->
    <div class="card mb-20">
      <div class="flex-between">
        <div>
          <el-radio-group v-model="freqType" size="small" @change="fetchAllData">
            <el-radio-button label="D">日线</el-radio-button>
            <el-radio-button label="W">周线</el-radio-button>
            <el-radio-button label="M">月线</el-radio-button>
          </el-radio-group>
        </div>
        
        <div>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            size="small"
            @change="fetchAllData"
          ></el-date-picker>
        </div>
      </div>
    </div>
    
    <!-- K线图 -->
    <div class="card mb-20">
      <h2 class="section-title">K线走势</h2>
      <div v-loading="loading.kline" class="chart-container">
        <kline-chart 
          v-if="klineData.length > 0" 
          :data="klineData" 
          :height="450" 
          :title="`${stockInfo.name} (${stockCode}) - K线图`"
          :indicators="chartIndicators"
        ></kline-chart>
        <div v-else class="no-data-placeholder">暂无数据，请选择其他日期</div>
      </div>
    </div>
    
    <!-- 技术指标卡片 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <!-- MACD指标 -->
        <div class="card mb-20">
          <h2 class="section-title">MACD指标分析</h2>          <div v-loading="loading.macd" class="chart-container">
            <indicator-chart 
              v-if="indicatorData.macd && indicatorData.macd.length > 0"
              :data="indicatorData.macd"
              indicator-type="macd"
              :height="300"
              title="MACD指标 (DIFF/DEA/MACD)"
            ></indicator-chart>
            <div v-else-if="indicatorData.macdError" class="no-data-placeholder">{{ indicatorData.macdError }}</div>
            <div v-else class="no-data-placeholder">加载中...</div>
          </div>
          
          <div class="indicator-description">
            <h4>MACD指标说明：</h4>
            <p>MACD是由两线一柱组成，快线为DIFF，慢线为DEA，柱状图表示两线之差（MACD）。</p>
            <ul>
              <li>当DIFF由下向上突破DEA，形成金叉，为买入信号</li>
              <li>当DIFF由上向下突破DEA，形成死叉，为卖出信号</li>
              <li>当MACD柱由负变正，表示市场由空头转为多头</li>
              <li>当MACD柱由正变负，表示市场由多头转为空头</li>
            </ul>
          </div>
        </div>
      </el-col>
      
      <el-col :span="12">
        <!-- KDJ指标 -->
        <div class="card mb-20">
          <h2 class="section-title">KDJ指标分析</h2>          <div v-loading="loading.kdj" class="chart-container">
            <indicator-chart 
              v-if="indicatorData.kdj && indicatorData.kdj.length > 0"
              :data="indicatorData.kdj"
              indicator-type="kdj"
              :height="300"
              title="KDJ指标 (超买/超卖)"
            ></indicator-chart>
            <div v-else-if="indicatorData.kdjError" class="no-data-placeholder">{{ indicatorData.kdjError }}</div>
            <div v-else class="no-data-placeholder">加载中...</div>
          </div>
          
          <div class="indicator-description">
            <h4>KDJ指标说明：</h4>
            <p>KDJ指标是一种超买超卖指标，由K、D、J三条线组成。</p>
            <ul>
              <li>当K线和D线同时向上突破20，为买入信号</li>
              <li>当K线和D线同时向下跌破80，为卖出信号</li>
              <li>J值大于100表示超买，小于0表示超卖</li>
              <li>当K线与D线交叉时，形成买卖信号</li>
            </ul>
          </div>
        </div>
      </el-col>
    </el-row>
    
    <el-row :gutter="20">
      <el-col :span="12">
        <!-- RSI指标 -->
        <div class="card mb-20">
          <h2 class="section-title">RSI指标分析</h2>          <div v-loading="loading.rsi" class="chart-container">
            <indicator-chart 
              v-if="indicatorData.rsi && indicatorData.rsi.length > 0"
              :data="indicatorData.rsi"
              indicator-type="rsi"
              :height="300"
              title="RSI指标 (相对强弱)"
            ></indicator-chart>
            <div v-else-if="indicatorData.rsiError" class="no-data-placeholder">{{ indicatorData.rsiError }}</div>
            <div v-else class="no-data-placeholder">加载中...</div>
          </div>
          
          <div class="indicator-description">
            <h4>RSI指标说明：</h4>
            <p>RSI是相对强弱指标，用于判断市场的超买超卖情况和趋势强度。</p>
            <ul>
              <li>RSI值在80以上为超买，20以下为超卖</li>
              <li>RSI值长期在50以上，说明市场处于多头趋势</li>
              <li>RSI值长期在50以下，说明市场处于空头趋势</li>
              <li>多周期RSI同时向上或向下发生背离，信号更可靠</li>
            </ul>
          </div>
        </div>
      </el-col>
      
      <el-col :span="12">
        <!-- 成交量分析 -->
        <div class="card mb-20">
          <h2 class="section-title">成交量分析</h2>          <div v-loading="loading.volume" class="chart-container">
            <indicator-chart 
              v-if="indicatorData.volume && indicatorData.volume.length > 0"
              :data="indicatorData.volume"
              indicator-type="volume"
              :height="300"
              title="成交量分析"
            ></indicator-chart>
            <div v-else-if="indicatorData.volumeError" class="no-data-placeholder">{{ indicatorData.volumeError }}</div>
            <div v-else class="no-data-placeholder">加载中...</div>
          </div>
          
          <div class="indicator-description">
            <h4>成交量指标说明：</h4>
            <p>成交量是衡量市场活跃度和资金流入流出的重要指标。</p>
            <ul>
              <li>价格上涨时成交量增大，表明买方力量强势</li>
              <li>价格下跌时成交量增大，表明卖方力量强势</li>
              <li>成交量萎缩表示交投不活跃，可能酝酿行情转折</li>
              <li>成交量移动平均线可以更好地识别成交量趋势</li>
            </ul>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { ElMessage } from 'element-plus';
import { klineService, indicatorService } from '../api';
import { demoStockList, formatDate } from '../utils';
import KlineChart from '../components/KlineChart.vue';
import IndicatorChart from '../components/IndicatorChart.vue';

// 添加辅助函数，处理日期格式问题
const convertShortDate = (dateStr) => {
  // 直接使用工具函数中的formatDate，以保持一致性
  return formatDate(dateStr);
};

export default {
  name: 'KlineDetail',
  components: {
    KlineChart,
    IndicatorChart
  },
  props: {
    code: {
      type: String,
      required: false
    }
  },
  setup(props) {
    const router = useRouter();
    const route = useRoute();
    
    // 从路由参数获取股票代码
    const stockCode = ref(props.code || route.params.code);
    
    // 股票基本信息
    const stockInfo = ref({
      name: '加载中...',
      code: stockCode.value
    });
    
    // 获取股票名称
    const findStockInfo = () => {
      const stock = demoStockList.find(s => s.code === stockCode.value);
      if (stock) {
        stockInfo.value = stock;
      }
    };
    
    // 频率和日期选择
    const freqType = ref('D');
      // 设置默认日期范围：最近100天
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 100);
    console.log(startDate, formatDate(startDate, 'YYYY-MM-DD'));
    
    const dateRange = ref([
      formatDate(startDate, 'YYYY-MM-DD'),
      formatDate(endDate, 'YYYY-MM-DD')
    ]);
    
    // 数据
    const klineData = ref([]);
    const indicatorData = reactive({
      macd: [],
      kdj: [],
      rsi: [],
      volume: [],
      // 错误信息字段
      macdError: '',
      kdjError: '',
      rsiError: '',
      volumeError: ''
    });
    
    // 加载状态
    const loading = reactive({
      kline: false,
      macd: false,
      kdj: false,
      rsi: false,
      volume: false
    });
    
    // 返回首页
    const goBack = () => {
      router.push('/');
    };    // 获取K线数据
    const fetchKlineData = async () => {
      loading.kline = true;
      try {
        // 如果日期范围为空，设置默认范围
        // if (!dateRange.value || dateRange.value.length < 2 || !dateRange.value[0] || !dateRange.value[1]) {
        //   const now = new Date();
        //   const past = new Date();
        //   past.setDate(past.getDate() - 100);
        //   dateRange.value = [
        //     formatDate(past, 'YYYY-MM-DD'),
        //     formatDate(now, 'YYYY-MM-DD')
        //   ];
        // }
        
        const params = {
          freq: freqType.value,
          start_date: dateRange.value[0],
          end_date: dateRange.value[1],
          with_indicators: false  // 在单独的API中获取指标，以便复用
        };
        let result = await klineService.getKlineData(stockCode.value, params);
        
        // 判断返回数据格式并处理
        if (typeof result === 'string') {
          // 如果是字符串，检查是否包含 'NaN'，如果包含则替换并解析
          if (result.includes('NaN')) {
            result = JSON.parse(result.replaceAll('NaN', 'null'));
          } else {
            result = JSON.parse(result);
          }
        }
        // 如果已经是对象，则直接使用
        
        console.log(result);
        
        // 处理可能的日期格式问题
        const processedData = (result.kline_data || []).map(item => {
          if (item.date) {
            item.date = convertShortDate(item.date);
          }
          return item;
        });
        console.log('K线数据:', processedData);
        
        klineData.value = processedData;
      } catch (error) {
        console.error('获取K线数据失败:', error);
        ElMessage.error('获取K线数据失败，请稍后重试');
        klineData.value = [];
      } finally {
        loading.kline = false;
      }
    };
      // 获取MACD指标数据
    const fetchMACDData = async () => {
      loading.macd = true;
      try {
        const params = {
          start_date: dateRange.value[0],
          end_date: dateRange.value[1],
          freq: freqType.value
        };
        
        let result = await indicatorService.getMACD(stockCode.value, params);
        
        // 判断返回数据格式并处理
        if (typeof result === 'string') {
          // 如果是字符串，检查是否包含 'NaN'，如果包含则替换并解析
          if (result.includes('NaN')) {
            result = JSON.parse(result.replaceAll('NaN', 'null'));
          } else {
            result = JSON.parse(result);
          }
        }
        // 如果已经是对象，则直接使用
        
        // 检查是否有错误消息
        if (result.error) {
          indicatorData.macdError = result.error;
          indicatorData.macd = [];
          console.log('MACD指标错误:', result.error);
          return;
        }
        
        // 处理可能的日期格式问题
        const processedData = (result.data || []).map(item => {
          if (item.date) {
            item.date = convertShortDate(item.date);
          }
          return item;
        });
        console.log('MACD数据:', processedData);
        
        indicatorData.macd = processedData;
        indicatorData.macdError = ''; // 清除之前的错误
      } catch (error) {
        console.error('获取MACD指标数据失败:', error);
        ElMessage.error('获取MACD指标数据失败，请稍后重试');
        indicatorData.macd = [];
        indicatorData.macdError = '获取数据失败，请稍后重试';
      } finally {
        loading.macd = false;
      }
    };
      // 获取KDJ指标数据
    const fetchKDJData = async () => {
      loading.kdj = true;
      try {
        const params = {
          start_date: dateRange.value[0],
          end_date: dateRange.value[1],
          freq: freqType.value
        };
        
        let result = await indicatorService.getKDJ(stockCode.value, params);
        
        // 判断返回数据格式并处理
        if (typeof result === 'string') {
          // 如果是字符串，检查是否包含 'NaN'，如果包含则替换并解析
          if (result.includes('NaN')) {
            result = JSON.parse(result.replaceAll('NaN', 'null'));
          } else {
            result = JSON.parse(result);
          }
        }
        // 如果已经是对象，则直接使用
        
        // 检查是否有错误消息
        if (result.error) {
          indicatorData.kdjError = result.error;
          indicatorData.kdj = [];
          console.log('KDJ指标错误:', result.error);
          return;
        }
        
        // 处理可能的日期格式问题
        const processedData = (result.data || []).map(item => {
          if (item.date) {
            item.date = convertShortDate(item.date);
          }
          return item;
        });
        console.log('KDJ数据:', processedData);
        
        indicatorData.kdj = processedData;
        indicatorData.kdjError = ''; // 清除之前的错误
      } catch (error) {
        console.error('获取KDJ指标数据失败:', error);
        ElMessage.error('获取KDJ指标数据失败，请稍后重试');
        indicatorData.kdj = [];
        indicatorData.kdjError = '获取数据失败，请稍后重试';
      } finally {
        loading.kdj = false;
      }
    };
      // 获取RSI指标数据
    const fetchRSIData = async () => {
      loading.rsi = true;
      try {
        const params = {
          start_date: dateRange.value[0],
          end_date: dateRange.value[1],
          freq: freqType.value
        };
        
        let result = await indicatorService.getRSI(stockCode.value, params);
        
        // 判断返回数据格式并处理
        if (typeof result === 'string') {
          // 如果是字符串，检查是否包含 'NaN'，如果包含则替换并解析
          if (result.includes('NaN')) {
            result = JSON.parse(result.replaceAll('NaN', 'null'));
          } else {
            result = JSON.parse(result);
          }
        }
        // 如果已经是对象，则直接使用
        
        // 检查是否有错误消息
        if (result.error) {
          indicatorData.rsiError = result.error;
          indicatorData.rsi = [];
          console.log('RSI指标错误:', result.error);
          return;
        }
        
        // 处理可能的日期格式问题
        const processedData = (result.data || []).map(item => {
          if (item.date) {
            item.date = convertShortDate(item.date);
          }
          return item;
        });
        indicatorData.rsi = processedData;
        indicatorData.rsiError = ''; // 清除之前的错误
      } catch (error) {
        console.error('获取RSI指标数据失败:', error);
        ElMessage.error('获取RSI指标数据失败，请稍后重试');
        indicatorData.rsi = [];
        indicatorData.rsiError = '获取数据失败，请稍后重试';
      } finally {
        loading.rsi = false;
      }
    };
      // 获取成交量分析数据
    const fetchVolumeData = async () => {
      loading.volume = true;
      try {
        const params = {
          start_date: dateRange.value[0],
          end_date: dateRange.value[1],
          freq: freqType.value
        };
        
        let result = await indicatorService.getVolumeAnalysis(stockCode.value, params);
        
        // 判断返回数据格式并处理
        if (typeof result === 'string') {
          // 如果是字符串，检查是否包含 'NaN'，如果包含则替换并解析
          if (result.includes('NaN')) {
            result = JSON.parse(result.replaceAll('NaN', 'null'));
          } else {
            result = JSON.parse(result);
          }
        }
        // 如果已经是对象，则直接使用
        
        // 检查是否有错误消息
        if (result.error) {
          indicatorData.volumeError = result.error;
          indicatorData.volume = [];
          console.log('成交量分析错误:', result.error);
          return;
        }
        
        // 处理可能的日期格式问题
        const processedData = (result.data || []).map(item => {
          if (item.date) {
            item.date = convertShortDate(item.date);
          }
          return item;
        });
        indicatorData.volume = processedData;
        indicatorData.volumeError = ''; // 清除之前的错误
      } catch (error) {
        console.error('获取成交量分析数据失败:', error);
        ElMessage.error('获取成交量分析数据失败，请稍后重试');
        indicatorData.volume = [];
        indicatorData.volumeError = '获取数据失败，请稍后重试';
      } finally {
        loading.volume = false;
      }
    };
    
    // 获取所有数据
    const fetchAllData = () => {
      fetchKlineData();
      fetchMACDData();
      fetchKDJData();
      fetchRSIData();
      fetchVolumeData();
    };
    
    // K线图中显示的指标数据
    const chartIndicators = computed(() => {
      return {
        macd: indicatorData.macd,
        kdj: indicatorData.kdj
      };
    });
    
    // 组件挂载时，获取数据
    onMounted(() => {
      findStockInfo();
      fetchAllData();
    });
    
    return {
      stockCode,
      stockInfo,
      freqType,
      dateRange,
      klineData,
      indicatorData,
      loading,
      chartIndicators,
      goBack,
      fetchAllData
    };
  }
};
</script>

<style scoped>
.stock-detail-container {
  padding-bottom: 50px;
}

.section-title {
  font-size: 16px;
  margin-bottom: 15px;
}

.no-data-placeholder {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #909399;
  font-size: 14px;
  background: #f5f7fa;
  border-radius: 4px;
  padding: 0 20px;
  text-align: center;
  word-break: break-word;
}

.indicator-description {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #ebeef5;
  font-size: 14px;
  color: #606266;
}

.indicator-description h4 {
  margin-bottom: 8px;
  color: #303133;
}

.indicator-description ul {
  padding-left: 20px;
  margin-top: 5px;
}

.indicator-description li {
  margin-bottom: 5px;
}
</style>
