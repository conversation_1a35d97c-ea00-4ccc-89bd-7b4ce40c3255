import { createRouter, createWebHistory } from 'vue-router';

// 导入视图组件
import Dashboard from '../views/Dashboard.vue';
import KlineDetail from '../views/KlineDetail.vue';

const routes = [
  {
    path: '/',
    name: 'Dashboard',
    component: Dashboard,
    meta: { title: '量化分析系统' }
  },
  {
    path: '/stock/:code',
    name: 'KlineDetail',
    component: KlineDetail,
    props: true,
    meta: { title: '股票详情分析' }
  }
];

const router = createRouter({
  history: createWebHistory(),
  routes
});

// 设置页面标题
router.beforeEach((to, from, next) => {
  document.title = to.meta.title || '量化分析系统演示';
  next();
});

export default router;
