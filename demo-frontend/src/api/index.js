import axios from 'axios';

// 创建axios实例
const api = axios.create({
  baseURL: '/api', // 使用vite.config.js中配置的代理
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  }
});

// 请求拦截器
api.interceptors.request.use(
  config => {
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  response => {
    return response.data;
  },
  error => {
    return Promise.reject(error);
  }
);

// K线服务
export const klineService = {
  // 获取K线数据
  getKlineData(stockCode, params = {}) {
    return api.get(`/analytics/kline/${stockCode}`, { params });
  }
};

// 技术指标服务
export const indicatorService = {
  // 获取MACD指标
  getMACD(stockCode, params = {}) {
    return api.get(`/indicators/macd/${stockCode}`, { params });
  },

  // 获取KDJ指标
  getKDJ(stockCode, params = {}) {
    return api.get(`/indicators/kdj/${stockCode}`, { params });
  },

  // 获取RSI指标
  getRSI(stockCode, params = {}) {
    return api.get(`/indicators/rsi/${stockCode}`, { params });
  },

  // 获取ARBR指标
  getARBR(stockCode, params = {}) {
    return api.get(`/indicators/arbr/${stockCode}`, { params });
  },

  // 获取成交量分析
  getVolumeAnalysis(stockCode, params = {}) {
    return api.get(`/indicators/volume/${stockCode}`, { params });
  }
};

// 高级技术指标服务
export const advancedIndicatorService = {
  // 获取完整的高级技术指标
  getCompleteIndicators(stockCode, params = {}) {
    return api.get(`/advanced-indicators/complete/${stockCode}`, { params });
  },

  // 获取Bollinger Bands
  getBollingerBands(stockCode, params = {}) {
    return api.get(`/advanced-indicators/bollinger/${stockCode}`, { params });
  },

  // 获取成交量内外盘分析
  getVolumeInOutAnalysis(stockCode, params = {}) {
    return api.get(`/advanced-indicators/volume-analysis/${stockCode}`, { params });
  },

  // 获取成交量Bollinger指标
  getVolumeBollinger(stockCode, params = {}) {
    return api.get(`/advanced-indicators/volume-bollinger/${stockCode}`, { params });
  },

  // 获取增强版KDJ指标
  getKDJEnhanced(stockCode, params = {}) {
    return api.get(`/advanced-indicators/kdj-enhanced/${stockCode}`, { params });
  },

  // 获取买卖点交易信号
  getTradingSignals(stockCode, params = {}) {
    return api.get(`/advanced-indicators/trading-signals/${stockCode}`, { params });
  },

  // 获取支持的数据周期
  getAvailablePeriods() {
    return api.get('/advanced-indicators/periods');
  }
};

export default {
  klineService,
  indicatorService,
  advancedIndicatorService
};
