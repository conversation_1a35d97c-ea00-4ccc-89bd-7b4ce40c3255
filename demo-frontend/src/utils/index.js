/**
 * 格式化日期
 * @param {Date|string} date 日期对象或日期字符串
 * @param {string} fmt 格式模板，例如: 'YYYY-MM-DD'
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(date, fmt = 'YYYY-MM-DD') {
  if (!date) return '';
  
  if (typeof date === 'string') {
    // 处理ISO格式
    if (date.includes('T')) {
      // 从ISO日期中提取日期部分
      const datePartOnly = date.split('T')[0];
      // 如果使用默认格式且已经是YYYY-MM-DD形式，直接返回
      if (fmt === 'YYYY-MM-DD' && /^\d{4}-\d{2}-\d{2}$/.test(datePartOnly)) {
        return datePartOnly;
      }
      // 否则继续处理
      date = datePartOnly;
    }
    // 处理短日期格式 YY-MM-DD
    else if (/^\d{2}-\d{2}-\d{2}$/.test(date)) {
      const [year, month, day] = date.split('-');
      return `20${year}-${month}-${day}`;
    }
    
    // 如果经过上面处理的字符串是有效的日期格式，才创建Date对象
    if (/^\d{4}-\d{2}-\d{2}$/.test(date)) {
      date = new Date(date);
    } else {
      // 尝试常规Date解析
      const tempDate = new Date(date);
      if (!isNaN(tempDate.getTime())) {
        date = tempDate;
      } else {
        console.error('无法解析日期:', date);
        return '';
      }
    }
  }
  
  if (isNaN(date.getTime())) {
    console.error('Invalid date:', date);
    return '';
  }
  
  const year = date.getFullYear().toString();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  const seconds = date.getSeconds().toString().padStart(2, '0');
  
  return fmt
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds);
}

/**
 * 获取涨跌色
 * @param {number} value 变化值
 * @returns {string} 颜色类名
 */
export function getPriceColorClass(value) {
  if (value > 0) return 'stock-up';
  if (value < 0) return 'stock-down';
  return '';
}

/**
 * 计算涨跌幅
 * @param {number} current 当前价格
 * @param {number} previous 前一个价格
 * @returns {string} 带百分号的涨跌幅字符串
 */
export function getChangePercent(current, previous) {
  if (!current || !previous) return '0.00%';
  const percent = (current - previous) * 100 / previous;
  return percent.toFixed(2) + '%';
}

/**
 * 格式化数字（保留小数点）
 * @param {number} num 需要格式化的数字
 * @param {number} digits 小数点后位数
 * @returns {string} 格式化后的数字字符串
 */
export function formatNumber(num, digits = 2) {
  if (isNaN(num) || num === null || num === undefined) return '--';
  return parseFloat(num).toFixed(digits);
}

/**
 * 随机股票代码列表（演示用）
 */
export const demoStockList = [
  { code: '000001', name: '平安银行' },
  { code: '000300', name: '沪深300' },
  { code: '000651', name: '格力电器' },
  { code: '000858', name: '五粮液' },
  { code: '600036', name: '招商银行' },
  { code: '600519', name: '贵州茅台' },
  { code: '601318', name: '中国平安' },
  { code: '601857', name: '中国石油' },
  { code: '603288', name: '海天味业' },
];
