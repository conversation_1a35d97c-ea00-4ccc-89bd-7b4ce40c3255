<template>
  <div class="indicator-chart">
    <e-chart-component :options="chartOptions" :height="height"></e-chart-component>
  </div>
</template>

<script>
import { computed } from 'vue';
import EChartComponent from './EChartComponent.vue';
import { formatDate } from '../utils';

export default {
  name: 'IndicatorChart',
  components: {
    EChartComponent
  },
  props: {
    data: {
      type: Array,
      required: true
    },
    indicatorType: {
      type: String,
      required: true,
      validator: (value) => ['macd', 'kdj', 'rsi', 'arbr', 'volume'].includes(value)
    },
    title: {
      type: String,
      default: ''
    },
    height: {
      type: Number,
      default: 300
    }
  },
  setup(props) {
    // 计算图表配置
    const chartOptions = computed(() => {
      if (!props.data || props.data.length === 0) {
        return {
          title: {
            text: '暂无数据',
            textStyle: {
              color: '#666',
              fontSize: 14
            },
            left: 'center',
            top: 'center'
          }
        };
      }
      
      // 基础配置
      const baseOptions = {
        animation: false,
        title: {
          text: props.title,
          left: 10,
          textStyle: {
            color: '#333',
            fontSize: 14
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          }
        },
        legend: {
          top: 10,
          right: 10
        },
        grid: {
          left: 50,
          right: 50,
          top: 50,
          bottom: 30
        },        xAxis: {
          type: 'category',
          data: props.data.map(item => {
            // 如果是ISO格式日期，提取日期部分
            if (typeof item.date === 'string' && item.date.includes('T')) {
              return item.date.split('T')[0];
            }
            return formatDate(item.date);
          }),
          scale: true,
          boundaryGap: false,
          axisLine: { lineStyle: { color: '#8392A5' } },
          splitLine: { show: false }
        },
        yAxis: {
          scale: true,
          splitLine: { show: true }
        },
        dataZoom: [
          { 
            type: 'inside',
            start: Math.max(0, 100 - 1200 / props.data.length * 100),
            end: 100
          },
          { 
            type: 'slider',
            start: Math.max(0, 100 - 1200 / props.data.length * 100),
            end: 100,
            bottom: 0
          }
        ],
        series: []
      };
        // 根据指标类型设置不同的系列
      switch (props.indicatorType) {
        case 'macd':
          // 动态查找 MACD 相关字段
          const macdKeys = Object.keys(props.data[0]).filter(key => 
            key.startsWith('MACD') || key.toLowerCase().includes('macd')
          );
          
          // 确定各个MACD指标的字段名
          let diffKey = macdKeys.find(key => key === 'MACD_12_26_9' || key.includes('MACD_') || key === 'diff');
          let deaKey = macdKeys.find(key => key === 'MACDs_12_26_9' || key.includes('MACDs_') || key === 'dea');
          let macdKey = macdKeys.find(key => key === 'MACDh_12_26_9' || key.includes('MACDh_') || key === 'macd');
          
          // 如果没找到匹配的键，使用备选方案
          if (!diffKey || !deaKey || !macdKey) {
            console.warn('MACD字段名不匹配，尝试使用备选命名');
            if (macdKeys.length >= 3) {
              diffKey = macdKeys[0];
              deaKey = macdKeys[1];
              macdKey = macdKeys[2];
            } else {
              diffKey = 'diff';
              deaKey = 'dea';
              macdKey = 'macd';
            }
          }
          
          console.log('MACD字段映射:', { diffKey, deaKey, macdKey });
          
          baseOptions.legend.data = ['DIFF', 'DEA', 'MACD'];
          baseOptions.series = [
            {
              name: 'DIFF',
              type: 'line',
              data: props.data.map(item => item[diffKey]),
              lineStyle: { normal: { color: '#da6ee8' } },
              smooth: true
            },
            {
              name: 'DEA',
              type: 'line',
              data: props.data.map(item => item[deaKey]),
              lineStyle: { normal: { color: '#39afe6' } },
              smooth: true
            },
            {
              name: 'MACD',
              type: 'bar',
              data: props.data.map(item => item[macdKey]),
              itemStyle: {
                normal: {
                  color: function(params) {
                    return params.data >= 0 ? '#c23531' : '#91cc75';
                  }
                }
              }
            }
          ];
          break;
          
        case 'kdj':
          baseOptions.legend.data = ['K', 'D', 'J'];
          baseOptions.series = [
            {
              name: 'K',
              type: 'line',
              data: props.data.map(item => item.K), // 修改为大写K
              lineStyle: { normal: { color: '#39afe6' } },
              smooth: true
            },
            {
              name: 'D',
              type: 'line',
              data: props.data.map(item => item.D), // 修改为大写D
              lineStyle: { normal: { color: '#da6ee8' } },
              smooth: true
            },
            {
              name: 'J',
              type: 'line',
              data: props.data.map(item => item.J), // 修改为大写J
              lineStyle: { normal: { color: '#c23531' } },
              smooth: true
            }
          ];
          
          // 添加超买超卖区域
          baseOptions.markLine = {
            data: [
              { yAxis: 80, lineStyle: { color: '#c23531' } },
              { yAxis: 20, lineStyle: { color: '#91cc75' } }
            ]
          };
          break;
          
        case 'rsi':
          const periods = Object.keys(props.data[0]).filter(key => key !== 'date');
          baseOptions.legend.data = periods;
          
          baseOptions.series = periods.map((period, index) => {
            const colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de'];
            return {
              name: period,
              type: 'line',
              data: props.data.map(item => item[period]),
              lineStyle: { normal: { color: colors[index % colors.length] } },
              smooth: true
            };
          });
          
          // 添加超买超卖区域
          baseOptions.markLine = {
            data: [
              { yAxis: 80, lineStyle: { color: '#c23531' } },
              { yAxis: 20, lineStyle: { color: '#91cc75' } }
            ]
          };
          break;
          
        case 'arbr':
          baseOptions.legend.data = ['AR', 'BR'];
          baseOptions.series = [
            {
              name: 'AR',
              type: 'line',
              data: props.data.map(item => item.ar),
              lineStyle: { normal: { color: '#5470c6' } },
              smooth: true
            },
            {
              name: 'BR',
              type: 'line',
              data: props.data.map(item => item.br),
              lineStyle: { normal: { color: '#91cc75' } },
              smooth: true
            }
          ];
          
          // 添加参考线
          baseOptions.markLine = {
            data: [
              { yAxis: 150, lineStyle: { color: '#c23531' } },
              { yAxis: 100, lineStyle: { color: '#91cc75' } },
              { yAxis: 50, lineStyle: { color: '#c23531' } }
            ]
          };
          break;
          
        case 'volume':
          const maKeys = Object.keys(props.data[0]).filter(key => key !== 'date' && key !== 'volume');
          baseOptions.legend.data = ['Volume', ...maKeys];
          
          // 成交量柱状图
          const volumeSeries = {
            name: 'Volume',
            type: 'bar',
            data: props.data.map(item => item.volume),
            itemStyle: {
              color: (params) => {
                const dataIndex = params.dataIndex;
                if (dataIndex > 0 && props.data[dataIndex].close) {
                  return props.data[dataIndex].close >= props.data[dataIndex - 1].close ? '#c23531' : '#91cc75';
                }
                return '#c23531';
              }
            }
          };
          
          // 移动平均线
          const maSeries = maKeys.map((key, index) => {
            const colors = ['#5470c6', '#91cc75', '#fac858'];
            return {
              name: key,
              type: 'line',
              data: props.data.map(item => item[key]),
              lineStyle: { normal: { color: colors[index % colors.length] } },
              smooth: true
            };
          });
          
          baseOptions.series = [volumeSeries, ...maSeries];
          break;
      }
      
      return baseOptions;
    });

    return {
      chartOptions
    };
  }
};
</script>
