<template>
  <div ref="chartContainer" :style="{ height: height + 'px', width: '100%' }"></div>
</template>

<script>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';
import * as echarts from 'echarts';

export default {
  name: 'EChartComponent',
  props: {
    options: {
      type: Object,
      required: true
    },
    height: {
      type: Number,
      default: 400
    },
    autoResize: {
      type: Boolean,
      default: true
    }
  },
  setup(props, { expose }) {
    const chartContainer = ref(null);
    let chart = null;

    // 初始化图表
    const initChart = () => {
      if (chartContainer.value) {
        chart = echarts.init(chartContainer.value);
        chart.setOption(props.options);
      }
    };

    // 更新图表选项
    const updateOptions = () => {
      if (chart) {
        chart.setOption(props.options, true);
      }
    };

    // 重置图表尺寸
    const resizeChart = () => {
      if (chart) {
        chart.resize();
      }
    };

    // 监听窗口尺寸变化
    const addResizeListener = () => {
      window.addEventListener('resize', resizeChart);
    };

    // 移除窗口尺寸监听
    const removeResizeListener = () => {
      window.removeEventListener('resize', resizeChart);
    };

    // 销毁图表
    const disposeChart = () => {
      if (!chart) {
        return;
      }
      removeResizeListener();
      chart.dispose();
      chart = null;
    };

    onMounted(() => {
      initChart();
      if (props.autoResize) {
        addResizeListener();
      }
    });

    onBeforeUnmount(() => {
      disposeChart();
    });

    watch(() => props.options, () => {
      updateOptions();
    }, { deep: true });

    // 对外暴露的方法
    expose({
      resize: resizeChart,
      dispose: disposeChart
    });

    return {
      chartContainer
    };
  }
};
</script>
