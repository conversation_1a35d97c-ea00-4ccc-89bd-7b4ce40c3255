<template>
  <div class="kline-chart-container">
    <e-chart-component :options="chartOptions" :height="height"></e-chart-component>
  </div>
</template>

<script>
import { ref, computed, watch, onMounted } from 'vue';
import EChartComponent from './EChartComponent.vue';
import { formatDate } from '../utils';

export default {
  name: 'KlineChart',
  components: {
    EChartComponent
  },
  props: {
    data: {
      type: Array,
      required: true
    },
    height: {
      type: Number,
      default: 400
    },
    title: {
      type: String,
      default: ''
    },
    showVolume: {
      type: Boolean,
      default: true
    },
    indicators: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props) {
    // 计算图表配置
    const chartOptions = computed(() => {
      if (!props.data || props.data.length === 0) {
        return {
          title: {
            text: '暂无数据',
            textStyle: {
              color: '#666',
              fontSize: 14
            },
            left: 'center',
            top: 'center'
          }
        };
      }      // 构建图表配置
      const dates = props.data.map(item => {
        // 使用已加强的formatDate函数处理各种日期格式
        return formatDate(item.date);
      });
      const volumes = props.data.map(item => item.volume);
      
      // K线图数据格式: [open, close, low, high]
      const candleData = props.data.map(item => [item.open, item.close, item.low, item.high]);

      // 格式化浮点数
      const formatCandleData = candleData.map(item => item.map(val => parseFloat(val)));

      // 基本配置
      const options = {
        animation: false,
        title: {
          text: props.title,
          left: 10,
          textStyle: {
            color: '#333',
            fontSize: 16
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          formatter: function(params) {
            const dataIndex = params[0].dataIndex;
            const item = props.data[dataIndex];
            
            let result = `<div style="font-weight:bold;margin-bottom:5px;">${formatDate(item.date)}</div>`;
            result += `开盘价: ${item.open}<br/>`;
            result += `收盘价: ${item.close}<br/>`;
            result += `最高价: ${item.high}<br/>`;
            result += `最低价: ${item.low}<br/>`;
            result += `成交量: ${item.volume}<br/>`;
              // 添加技术指标信息
            if (props.indicators && props.indicators.macd && props.indicators.macd[dataIndex]) {
              const macd = props.indicators.macd[dataIndex];
              result += `<br/><b>MACD:</b><br/>`;
              result += `DIFF: ${macd.diff !== undefined && macd.diff !== null ? macd.diff.toFixed(4) : 'N/A'}<br/>`;
              result += `DEA: ${macd.dea !== undefined && macd.dea !== null ? macd.dea.toFixed(4) : 'N/A'}<br/>`;
              result += `MACD: ${macd.macd !== undefined && macd.macd !== null ? macd.macd.toFixed(4) : 'N/A'}<br/>`;
            }
            
            if (props.indicators && props.indicators.kdj && props.indicators.kdj[dataIndex]) {
              const kdj = props.indicators.kdj[dataIndex];
              result += `<br/><b>KDJ:</b><br/>`;
              result += `K: ${kdj.k !== undefined && kdj.k !== null ? kdj.k.toFixed(2) : 'N/A'}<br/>`;
              result += `D: ${kdj.d !== undefined && kdj.d !== null ? kdj.d.toFixed(2) : 'N/A'}<br/>`;
              result += `J: ${kdj.j !== undefined && kdj.j !== null ? kdj.j.toFixed(2) : 'N/A'}<br/>`;
            }
            
            return result;
          }
        },
        legend: {
          data: ['K线', '成交量'],
          top: 10,
          right: 10
        },
        axisPointer: {
          link: { xAxisIndex: 'all' }
        },
        grid: props.showVolume 
          ? [
              { left: 50, right: 50, top: '10%', height: '60%' },
              { left: 50, right: 50, top: '75%', height: '15%' }
            ]
          : [{ left: 50, right: 50, top: '10%', bottom: '10%' }],
        xAxis: props.showVolume 
          ? [
              { 
                type: 'category', 
                data: dates,
                axisLine: { lineStyle: { color: '#8392A5' } },
                scale: true,
                boundaryGap: false,
                axisLabel: { show: false },
                splitLine: { show: false }
              },
              { 
                type: 'category', 
                data: dates,
                gridIndex: 1,
                scale: true,
                boundaryGap: false,
                axisLine: { lineStyle: { color: '#8392A5' } },
                axisLabel: { show: true },
                splitLine: { show: false }
              }
            ]
          : [{ 
              type: 'category', 
              data: dates,
              scale: true,
              boundaryGap: false,
              axisLine: { lineStyle: { color: '#8392A5' } },
              axisLabel: { show: true },
              splitLine: { show: false }
            }],
        yAxis: props.showVolume 
          ? [
              { 
                scale: true,
                splitNumber: 6,
                axisLine: { lineStyle: { color: '#8392A5' } },
                splitLine: { show: true }
              },
              { 
                scale: true,
                gridIndex: 1,
                splitNumber: 2,
                axisLine: { lineStyle: { color: '#8392A5' } },
                axisLabel: { show: true },
                splitLine: { show: false }
              }
            ]
          : [{ 
              scale: true,
              splitNumber: 6,
              axisLine: { lineStyle: { color: '#8392A5' } },
              splitLine: { show: true }
            }],
        dataZoom: [
          { 
            type: 'inside', 
            xAxisIndex: [0, 1], 
            start: Math.max(0, 100 - 1200 / props.data.length * 100),
            end: 100
          },
          { 
            type: 'slider', 
            xAxisIndex: [0, 1], 
            start: Math.max(0, 100 - 1200 / props.data.length * 100),
            end: 100,
            bottom: 0
          }
        ],
        series: [
          {
            name: 'K线',
            type: 'candlestick',
            data: formatCandleData,
            itemStyle: {
              color: '#c23531',
              color0: '#91cc75',
              borderColor: '#c23531',
              borderColor0: '#91cc75'
            }
          }
        ]
      };

      // 添加成交量
      if (props.showVolume) {
        options.series.push({
          name: '成交量',
          type: 'bar',
          xAxisIndex: 1,
          yAxisIndex: 1,
          data: volumes,
          itemStyle: {
            color: (params) => {
              const dataIndex = params.dataIndex;
              // 根据当天收盘价相对前一天的涨跌设置颜色
              if (dataIndex > 0) {
                return props.data[dataIndex].close >= props.data[dataIndex - 1].close ? '#c23531' : '#91cc75';
              }
              return '#c23531'; // 第一天默认红色
            }
          }
        });
      }

      return options;
    });

    // 添加对data属性的深度监听，确保数据变化时图表能够更新
    watch(() => props.data, (newData, oldData) => {
      // 当data发生变化时，由于chartOptions是computed属性，
      // 它会自动重新计算，但这里我们可以添加一些额外的逻辑
      // 比如数据变化时的动画或其他需要在数据更新时执行的操作
      console.log('K线图数据已更新', newData.length);
      
      // 如果需要在图表更新后执行一些操作，可以在这里添加
    }, { deep: true }); // deep: true 确保深度监听数组内的对象变化

    return {
      chartOptions
    };
  }
};
</script>
