# 量化分析系统演示前端

这是一个简单的量化分析系统前端演示项目，用于展示量化分析后端API的功能和数据可视化效果。界面简洁直观，功能一目了然。

## 功能特点

该演示系统主要包含以下功能：

1. **股票K线数据展示**
   - 支持日K/周K/月线切换
   - 可调整显示时间范围
   - K线与成交量联动显示

2. **技术指标分析**
   - MACD指标（差离值/信号线/柱状图）
   - KDJ指标（超买超卖指标）
   - RSI指标（相对强弱指标）
   - 成交量分析

3. **便捷操作**
   - 股票快速搜索
   - 日期范围选择
   - 图表放大缩小
   - 数据缓存加速

## 页面说明

1. **首页/仪表盘**
   - 股票选择面板
   - 当前选中股票K线展示
   - 可选单个技术指标显示

2. **股票详情页**
   - 完整K线数据
   - 全部技术指标面板
   - 每个指标的详细说明

## 使用方法

1. 在首页选择或搜索想要查看的股票
2. 选择时间范围和K线周期（日线/周线/月线）
3. 选择需要显示的技术指标
4. 点击"查看完整分析"进入详情页查看全部指标

## 部署说明

本前端项目运行在本地开发环境中:
http://localhost:3000/

实际部署时，可以将构建后的文件部署到任何静态文件服务器中。

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 预览构建后的版本
npm run preview
```
