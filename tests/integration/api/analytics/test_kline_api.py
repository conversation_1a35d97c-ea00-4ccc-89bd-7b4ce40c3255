import pytest
from fastapi.testclient import TestClient
from unittest.mock import AsyncMock, MagicMock
from datetime import date, timedelta
import pandas as pd

# 假设你的 FastAPI app 实例在 app.main 模块中
# 如果不在，请修改为正确的导入路径
from app.main import app 
from app.services.analytics.kline_service import KlineAnalysisService
from app.api.endpoints.analytics.kline import get_kline_service

# --- Fixture for Test Client with Dependency Override ---
@pytest.fixture(scope="module") # Use module scope for efficiency
def test_client():
    # Apply dependency override before creating the client
    app.dependency_overrides[get_kline_service] = override_get_kline_service
    client = TestClient(app)
    yield client # Provide the client to tests
    # Cleanup after tests in this module are done
    app.dependency_overrides.clear()


# --- 模拟数据 ---
MOCK_STOCK_CODE = "000001.SZ"
MOCK_START_DATE = (date.today() - timedelta(days=5)).isoformat()
MOCK_END_DATE = date.today().isoformat()

# 模拟 KlineAnalysisService 返回的 K 线数据点
MOCK_KLINE_POINTS_SIMPLE = [
    {"date": "2024-01-01", "open": 10.0, "high": 10.5, "low": 9.8, "close": 10.2, "volume": 10000},
    {"date": "2024-01-02", "open": 10.2, "high": 10.8, "low": 10.1, "close": 10.7, "volume": 12000},
]

# 模拟指标数据 (仅用于结构示例)
MOCK_INDICATORS_DATA = {
    "macd": [{"date": "2024-01-01", "dif": 0.1}, {"date": "2024-01-02", "dif": 0.15}],
    "kdj": [{"date": "2024-01-01", "k": 50}, {"date": "2024-01-02", "k": 60}]
}

# 模拟统计数据 (仅用于结构示例)
MOCK_STATS = {"avg_volume": 11000, "max_price": 10.8}

# --- 模拟服务 ---
# 创建一个异步 Mock 类来模拟 KlineAnalysisService
mock_kline_service = AsyncMock(spec=KlineAnalysisService)

# 定义模拟方法的返回值
async def mock_get_kline_data(stock_code, freq, start_date, end_date, with_indicators, indicators):
    """模拟 KlineAnalysisService.get_kline_data 的行为"""
    
    # 构建基础返回结构
    result = {
        "stock_code": stock_code,
        "freq": freq,
        "start_date": MOCK_KLINE_POINTS_SIMPLE[0]['date'], # Use mock data for consistency
        "end_date": MOCK_KLINE_POINTS_SIMPLE[-1]['date'],
        "data_count": len(MOCK_KLINE_POINTS_SIMPLE),
        "statistics": MOCK_STATS, # Add mock stats
        "kline_data": MOCK_KLINE_POINTS_SIMPLE # Use the simple points list
    }
    
    # 如果请求了指标，添加模拟指标数据
    if with_indicators and indicators:
        result["indicators"] = {ind: MOCK_INDICATORS_DATA.get(ind, []) for ind in indicators}
        # 注意：为了简化，这里直接使用了 MOCK_KLINE_POINTS_SIMPLE 作为 kline_data
        # 实际应用中，带指标的数据点可能不同，但对于测试结构是足够的
        
    return result

# 使用 side_effect 来指定调用 mock 方法时执行的函数
# 这样 mock_kline_service.get_kline_data 仍然是一个 mock 对象
mock_kline_service.get_kline_data.side_effect = mock_get_kline_data

# --- 依赖覆盖 ---
async def override_get_kline_service():
    """覆盖原始的依赖注入函数，返回 mock 实例"""
    return mock_kline_service

# 在测试开始前应用依赖覆盖
app.dependency_overrides[get_kline_service] = override_get_kline_service


# --- 测试用例 ---
def test_get_kline_data_success_simple(test_client): # Use the fixture
    """测试成功获取基本 K 线数据 (不含指标)"""
    mock_kline_service.reset_mock() # Reset mock before the test
    response = test_client.get( # Use test_client from fixture
        f"/api/v1/analytics/kline/{MOCK_STOCK_CODE}",
        params={
            "freq": "D",
            "start_date": MOCK_START_DATE,
            "end_date": MOCK_END_DATE,
            "with_indicators": False # 明确指定不获取指标
        }
    )
    assert response.status_code == 200
    # 验证返回的完整结构
    response_data = response.json()
    assert response_data["stock_code"] == MOCK_STOCK_CODE
    assert response_data["freq"] == "D"
    assert response_data["kline_data"] == MOCK_KLINE_POINTS_SIMPLE # Check the kline data list
    assert "statistics" in response_data # Check if stats exist
    assert "indicators" not in response_data # Should not have indicators
    # 验证 mock 服务是否被正确调用
    mock_kline_service.get_kline_data.assert_called_once_with(
        MOCK_STOCK_CODE, "D", MOCK_START_DATE, MOCK_END_DATE, False, [] # Endpoint converts missing indicators to []
    )
    # No need for reset_mock() here, it's done at the start of each test

def test_get_kline_data_success_with_indicators(test_client): # Use the fixture
    """测试成功获取带技术指标的 K 线数据"""
    mock_kline_service.reset_mock() # Reset mock before the test
    indicators_to_request = ["macd", "kdj"]
    response = test_client.get( # Use test_client from fixture
        f"/api/v1/analytics/kline/{MOCK_STOCK_CODE}",
        params={
            "freq": "W", # 测试不同周期
            "start_date": MOCK_START_DATE,
            "end_date": MOCK_END_DATE,
            "with_indicators": True,
            "indicators": indicators_to_request # 传递指标列表
        }
    )
    assert response.status_code == 200
    # 验证返回的完整结构
    response_data = response.json()
    assert response_data["stock_code"] == MOCK_STOCK_CODE
    assert response_data["freq"] == "W"
    assert response_data["kline_data"] == MOCK_KLINE_POINTS_SIMPLE # Still use simple points for base
    assert "statistics" in response_data
    assert "indicators" in response_data
    assert "macd" in response_data["indicators"] # Check requested indicators exist
    assert "kdj" in response_data["indicators"]
    # 验证 mock 服务是否被正确调用
    mock_kline_service.get_kline_data.assert_called_once_with(
        MOCK_STOCK_CODE, "W", MOCK_START_DATE, MOCK_END_DATE, True, indicators_to_request
    )
    # No need for reset_mock() here

def test_get_kline_data_default_dates(test_client): # Use the fixture
    """测试使用默认日期获取 K 线数据"""
    mock_kline_service.reset_mock() # Reset mock before the test
    response = test_client.get( # Use test_client from fixture
        f"/api/v1/analytics/kline/{MOCK_STOCK_CODE}",
        params={"freq": "D"} # 只提供频率，使用默认日期
    )
    assert response.status_code == 200
    # 验证返回的完整结构
    response_data = response.json()
    assert response_data["stock_code"] == MOCK_STOCK_CODE
    assert response_data["freq"] == "D"
    assert response_data["kline_data"] == MOCK_KLINE_POINTS_SIMPLE # Check the kline data list
    assert "statistics" in response_data # Check if stats exist
    assert "indicators" not in response_data # Should not have indicators

    # 验证调用时传递了计算出的默认日期和空指标列表
    # 注意：这里需要知道默认日期的计算逻辑才能精确验证，或者只验证部分参数
    mock_kline_service.get_kline_data.assert_called_once()
    call_args, call_kwargs = mock_kline_service.get_kline_data.call_args
    assert call_args[0] == MOCK_STOCK_CODE
    assert call_args[1] == "D"
    # 验证 start_date 和 end_date 是否是符合格式的字符串 (具体值依赖当天日期)
    assert isinstance(call_args[2], str)
    assert isinstance(call_args[3], str)
    assert call_args[4] is False # with_indicators 默认为 False
    assert call_args[5] == [] # Endpoint converts missing indicators to []
    
    # No need for reset_mock() here

def test_get_kline_data_invalid_freq(test_client): # Use the fixture
    """测试无效的频率参数"""
    mock_kline_service.reset_mock() # Reset mock before the test
    response = test_client.get( # Use test_client from fixture
        f"/api/v1/analytics/kline/{MOCK_STOCK_CODE}",
        params={"freq": "X"} # 无效频率
    )
    # FastAPI 的 Query 参数验证会返回 422 Unprocessable Entity
    assert response.status_code == 422 
    # 验证 mock 服务未被调用
    mock_kline_service.get_kline_data.assert_not_called()

# --- 清理依赖覆盖 (Now handled by the test_client fixture) ---
# The cleanup_dependency_override fixture is no longer needed
# as the test_client fixture handles setup and teardown.
