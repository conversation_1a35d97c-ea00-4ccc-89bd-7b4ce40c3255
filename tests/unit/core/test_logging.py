import logging
import os
import shutil
import pytest
from datetime import datetime
from pathlib import Path
from app.core.logging import setup_logging, getLogger
from app.core.config import settings

@pytest.fixture
def cleanup_logs():
    """清理测试产生的日志文件"""
    yield
    # 使用shutil.rmtree强制删除logs目录及其内容
    log_dir = Path(settings.LOG_DIR)
    # 确保logs目录存在
    if log_dir.exists():
        shutil.rmtree(log_dir, ignore_errors=True)

@pytest.fixture
def clean_handlers():
    """清理所有日志处理器"""
    root_logger = logging.getLogger()
    original_handlers = root_logger.handlers[:]
    yield
    # 恢复原始处理器
    root_logger.handlers = original_handlers

def test_setup_logging_development(monkeypatch, clean_handlers):
    """测试开发环境下的日志配置"""
    # 模拟开发环境
    monkeypatch.setattr(settings, "APP_ENV", "development")
    monkeypatch.setattr(settings, "DEBUG", True)
    
    # 清理现有处理器
    root_logger = logging.getLogger()
    root_logger.handlers.clear()
    
    # 配置日志
    setup_logging()
    
    # 验证日志级别
    assert root_logger.level == logging.DEBUG
    
    # 验证是否有控制台处理器
    console_handlers = [h for h in root_logger.handlers if isinstance(h, logging.StreamHandler)]
    assert len(console_handlers) >= 1
    
    # 验证没有文件处理器
    file_handlers = [h for h in root_logger.handlers if isinstance(h, logging.FileHandler)]
    assert len(file_handlers) == 0

def test_setup_logging_production(monkeypatch, clean_handlers, cleanup_logs):
    """测试生产环境下的日志配置"""
    # 模拟生产环境
    monkeypatch.setattr(settings, "APP_ENV", "production")
    monkeypatch.setattr(settings, "DEBUG", False)
    
    # 清理现有处理器
    root_logger = logging.getLogger()
    root_logger.handlers.clear()
    
    # 配置日志
    setup_logging()
    
    # 验证日志级别
    assert root_logger.level == logging.INFO
    
    # 验证处理器类型
    console_handlers = [h for h in root_logger.handlers if isinstance(h, logging.StreamHandler)]
    file_handlers = [h for h in root_logger.handlers if isinstance(h, logging.FileHandler)]
    
    assert len(console_handlers) >= 1  # 至少有一个控制台处理器
    assert len(file_handlers) >= 1  # 至少有一个文件处理器
    
    # 验证日志文件是否创建
    log_file = Path("logs") / f"{settings.APP_NAME.lower().replace(' ', '_')}_{datetime.now().strftime('%Y-%m-%d')}.log"
    assert log_file.exists()

def test_setup_logging_custom_level(clean_handlers):
    """测试自定义日志级别"""
    # 清理现有处理器
    root_logger = logging.getLogger()
    root_logger.handlers.clear()
    
    # 配置自定义日志级别
    setup_logging(log_level="ERROR")
    
    # 验证日志级别
    assert root_logger.level == logging.ERROR

def test_module_specific_logging(clean_handlers):
    """测试特定模块的日志级别"""
    setup_logging()
    
    # 验证特定模块的日志级别
    assert logging.getLogger("sqlalchemy.engine").level == logging.WARNING
    assert logging.getLogger("uvicorn.access").level == logging.WARNING
    assert logging.getLogger("uvicorn").level == logging.INFO
    assert logging.getLogger("fastapi").level == logging.INFO
    assert logging.getLogger("sqlalchemy").level == logging.INFO

def test_getLogger():
    """测试获取日志记录器"""
    logger = getLogger("test_logger")
    assert isinstance(logger, logging.Logger)
    assert logger.name == "test_logger"

def test_log_formatter(clean_handlers):
    """测试日志格式化"""
    # 清理现有处理器
    root_logger = logging.getLogger()
    root_logger.handlers.clear()
    
    setup_logging()
    
    # 获取控制台处理器
    console_handlers = [h for h in root_logger.handlers if isinstance(h, logging.StreamHandler)]
    assert len(console_handlers) > 0
    
    handler = console_handlers[0]
    formatter = handler.formatter
    
    # 验证格式化器
    assert isinstance(formatter, logging.Formatter)
    assert formatter._fmt == "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

def test_logging_handlers_cleanup(clean_handlers):
    """测试处理器清理"""
    # 清理现有处理器
    root_logger = logging.getLogger()
    root_logger.handlers.clear()
    
    # 添加一个测试处理器
    test_handler = logging.StreamHandler()
    root_logger.addHandler(test_handler)
    
    # 重新配置日志
    setup_logging()
    
    # 验证原始测试处理器是否被移除
    assert test_handler not in root_logger.handlers

def test_logs_directory_creation(monkeypatch, cleanup_logs):
    """测试日志目录创建"""
    # 模拟生产环境
    monkeypatch.setattr(settings, "APP_ENV", "production")
    
    # 确保logs目录不存在
    log_dir = Path("logs")
    if log_dir.exists():
        shutil.rmtree(log_dir, ignore_errors=True)
    
    # 配置日志
    setup_logging()
    
    # 验证logs目录是否创建
    assert log_dir.exists()
    assert log_dir.is_dir()

def test_logging_output(capsys, clean_handlers):
    """测试日志输出"""
    # 清理现有处理器
    root_logger = logging.getLogger()
    root_logger.handlers.clear()
    
    setup_logging()
    logger = getLogger("test")
    
    # 记录测试消息
    test_message = "测试日志消息"
    logger.info(test_message)
    
    # 捕获输出
    captured = capsys.readouterr()
    
    # 验证日志消息是否包含在输出中
    assert test_message in captured.out
