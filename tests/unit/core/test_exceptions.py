import pytest
from fastapi import HTTPException, status, Request
from fastapi.responses import JSONResponse
from app.core.exceptions import (
    BaseAppException,
    DatabaseException,
    NotFoundException,
    ValidationException,
    APIException,
    AuthException,
    app_exception_handler
)

def test_base_app_exception():
    """测试基础应用异常"""
    error = BaseAppException("测试错误", code=400, details={"field": "value"})
    assert str(error) == "测试错误"
    assert error.message == "测试错误"
    assert error.code == 400
    assert error.details == {"field": "value"}

def test_database_exception():
    """测试数据库异常"""
    # 测试基本异常信息
    error = DatabaseException("数据库连接失败")
    assert str(error) == "数据库连接失败"
    assert error.message == "数据库连接失败"
    assert error.code == 500
    
    # 测试带有详细信息的异常
    error_with_details = DatabaseException(
        "查询失败",
        details={"sql_error": "语法错误"}
    )
    assert str(error_with_details) == "查询失败"
    assert error_with_details.details["sql_error"] == "语法错误"

def test_not_found_exception():
    """测试未找到资源异常"""
    # 测试基本异常信息
    error = NotFoundException("用户不存在")
    assert str(error) == "用户不存在"
    assert error.message == "用户不存在"
    assert error.code == 404
    
    # 测试带有详细信息的异常
    error_with_details = NotFoundException(
        "股票不存在",
        details={"stock_code": "000001"}
    )
    assert str(error_with_details) == "股票不存在"
    assert error_with_details.details["stock_code"] == "000001"

def test_validation_exception():
    """测试验证异常"""
    # 测试基本异常信息
    error = ValidationException("无效的日期格式")
    assert str(error) == "无效的日期格式"
    assert error.message == "无效的日期格式"
    assert error.code == 422
    
    # 测试带有详细信息的异常
    error_with_details = ValidationException(
        "数值超出范围",
        details={"field": "price", "max": 100}
    )
    assert str(error_with_details) == "数值超出范围"
    assert error_with_details.details["field"] == "price"

def test_api_exception():
    """测试API异常"""
    # 测试基本异常信息
    error = APIException("API请求失败")
    assert str(error) == "API请求失败"
    assert error.message == "API请求失败"
    assert error.code == 502
    
    # 测试自定义状态码
    error_with_code = APIException("服务暂时不可用", code=503)
    assert error_with_code.code == 503

def test_auth_exception():
    """测试认证异常"""
    # 测试基本异常信息
    error = AuthException("token已过期")
    assert str(error) == "token已过期"
    assert error.message == "token已过期"
    assert error.code == 401
    
    # 测试权限不足场景
    error_forbidden = AuthException("权限不足", code=403)
    assert error_forbidden.code == 403

@pytest.mark.asyncio
async def test_app_exception_handler():
    """测试异常处理器"""
    # 创建一个测试异常
    test_exception = BaseAppException(
        message="测试错误",
        code=400,
        details={"test": "value"}
    )
    
    # 创建一个模拟的Request对象
    mock_request = Request(scope={
        "type": "http",
        "method": "GET",
        "path": "/test",
        "headers": []
    })
    
    # 调用异常处理器
    response = await app_exception_handler(mock_request, test_exception)
    
    # 验证返回的JSONResponse
    assert isinstance(response, JSONResponse)
    assert response.status_code == 400
    
    # 验证响应内容
    content = bytes(response.body).decode()
    assert "测试错误" in content
    assert "400" in content
    assert "test" in content
    assert "value" in content

def test_exception_inheritance():
    """测试异常继承关系"""
    # 验证所有异常都继承自BaseAppException
    assert issubclass(DatabaseException, BaseAppException)
    assert issubclass(NotFoundException, BaseAppException)
    assert issubclass(ValidationException, BaseAppException)
    assert issubclass(APIException, BaseAppException)
    assert issubclass(AuthException, BaseAppException)
    
    # 验证BaseAppException继承自Exception
    assert issubclass(BaseAppException, Exception)

def test_default_values():
    """测试异常默认值"""
    # 测试BaseAppException默认值
    base_error = BaseAppException()
    assert base_error.message == "发生内部错误"
    assert base_error.code == 500
    assert base_error.details == {}
    
    # 测试DatabaseException默认值
    db_error = DatabaseException()
    assert db_error.message == "数据库操作失败"
    assert db_error.code == 500
    
    # 测试NotFoundException默认值
    not_found_error = NotFoundException()
    assert not_found_error.message == "请求的资源不存在"
    assert not_found_error.code == 404
    
    # 测试ValidationException默认值
    validation_error = ValidationException()
    assert validation_error.message == "数据验证失败"
    assert validation_error.code == 422
    
    # 测试APIException默认值
    api_error = APIException()
    assert api_error.message == "外部API调用失败"
    assert api_error.code == 502
    
    # 测试AuthException默认值
    auth_error = AuthException()
    assert auth_error.message == "认证失败或权限不足"
    assert auth_error.code == 401
