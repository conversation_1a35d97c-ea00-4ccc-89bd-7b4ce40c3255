"""
核心配置模块单元测试

测试内容:
1. 配置加载
   - 环境变量加载
   - 默认值处理
   - 配置验证

2. 配置项
   - 应用基本配置
   - 数据库配置
   - API配置
   - CORS配置
   - 缓存配置
   - 调度器配置

3. 配置实例
   - 全局配置实例
   - 配置继承关系
"""
import pytest
from pathlib import Path
from typing import Any, Dict, List

from app.core.config import Settings, settings

@pytest.mark.unit
class TestSettings:
    """配置模块测试"""
    
    def test_settings_from_env(self, test_env_vars: Dict[str, Any], test_settings: Settings) -> None:
        """测试从环境变量加载配置"""
        # 验证配置值
        assert test_settings.APP_NAME == "test_app"
        assert test_settings.APP_VERSION == "2.0.0"
        assert test_settings.DEBUG is False
        assert test_settings.API_PREFIX == "/api/v2"
        assert test_settings.DATABASE_URL == "sqlite:///./test.db"
        assert test_settings.SCHEDULER_ENABLED is False
        assert test_settings.LOG_LEVEL == "DEBUG"
        assert test_settings.CACHE_ENABLED is False

    def test_settings_default_values(self) -> None:
        """测试默认配置值"""
        test_settings = Settings()
        
        # 验证默认值
        assert test_settings.APP_NAME == "Quantization"
        assert test_settings.APP_VERSION == "1.0.0"
        assert test_settings.APP_ENV == "development"
        assert test_settings.DEBUG is True
        assert test_settings.DATABASE_URL == "sqlite:///./quantization.db"
        assert test_settings.DATABASE_TYPE == "sqlite"
        assert test_settings.API_PREFIX == "/api/v1"
        assert test_settings.API_V1_STR == "/v1"
        assert test_settings.SCHEDULER_ENABLED is True
        assert test_settings.CACHE_ENABLED is True
        assert test_settings.CACHE_EXPIRATION == 3600

    def test_database_url_validation(self, temp_dir: Path) -> None:
        """测试数据库URL验证"""
        db_path = temp_dir / "test.db"
        test_settings = Settings(DATABASE_URL=f"sqlite:///{db_path}")
        
        # 验证数据库目录是否被创建
        assert temp_dir.exists()
        # 数据库文件父目录应该存在
        assert db_path.parent.exists()

    def test_cors_origins_empty(self) -> None:
        """测试空CORS配置"""
        test_settings = Settings(BACKEND_CORS_ORIGINS=[])
        assert len(test_settings.BACKEND_CORS_ORIGINS) == 0

    def test_cors_origins_with_values(self) -> None:
        """测试CORS配置"""
        test_settings = Settings(
            BACKEND_CORS_ORIGINS=["http://localhost:3000", "https://example.com"]
        )
        assert len(test_settings.BACKEND_CORS_ORIGINS) == 2
        assert "http://localhost:3000" in test_settings.BACKEND_CORS_ORIGINS
        assert "https://example.com" in test_settings.BACKEND_CORS_ORIGINS

    def test_log_configuration(self, test_log_dir: Path) -> None:
        """测试日志配置"""
        test_settings = Settings(
            LOG_LEVEL="INFO",
            LOG_DIR=str(test_log_dir),
            LOG_FORMAT="json"
        )
        assert test_settings.LOG_LEVEL == "INFO"
        assert test_settings.LOG_DIR == str(test_log_dir)
        assert test_settings.LOG_FORMAT == "json"
        assert Path(test_settings.LOG_DIR).exists()

    def test_api_configuration(self) -> None:
        """测试API配置"""
        test_settings = Settings(
            API_HOST="0.0.0.0",
            API_PORT="9000",
            API_PREFIX="/api/v2"
        )
        assert test_settings.API_HOST == "0.0.0.0"
        assert test_settings.API_PORT == "9000"
        assert test_settings.API_PREFIX == "/api/v2"

    def test_cache_configuration(self) -> None:
        """测试缓存配置"""
        test_settings = Settings(
            CACHE_ENABLED=True,
            CACHE_URL="redis://localhost:6379/0",
            CACHE_EXPIRATION=7200
        )
        assert test_settings.CACHE_ENABLED is True
        assert test_settings.CACHE_URL == "redis://localhost:6379/0"
        assert test_settings.CACHE_EXPIRATION == 7200

    def test_scheduler_configuration(self) -> None:
        """测试调度器配置"""
        test_settings = Settings(
            SCHEDULER_ENABLED=True,
            SCHEDULER_TIMEZONE="Asia/Shanghai",
            SCHEDULER_MAX_INSTANCES=5
        )
        assert test_settings.SCHEDULER_ENABLED is True
        assert test_settings.SCHEDULER_TIMEZONE == "Asia/Shanghai"
        assert test_settings.SCHEDULER_MAX_INSTANCES == 5

    def test_global_settings_instance(self) -> None:
        """测试全局settings实例"""
        assert isinstance(settings, Settings)
        assert settings.APP_NAME == "Quantization"
        assert settings.APP_VERSION == "1.0.0"
        assert settings.APP_ENV == "development"
