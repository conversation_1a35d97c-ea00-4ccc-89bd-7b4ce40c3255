"""
核心组件测试配置

提供核心组件测试所需的fixtures和辅助函数。
"""
import pytest
import shutil
from typing import Any, Dict, Generator, AsyncGenerator
from pathlib import Path
from sqlalchemy.ext.asyncio import create_async_engine, AsyncEngine
import asyncio

from app.core.config import Settings

@pytest.fixture
def test_env_vars(test_settings: Settings) -> Dict[str, Any]:
    """测试环境变量"""
    # 返回当前设置的字典表示
    return test_settings.model_dump()

@pytest.fixture
def test_settings() -> Settings:
    """创建测试用Settings实例"""
    return Settings(
        APP_NAME="test_app",
        APP_VERSION="2.0.0",
        APP_ENV="test",
        DEBUG=False,
        DATABASE_URL="sqlite:///./test.db",
        DATABASE_TYPE="sqlite",
        SQL_ECHO=False,
        API_PREFIX="/api/v2",
        API_V1_STR="/api/v1",
        API_HOST="localhost",
        API_PORT="8000",
        BACKEND_CORS_ORIGINS=["*"],
        DATA_API_TYPE="mairui",
        MAIRUI_TOKEN="test-token",
        TUSHARE_TOKEN="test-token",
        SCHEDULER_ENABLED=False,
        DATA_UPDATE_CRON="0 0 * * *",
        STOCK_LIST_UPDATE_CRON="0 0 * * 1",
        CACHE_ENABLED=False,
        CACHE_EXPIRATION=3600,
        REDIS_URL="redis://localhost:6379/0",
        LOG_LEVEL="DEBUG",
        LOG_DIR="./logs"
    )

@pytest.fixture
def temp_dir() -> Generator[Path, None, None]:
    """创建临时目录用于测试"""
    test_dir = Path("./test_temp")
    test_dir.mkdir(exist_ok=True)
    yield test_dir
    # 清理临时目录
    if test_dir.exists():
        shutil.rmtree(test_dir, ignore_errors=True)

@pytest.fixture
def test_db_url(temp_dir: Path) -> str:
    """测试数据库URL"""
    return f"sqlite:///{temp_dir}/test.db"

@pytest.fixture
def test_log_dir(temp_dir: Path) -> Path:
    """测试日志目录"""
    log_dir = temp_dir / "logs"
    log_dir.mkdir(exist_ok=True)
    return log_dir

@pytest.fixture
async def test_engine(test_db_url: str) -> AsyncGenerator[AsyncEngine, None]:
    """创建测试用异步数据库引擎"""
    # 将同步URL转换为异步URL
    async_url = test_db_url.replace('sqlite:///', 'sqlite+aiosqlite:///')
    engine = create_async_engine(
        async_url,
        echo=False,
        future=True
    )
    
    try:
        yield engine
    finally:
        await engine.dispose()
