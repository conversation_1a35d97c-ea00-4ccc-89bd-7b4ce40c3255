"""
StockFetchService测试用例
"""

import pytest
from datetime import datetime
from unittest.mock import Mock, AsyncMock

from app.services.schedule.stock_fetch_service import StockDataFetchService


class TestStockDataFetchService:
    """StockDataFetchService测试类"""

    @pytest.fixture
    def fetch_service(self):
        """创建数据获取服务实例"""
        return StockDataFetchService()

    @pytest.mark.asyncio
    async def test_init_data_fetcher(self, fetch_service, mock_db_session, mocker):
        """测试初始化数据获取器"""
        # 模拟数据库会话生成器
        mocker.patch('app.services.schedule.stock_fetch_service.get_db',
                    return_value=self._mock_db_generator(mock_db_session))
        
        # 模拟数据获取器工厂
        mock_factory = Mock()
        mock_fetcher = AsyncMock()
        mock_factory.create_fetcher.return_value = mock_fetcher
        mocker.patch('app.services.schedule.stock_fetch_service.DataFetcherFactory',
                    return_value=mock_factory)
        
        fetcher = await fetch_service.init_data_fetcher()
        
        assert fetcher == mock_fetcher
        mock_factory.create_fetcher.assert_called_once()

    @pytest.mark.asyncio
    async def test_update_stock_list(self, fetch_service, mocker):
        """测试更新股票列表"""
        # 模拟数据获取器
        mock_fetcher = AsyncMock()
        mock_fetcher.get_stock_list = AsyncMock(return_value=[
            {
                'code': '000001',
                'name': '平安银行',
                'exchange': 'SZ',
                'full_code': 'SZ000001'
            },
            {
                'code': '000002',
                'name': '万科A',
                'exchange': 'SZ',
                'full_code': 'SZ000002'
            }
        ])
        mocker.patch.object(fetch_service, 'init_data_fetcher',
                          return_value=mock_fetcher)
        
        # 模拟存储服务
        mock_storage = AsyncMock()
        mock_storage.batch_save_stock_info = AsyncMock()
        mocker.patch.object(fetch_service, '_get_storage_service',
                          return_value=mock_storage)
        
        await fetch_service.update_stock_list()
        
        mock_fetcher.get_stock_list.assert_called_once()
        mock_storage.batch_save_stock_info.assert_called_once()

    @pytest.mark.asyncio
    async def test_update_stock_list_error(self, fetch_service, mocker):
        """测试更新股票列表异常情况"""
        # 模拟数据获取器抛出异常
        mock_fetcher = AsyncMock()
        mock_fetcher.get_stock_list = AsyncMock(side_effect=Exception("测试异常"))
        mocker.patch.object(fetch_service, 'init_data_fetcher',
                          return_value=mock_fetcher)
        
        with pytest.raises(Exception) as exc_info:
            await fetch_service.update_stock_list()
        
        assert str(exc_info.value) == "测试异常"

    @pytest.mark.asyncio
    async def test_update_daily_data(self, fetch_service, mocker):
        """测试更新日线数据"""
        # 模拟数据获取器
        mock_fetcher = AsyncMock()
        # 模拟获取股票列表
        mock_fetcher.get_stock_list = AsyncMock(return_value=[
            {
                'code': '000001',
                'name': '平安银行',
                'exchange': 'SZ',
                'full_code': 'SZ000001'
            }
        ])
        
        # 模拟获取日线数据
        mock_fetcher.get_daily_data = AsyncMock(return_value=[
            {
                'code': '000001',
                'trade_date': '2025-03-21',
                'open': 10.0,
                'high': 10.8,
                'low': 9.9,
                'close': 10.5,
                'volume': 1000000,
                'amount': 10500000
            }
        ])
        mocker.patch.object(fetch_service, 'init_data_fetcher',
                          return_value=mock_fetcher)
        
        # 模拟存储服务
        mock_storage = AsyncMock()
        mock_storage.batch_save_stock_daily = AsyncMock()
        mocker.patch.object(fetch_service, '_get_storage_service',
                          return_value=mock_storage)
        
        await fetch_service.update_daily_data()
        
        # 验证调用
        mock_fetcher.get_stock_list.assert_called_once()
        mock_fetcher.get_daily_data.assert_called_once()
        mock_storage.batch_save_stock_daily.assert_called_once()

    @pytest.mark.asyncio
    async def test_update_daily_data_error(self, fetch_service, mocker):
        """测试更新日线数据异常情况"""
        # 模拟数据获取器
        mock_fetcher = AsyncMock()
        # 模拟获取股票列表成功
        mock_fetcher.get_stock_list = AsyncMock(return_value=[
            {
                'code': '000001',
                'name': '平安银行',
                'exchange': 'SZ',
                'full_code': 'SZ000001'
            }
        ])
        # 模拟获取日线数据失败
        mock_fetcher.get_daily_data = AsyncMock(side_effect=Exception("测试异常"))
        mocker.patch.object(fetch_service, 'init_data_fetcher',
                          return_value=mock_fetcher)
        
        # 模拟存储服务
        mock_storage = AsyncMock()
        mock_storage.batch_save_stock_daily = AsyncMock()
        mocker.patch.object(fetch_service, '_get_storage_service',
                          return_value=mock_storage)
        
        # 执行更新
        with pytest.raises(Exception) as exc_info:
            await fetch_service.update_daily_data()
        
        # 验证调用
        mock_fetcher.get_stock_list.assert_called_once()
        mock_fetcher.get_daily_data.assert_called_once()
        # 由于日线数据获取失败，不应该调用保存方法
        mock_storage.batch_save_stock_daily.assert_not_called()
        assert str(exc_info.value) == "更新股票 000001 日线数据失败: 测试异常"

    @staticmethod
    async def _mock_db_generator(mock_session):
        """模拟数据库会话生成器"""
        yield mock_session
