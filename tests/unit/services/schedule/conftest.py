"""
调度服务测试配置
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.scheduler import scheduler
from app.services.storage.stock_storage import StockStorageService


@pytest.fixture
def mock_scheduler(mocker):
    """模拟调度器"""
    mock_scheduler = Mock()
    mock_scheduler.get_jobs = Mock(return_value=[])
    mock_scheduler.get_job = Mock(return_value=None)
    mocker.patch('app.core.scheduler.scheduler.scheduler', mock_scheduler)
    return mock_scheduler


@pytest.fixture
def mock_storage_service(mocker):
    """模拟存储服务"""
    mock_service = Mock(spec=StockStorageService)
    mock_service._cache = {}
    return mock_service


@pytest.fixture
def mock_db_session():
    """模拟数据库会话"""
    mock_session = AsyncMock(spec=AsyncSession)
    return mock_session


@pytest.fixture
def sample_job():
    """创建示例任务"""
    return {
        'id': 'test_job',
        'name': 'Test Job',
        'func': 'test_module.test_func',
        'trigger': 'cron[hour=0]',
        'next_run_time': datetime.now() + timedelta(hours=1),
        'args': (),
        'kwargs': {}
    }


@pytest.fixture
def active_jobs():
    """创建活动任务列表"""
    current_time = datetime.now()
    return [
        Mock(
            id='job1',
            name='Job 1',
            func=Mock(__module__='test_module', __name__='test_func1'),
            trigger='cron[hour=0]',
            next_run_time=current_time + timedelta(hours=1),
            args=(),
            kwargs={}
        ),
        Mock(
            id='job2',
            name='Job 2',
            func=Mock(__module__='test_module', __name__='test_func2'),
            trigger='cron[hour=12]',
            next_run_time=current_time + timedelta(hours=2),
            args=(),
            kwargs={}
        )
    ]
