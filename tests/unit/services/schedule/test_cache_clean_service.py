"""
CacheCleanService测试用例
"""

from venv import logger
import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from app.services.schedule.cache_clean_service import CacheCleanService


class TestCacheCleanService:
    """CacheCleanService测试类"""

    @pytest.fixture
    def cache_service(self):
        """创建缓存清理服务实例"""
        return CacheCleanService()

    @pytest.mark.asyncio
    async def test_get_storage_service(self, cache_service, mock_db_session, mock_storage_service, mocker):
        """测试获取存储服务实例"""
        # 模拟数据库会话生成器
        mocker.patch('app.services.schedule.cache_clean_service.get_db', 
                    return_value=self._mock_db_generator(mock_db_session))
        
        # 模拟StockStorageService构造函数
        mocker.patch('app.services.schedule.cache_clean_service.StockStorageService',
                    return_value=mock_storage_service)
        
        storage = await cache_service._get_storage_service()
        
        assert storage == mock_storage_service
        assert len(cache_service._storage_services) == 1
        assert cache_service._storage_services[0] == mock_storage_service

    @pytest.mark.asyncio
    async def test_clean_memory_cache(self, cache_service, mock_storage_service, mocker):
        """测试清理内存缓存"""
        # 模拟_get_storage_service方法
        mocker.patch.object(cache_service, '_get_storage_service',
                          return_value=mock_storage_service)
        
        # 创建一个带有clear方法的字典作为缓存
        test_cache = {'key1': 'value1', 'key2': 'value2'}
        
        # 设置mock对象的_cache属性
        mock_storage_service._cache = test_cache
        cache_service._storage_services = [mock_storage_service]
        
        # 直接调用原始方法而不是通过装饰器
        await cache_service.clean_memory_cache.__wrapped__(cache_service)
        
        logger.info(f"{mock_storage_service._cache}")
        # 验证缓存被清理
        assert len(mock_storage_service._cache) == 0
        assert len(cache_service._storage_services) == 0

    @pytest.mark.asyncio
    async def test_clean_memory_cache_error(self, cache_service, mocker):
        """测试清理内存缓存异常情况"""
        # 模拟_get_storage_service方法抛出异常
        mocker.patch.object(cache_service, '_get_storage_service',
                          side_effect=Exception("测试异常"))
        
        # 确保在调用clean_memory_cache之前没有存储服务实例
        cache_service._storage_services = []
        with pytest.raises(Exception) as exc_info:
            # 直接调用原始方法而不是通过装饰器
            await cache_service.clean_memory_cache.__wrapped__(cache_service)
        
        assert str(exc_info.value) == "测试异常"

    @pytest.mark.asyncio
    async def test_clean_expired_data(self, cache_service, mock_storage_service, mocker):
        """测试清理过期数据"""
        # 模拟_get_storage_service方法
        mocker.patch.object(cache_service, '_get_storage_service',
                          return_value=mock_storage_service)
        
        await cache_service.clean_expired_data()
        
        # 由于目前clean_expired_data的具体实现是TODO状态，
        # 这里只验证方法能正常执行，不抛出异常

    @staticmethod
    async def _mock_db_generator(mock_session):
        """模拟数据库会话生成器"""
        yield mock_session
