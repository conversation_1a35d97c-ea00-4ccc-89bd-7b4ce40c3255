"""
高级技术指标服务测试

测试基于 new_feature_formatted.md 文档实现的新功能
"""
import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, MagicMock, patch

from app.services.indicators.advanced_indicator_service import (
    AdvancedIndicatorService,
    PeriodType
)
from app.core.exceptions import ValidationException


class TestAdvancedIndicatorService:
    """高级技术指标服务测试类"""
    
    @pytest.fixture
    def mock_storage_service(self):
        """模拟存储服务"""
        return MagicMock()
    
    @pytest.fixture
    def advanced_service(self, mock_storage_service):
        """创建高级指标服务实例"""
        return AdvancedIndicatorService(mock_storage_service)
    
    @pytest.fixture
    def sample_stock_data(self):
        """生成样本股票数据"""
        dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
        np.random.seed(42)
        
        # 生成模拟的OHLCV数据
        base_price = 100
        returns = np.random.normal(0, 0.02, len(dates))
        prices = base_price * (1 + returns).cumprod()
        
        data = pd.DataFrame({
            'Open': prices * (1 + np.random.normal(0, 0.01, len(dates))),
            'High': prices * (1 + np.abs(np.random.normal(0, 0.02, len(dates)))),
            'Low': prices * (1 - np.abs(np.random.normal(0, 0.02, len(dates)))),
            'Close': prices,
            'Volume': np.random.randint(100000, 1000000, len(dates))
        }, index=dates)
        
        return data
    
    def test_select_period_valid_indices(self, advanced_service):
        """测试有效周期索引的选择"""
        # 测试所有有效的周期索引
        expected_periods = {
            1: PeriodType.DAILY,
            2: PeriodType.WEEKLY,
            3: PeriodType.MONTHLY,
            4: PeriodType.MIN_15,
            5: PeriodType.MIN_30,
            6: PeriodType.MIN_60
        }
        
        for index, expected_period in expected_periods.items():
            result = advanced_service.select_period(index)
            assert result == expected_period
    
    def test_select_period_invalid_index(self, advanced_service):
        """测试无效周期索引的处理"""
        invalid_indices = [0, 7, -1, 100]
        
        for invalid_index in invalid_indices:
            with pytest.raises(ValidationException):
                advanced_service.select_period(invalid_index)
    
    def test_calculate_bollinger_bands(self, advanced_service, sample_stock_data):
        """测试Bollinger Bands计算"""
        result = advanced_service.calculate_bollinger_bands(sample_stock_data)
        
        # 检查返回的数据结构
        assert 'middle' in result
        assert 'upper' in result
        assert 'lower' in result
        
        # 检查数据长度一致性
        assert len(result['middle']) == len(sample_stock_data)
        assert len(result['upper']) == len(sample_stock_data)
        assert len(result['lower']) == len(sample_stock_data)
        
        # 检查布林带的基本关系：上轨 > 中轨 > 下轨
        valid_data = result['middle'].dropna()
        if len(valid_data) > 0:
            # 取一些有效数据点进行检查
            for i in valid_data.index:
                if not pd.isna(result['upper'].loc[i]) and not pd.isna(result['lower'].loc[i]):
                    assert result['upper'].loc[i] >= result['middle'].loc[i]
                    assert result['middle'].loc[i] >= result['lower'].loc[i]
    
    def test_calculate_bollinger_bands_custom_params(self, advanced_service, sample_stock_data):
        """测试自定义参数的Bollinger Bands计算"""
        result = advanced_service.calculate_bollinger_bands(
            sample_stock_data, 
            window=10, 
            std_dev=1.5
        )
        
        assert 'middle' in result
        assert 'upper' in result
        assert 'lower' in result
        
        # 检查自定义参数下的结果
        assert len(result['middle'].dropna()) > 0
    
    def test_calculate_volume_inout_difference(self, advanced_service, sample_stock_data):
        """测试成交量内外盘差异计算"""
        result = advanced_service.calculate_volume_inout_difference(sample_stock_data)
        
        # 检查返回的数据结构
        assert 'in_vol' in result
        assert 'ex_vol' in result
        assert 'diff_vol' in result
        
        # 检查数据长度一致性
        assert len(result['in_vol']) == len(sample_stock_data)
        assert len(result['ex_vol']) == len(sample_stock_data)
        assert len(result['diff_vol']) == len(sample_stock_data)
        
        # 检查内外盘之和等于总成交量
        total_calculated = result['in_vol'] + result['ex_vol']
        np.testing.assert_array_almost_equal(
            total_calculated.values, 
            sample_stock_data['Volume'].values,
            decimal=2
        )
        
        # 检查差异值为正数（用于对数计算）
        assert (result['diff_vol'] > 0).all()
    
    def test_calculate_volume_bollinger(self, advanced_service, sample_stock_data):
        """测试基于成交量的Bollinger指标计算"""
        # 先计算成交量差异
        volume_diff = advanced_service.calculate_volume_inout_difference(sample_stock_data)
        
        # 计算成交量Bollinger
        result = advanced_service.calculate_volume_bollinger(volume_diff['diff_vol'])
        
        # 检查返回的数据结构
        assert 'vol_middle' in result
        assert 'vol_upper' in result
        assert 'vol_lower' in result
        assert 'log_vol' in result
        
        # 检查数据长度一致性
        assert len(result['vol_middle']) == len(sample_stock_data)
        assert len(result['vol_upper']) == len(sample_stock_data)
        assert len(result['vol_lower']) == len(sample_stock_data)
        assert len(result['log_vol']) == len(sample_stock_data)
    
    def test_calculate_kdj(self, advanced_service, sample_stock_data):
        """测试KDJ指标计算"""
        result = advanced_service.calculate_kdj(sample_stock_data)
        
        # 检查返回的数据结构
        assert 'K' in result
        assert 'D' in result
        assert 'J' in result
        
        # 检查数据长度一致性
        assert len(result['K']) == len(sample_stock_data)
        assert len(result['D']) == len(sample_stock_data)
        assert len(result['J']) == len(sample_stock_data)
        
        # 检查KDJ值的基本范围（K和D通常在0-100之间，J可能超出）
        k_values = result['K'].dropna()
        d_values = result['D'].dropna()
        
        if len(k_values) > 0 and len(d_values) > 0:
            # K和D值应该大部分在0-100之间
            assert k_values.min() >= 0
            assert d_values.min() >= 0
            # 允许一些超出100的情况，这在极端市场情况下是正常的
    
    def test_calculate_kdj_custom_params(self, advanced_service, sample_stock_data):
        """测试自定义参数的KDJ计算"""
        result = advanced_service.calculate_kdj(
            sample_stock_data,
            k_period=14,
            d_period=5,
            j_period=5
        )
        
        assert 'K' in result
        assert 'D' in result
        assert 'J' in result
        
        # 检查自定义参数下的结果
        assert len(result['K'].dropna()) > 0
    
    def test_calculate_buy_sell_signals(self, advanced_service, sample_stock_data):
        """测试买卖点信号计算"""
        # 准备所需的指标数据
        volume_diff = advanced_service.calculate_volume_inout_difference(sample_stock_data)
        volume_bollinger = advanced_service.calculate_volume_bollinger(volume_diff['diff_vol'])
        kdj_indicators = advanced_service.calculate_kdj(sample_stock_data)
        
        # 计算交易信号
        result = advanced_service.calculate_buy_sell_signals(
            sample_stock_data, volume_bollinger, kdj_indicators
        )
        
        # 检查返回的数据结构
        assert 'buy_signals' in result
        assert 'sell_signals' in result
        
        # 检查信号为列表类型
        assert isinstance(result['buy_signals'], list)
        assert isinstance(result['sell_signals'], list)
        
        # 检查信号位置的有效性
        if result['buy_signals']:
            for signal in result['buy_signals']:
                assert isinstance(signal, int)
                assert signal >= 0
        
        if result['sell_signals']:
            for signal in result['sell_signals']:
                assert isinstance(signal, int)
                assert signal >= 0
    
    @pytest.mark.asyncio
    async def test_get_stock_data_error_handling(self, advanced_service):
        """测试股票数据获取的错误处理"""
        # 模拟数据获取失败
        advanced_service.data_fetcher.get_stock_data = AsyncMock(return_value=None)
        
        with pytest.raises(ValidationException):
            await advanced_service.get_stock_data("INVALID_CODE", "2023-01-01", "2023-12-31")
    
    def test_calculate_bollinger_bands_missing_column(self, advanced_service):
        """测试缺少必需列时的错误处理"""
        # 创建缺少Close列的数据
        incomplete_data = pd.DataFrame({
            'Open': [100, 101, 102],
            'High': [105, 106, 107],
            'Low': [95, 96, 97],
            'Volume': [1000, 1100, 1200]
        })
        
        with pytest.raises(ValidationException):
            advanced_service.calculate_bollinger_bands(incomplete_data)
    
    @pytest.mark.asyncio
    async def test_complete_indicators_integration(self, advanced_service, sample_stock_data):
        """测试完整指标计算的集成测试"""
        # 模拟get_stock_data方法返回样本数据
        advanced_service.get_stock_data = AsyncMock(return_value=sample_stock_data)
        
        result = await advanced_service.calculate_complete_indicators(
            stock_code="000001",
            start_date="2023-01-01",
            end_date="2023-12-31",
            period_index=1,
            bollinger_window=20,
            lag=2
        )
        
        # 检查返回的完整结果结构
        expected_keys = [
            'period_type', 'data_points', 'date_range',
            'price_bollinger', 'volume_analysis', 'volume_bollinger',
            'kdj_indicators', 'trading_signals', 'parameters'
        ]
        
        for key in expected_keys:
            assert key in result
        
        # 检查参数信息
        assert result['parameters']['bollinger_window'] == 20
        assert result['parameters']['lag'] == 2
        assert result['parameters']['period_index'] == 1
        
        # 检查数据点数量
        assert result['data_points'] == len(sample_stock_data)
          # 检查周期类型
        assert result['period_type'] == "daily"
    
    @pytest.mark.asyncio 
    async def test_complete_indicators_with_different_periods(self, advanced_service, sample_stock_data):
        """测试不同周期的完整指标计算"""
        advanced_service.get_stock_data = AsyncMock(return_value=sample_stock_data)
        
        # 测试不同的周期索引
        for period_index in range(1, 7):
            result = await advanced_service.calculate_complete_indicators(
                stock_code="000001",
                start_date="2023-01-01", 
                end_date="2023-12-31",
                period_index=period_index
            )
            
            # 检查周期类型设置正确
            expected_period = advanced_service.select_period(period_index)
            assert result['period_type'] == expected_period
            
            # 检查参数设置正确
            assert result['parameters']['period_index'] == period_index


# 集成测试和性能测试
class TestAdvancedIndicatorServiceIntegration:
    """高级技术指标服务集成测试"""
    
    @pytest.mark.asyncio
    async def test_real_calculation_workflow(self):
        """测试真实计算工作流程"""
        # 这个测试需要真实的数据库连接，在实际环境中运行
        pass
    
    @pytest.mark.asyncio
    async def test_performance_with_large_dataset(self):
        """测试大数据集的性能"""
        # 生成大量数据进行性能测试
        pass
    
    def test_memory_usage(self):
        """测试内存使用情况"""
        # 监控内存使用情况
        pass


# 运行测试的示例
if __name__ == "__main__":
    pytest.main([__file__, "-v"])
