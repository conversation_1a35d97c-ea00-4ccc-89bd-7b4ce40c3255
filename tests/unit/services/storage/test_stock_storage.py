"""
StockStorageService单元测试
"""
import pytest
from datetime import datetime
from typing import Optional
from unittest.mock import Mock, AsyncMock, patch

from app.models.stock import StockInfo, StockDailyBase, StockIndicator, IndicatorVersion
from app.core.exceptions import DatabaseException

# ====== Mock类 ======
class MockPartitionManager:
    """模拟PartitionManager，用于测试"""
    
    @staticmethod
    def get_partition(key: str, dt: datetime):
        """模拟获取指定日期的分区"""
        from app.models.stock import StockDailyBase
        table_suffix = dt.strftime("%Y%m")
        class_name = f"Partition_StockDailyBase_{table_suffix}"
        mock_partition = type(
            class_name,
            (StockDailyBase,),
            {
                '__tablename__': f'stock_daily_{table_suffix}',
                '__table_args__': {'extend_existing': True},
                '_partition_date': dt,
                'create_table': lambda bind, checkfirst=True: None
            }
        )
        return mock_partition

    @classmethod
    def get_partitions_between(cls, key: str, start_date: Optional[datetime], end_date: Optional[datetime]):
        """模拟获取时间范围内的所有分区"""
        if not start_date:
            start_date = end_date
        if not end_date:
            end_date = start_date
        if not start_date or not end_date:
            return []
            
        current_date = datetime(start_date.year, start_date.month, 1)
        end_month = datetime(end_date.year, end_date.month, 1)
        
        partitions = []
        while current_date <= end_month:
            partition = cls.get_partition(key, current_date)
            if partition and partition not in partitions:
                partitions.append(partition)
            if current_date.month == 12:
                current_date = datetime(current_date.year + 1, 1, 1)
            else:
                current_date = datetime(current_date.year, current_date.month + 1, 1)
        return partitions

# ====== 测试股票基本信息相关方法 ======

@pytest.mark.asyncio
async def test_save_stock_info_success(stock_storage, sample_stock_info):
    """测试成功保存股票信息"""
    result = await stock_storage.save_stock_info(sample_stock_info)
    
    assert isinstance(result, StockInfo)
    assert result.code == sample_stock_info["code"]
    assert result.name == sample_stock_info["name"]
    stock_storage.db.add.assert_called_once()
    stock_storage.db.commit.assert_called_once()

@pytest.mark.asyncio
async def test_delete_stock_daily_success(stock_storage):
    """测试删除日线数据成功"""
    stock_code = "000001"
    start_date = datetime(2025, 3, 21)
    end_date = datetime(2025, 3, 22)
    
    mock_partition = Mock()
    mock_result = AsyncMock()
    mock_result.rowcount = 2  # 假设删除了两条记录
    stock_storage.db.execute = AsyncMock(return_value=mock_result)
    
    with patch('app.models.partition.PartitionManager.get_partitions_between',
              side_effect=MockPartitionManager.get_partitions_between):
        result = await stock_storage.delete_stock_daily(
            stock_code,
            start_date=start_date,
            end_date=end_date
        )
    
    assert result is True
    stock_storage.db.execute.assert_called_once()

@pytest.mark.asyncio
async def test_delete_stock_daily_not_found(stock_storage):
    """测试删除不存在的日线数据"""
    # 创建一个模拟分区
    mock_partition = MockPartitionManager.get_partition("StockDailyBase", datetime(2025, 3, 21))
    
    # 模拟数据库执行结果
    mock_result = AsyncMock()
    mock_result.rowcount = 0
    stock_storage.db.execute = AsyncMock(return_value=mock_result)
    
    with patch('app.models.partition.PartitionManager.get_partitions_between',
              return_value=[mock_partition]):
        result = await stock_storage.delete_stock_daily("000001")
    
    assert result is False
    # 验证 execute 被调用一次
    stock_storage.db.execute.assert_called_once()

@pytest.mark.asyncio
async def test_batch_save_indicators_success(stock_storage):
    """测试批量保存技术指标成功"""
    mock_version = IndicatorVersion(
        indicator_type="MA",
        version_hash="test_hash",
        is_current=True
    )
    
    # Mock get_current_indicator_version
    mock_version_result = AsyncMock()
    mock_version_result.scalar_one_or_none = Mock(return_value=mock_version)
    stock_storage.db.execute = AsyncMock(side_effect=[mock_version_result])
    
    indicators_data = [
        {
            "indicator_type": "MA",
            "values": {"MA5": 10.5, "MA10": 11.0},
            "trade_date": datetime(2025, 3, 21)
        },
        {
            "indicator_type": "MA",
            "values": {"MA5": 10.6, "MA10": 11.1},
            "trade_date": datetime(2025, 3, 22)
        }
    ]
    
    result = await stock_storage.batch_save_indicators("000001", indicators_data)
    
    assert len(result) == 2
    assert all(isinstance(indicator, StockIndicator) for indicator in result)
    assert all(indicator.stock_code == "000001" for indicator in result)
    assert all(indicator.version_hash == mock_version.version_hash for indicator in result)
    # 更新断言以匹配新的实现
    stock_storage.db.add_all.assert_called_once()
    stock_storage.db.commit.assert_called_once()

@pytest.mark.asyncio
async def test_batch_save_indicators_no_version(stock_storage):
    """测试批量保存技术指标时没有有效版本的情况"""
    # Mock get_current_indicator_version to return None
    mock_version_result = AsyncMock()
    mock_version_result.scalar_one_or_none = Mock(return_value=None)
    stock_storage.db.execute = AsyncMock(return_value=mock_version_result)
    
    indicators_data = [
        {
            "indicator_type": "MA",
            "values": {"MA5": 10.5},
            "trade_date": datetime(2025, 3, 21)
        }
    ]
    
    with pytest.raises(DatabaseException) as exc_info:
        await stock_storage.batch_save_indicators("000001", indicators_data)
    
    assert "没有有效的版本信息" in str(exc_info.value)

@pytest.mark.asyncio
async def test_save_stock_daily_failure(stock_storage, sample_daily_data):
    """测试保存日线数据失败的情况"""
    mock_partition = Mock()
    stock_storage.db.run_sync = AsyncMock()
    stock_storage.db.commit.side_effect = Exception("数据库错误")
    
    with pytest.raises(DatabaseException) as exc_info, \
         patch('app.models.partition.PartitionManager.get_partition', return_value=mock_partition):
        await stock_storage.save_stock_daily(
            "000001",
            sample_daily_data,
            datetime(2025, 3, 21)
        )
    
    assert "保存日线数据失败" in str(exc_info.value)

@pytest.mark.asyncio
async def test_batch_save_stock_info_success(stock_storage):
    """测试批量保存股票信息成功"""
    stock_info_list = [
        {
            "code": "000001",
            "name": "平安银行",
            "industry": "银行",
            "exchange": "SZ",
            "full_code": "SZ000001"
        },
        {
            "code": "000002",
            "name": "万科A",
            "industry": "房地产",
            "exchange": "SZ",
            "full_code": "SZ000002"
        }
    ]
    
    result = await stock_storage.batch_save_stock_info(stock_info_list)
    
    assert len(result) == 2
    assert all(isinstance(info, StockInfo) for info in result)
    assert result[0].code == "000001"
    assert result[1].code == "000002"
    stock_storage.db.add_all.assert_called_once()
    stock_storage.db.commit.assert_called_once()

@pytest.mark.asyncio
async def test_batch_save_stock_daily_success(stock_storage):
    """测试批量保存日线数据成功"""
    daily_data_list = [
        {
            "trade_date": datetime(2025, 3, 21),
            "open": 10.0,
            "close": 10.5
        },
        {
            "trade_date": datetime(2025, 3, 22),
            "open": 10.5,
            "close": 11.0
        }
    ]
    stock_code = "000001"
    partition_date = datetime(2025, 3, 21)
    
    mock_partition = Mock()
    stock_storage.db.run_sync = AsyncMock()
    
    with patch('app.models.partition.PartitionManager.get_partition', return_value=mock_partition), \
         patch('app.models.partition.PartitionManager', MockPartitionManager):
        result = await stock_storage.batch_save_stock_daily(
            stock_code,
            daily_data_list,
            partition_date
        )
    
    assert len(result) == 2
    assert all(r.stock_code == stock_code for r in result)
    stock_storage.db.add_all.assert_called_once()
    stock_storage.db.commit.assert_called_once()

@pytest.mark.asyncio
async def test_update_stock_info_success(stock_storage):
    """测试更新股票信息成功"""
    stock_code = "000001"
    db_info = StockInfo(code=stock_code, name="旧名称", industry="银行")
    update_data = {"name": "新名称", "industry": "金融"}
    
    # Mock get_stock_info
    mock_info_result = AsyncMock()
    mock_info_result.scalar_one_or_none = Mock(return_value=db_info)
    stock_storage.db.execute = AsyncMock(side_effect=[mock_info_result])
    
    result = await stock_storage.update_stock_info(stock_code, update_data)
    
    assert result.code == stock_code
    assert result.name == "新名称"
    assert result.industry == "金融"
    assert stock_storage._cache[f"stock_info:{stock_code}"] == result
    stock_storage.db.commit.assert_called_once()

@pytest.mark.asyncio
async def test_update_stock_info_not_found(stock_storage):
    """测试更新不存在的股票信息"""
    mock_result = AsyncMock()
    mock_result.scalar_one_or_none = Mock(return_value=None)
    stock_storage.db.execute = AsyncMock(return_value=mock_result)
    
    with pytest.raises(DatabaseException) as exc_info:
        await stock_storage.update_stock_info("000001", {"name": "新名称"})
    
    assert "不存在" in str(exc_info.value)

@pytest.mark.asyncio
async def test_delete_stock_info_success(stock_storage):
    """测试删除股票信息成功"""
    stock_code = "000001"
    mock_result = AsyncMock()
    mock_result.rowcount = 1
    stock_storage.db.execute = AsyncMock(return_value=mock_result)
    
    # 先添加到缓存
    stock_storage._cache[f"stock_info:{stock_code}"] = StockInfo(code=stock_code)
    
    result = await stock_storage.delete_stock_info(stock_code)
    
    assert result is True
    assert f"stock_info:{stock_code}" not in stock_storage._cache
    stock_storage.db.execute.assert_called_once()

@pytest.mark.asyncio
async def test_delete_stock_info_not_found(stock_storage):
    """测试删除不存在的股票信息"""
    mock_result = AsyncMock()
    mock_result.rowcount = 0
    stock_storage.db.execute = AsyncMock(return_value=mock_result)
    
    result = await stock_storage.delete_stock_info("000001")
    
    assert result is False
    stock_storage.db.execute.assert_called_once()

@pytest.mark.asyncio
async def test_save_stock_info_failure(stock_storage, sample_stock_info):
    """测试保存股票信息失败的情况"""
    stock_storage.db.commit.side_effect = Exception("数据库错误")
    
    with pytest.raises(DatabaseException) as exc_info:
        await stock_storage.save_stock_info(sample_stock_info)
    
    assert "保存股票信息失败" in str(exc_info.value)

@pytest.mark.asyncio
async def test_get_stock_info_from_cache(stock_storage):
    """测试从缓存获取股票信息"""
    stock_code = "000001"
    cached_info = StockInfo(code=stock_code, name="测试股票")
    stock_storage._cache[f"stock_info:{stock_code}"] = cached_info
    
    result = await stock_storage.get_stock_info(stock_code)
    
    assert result == cached_info
    stock_storage.db.execute.assert_not_called()

@pytest.mark.asyncio
async def test_get_stock_info_from_db(stock_storage):
    """测试从数据库获取股票信息"""
    stock_code = "000001"
    db_info = StockInfo(code=stock_code, name="测试股票")
    
    mock_result = AsyncMock()
    mock_result.scalar_one_or_none = Mock(return_value=db_info)
    stock_storage.db.execute = AsyncMock(return_value=mock_result)
    
    result = await stock_storage.get_stock_info(stock_code)
    
    assert isinstance(result, StockInfo)
    assert result.code == stock_code
    assert result.name == "测试股票"
    assert stock_storage._cache[f"stock_info:{stock_code}"] is not None
    assert stock_storage._cache[f"stock_info:{stock_code}"].code == stock_code
    stock_storage.db.execute.assert_called_once()

# ====== 测试股票日线数据相关方法 ======

@pytest.mark.asyncio
async def test_save_stock_daily_success(stock_storage, sample_daily_data):
    """测试成功保存日线数据"""
    mock_partition = Mock()
    stock_storage.db.run_sync = AsyncMock()
    
    with patch('app.models.partition.PartitionManager.get_partition', return_value=mock_partition):
        result = await stock_storage.save_stock_daily(
            "000001",
            sample_daily_data,
            datetime(2025, 3, 21)
        )
    
    assert result.stock_code == "000001"
    stock_storage.db.add.assert_called_once()
    stock_storage.db.commit.assert_called_once()

@pytest.mark.asyncio
async def test_get_stock_daily_success(stock_storage):
    """测试成功获取日线数据"""
    mock_partition = Mock()
    mock_daily = Mock(stock_code="000001", trade_date=datetime(2025, 3, 21))
    
    mock_result = AsyncMock()
    mock_result.scalars = Mock()
    mock_result.scalars.return_value = Mock(all=lambda: [mock_daily])
    stock_storage.db.execute = AsyncMock(return_value=mock_result)
    
    with patch('app.models.partition.PartitionManager.get_partitions_between',
              side_effect=MockPartitionManager.get_partitions_between):
        result = await stock_storage.get_stock_daily(
            "000001",
            start_date="2025-03-21",
            end_date="2025-03-21"
        )
    
    assert len(result) == 1
    assert result[0].stock_code == "000001"
    stock_storage.db.execute.assert_called_once()

# ====== 测试技术指标相关方法 ======

@pytest.mark.asyncio
async def test_save_indicator_success(stock_storage):
    """测试成功保存技术指标"""
    mock_version = IndicatorVersion(
        indicator_type="MA",
        version_hash="test_hash",
        is_current=True
    )
    
    # Mock get_current_indicator_version
    mock_version_result = AsyncMock()
    mock_version_result.scalar_one_or_none = Mock(return_value=mock_version)
    stock_storage.db.execute = AsyncMock(side_effect=[mock_version_result])
    
    indicator_data = {
        "values": {"MA5": 10.5, "MA10": 11.0},
        "trade_date": datetime(2025, 3, 21)
    }
    
    result = await stock_storage.save_indicator(
        "000001", "MA", indicator_data["values"],
        indicator_data["trade_date"]
    )
    
    assert isinstance(result, StockIndicator)
    assert result.stock_code == "000001"
    assert result.indicator_type == "MA"
    assert result.version_hash == mock_version.version_hash
    stock_storage.db.add.assert_called_once()
    stock_storage.db.commit.assert_called_once()

@pytest.mark.asyncio
async def test_save_indicator_no_version(stock_storage):
    """测试保存技术指标时没有有效版本的情况"""
    # Mock get_current_indicator_version to return None
    mock_version_result = AsyncMock()
    mock_version_result.scalar_one_or_none = Mock(return_value=None)
    stock_storage.db.execute = AsyncMock(return_value=mock_version_result)
    
    with pytest.raises(DatabaseException) as exc_info:
        await stock_storage.save_indicator(
            "000001", "MA",
            {"MA5": 10.5},
            datetime(2025, 3, 21)
        )
    
    assert "没有有效的版本信息" in str(exc_info.value)

@pytest.mark.asyncio
async def test_get_indicators_success(stock_storage):
    """测试成功获取技术指标数据"""
    mock_indicator = StockIndicator(
        stock_code="000001",
        indicator_type="MA",
        version_hash="test_hash",
        values={"MA5": 10.5},
        trade_date=datetime(2025, 3, 21)
    )
    
    mock_result = AsyncMock()
    mock_result.scalars = Mock()
    mock_result.scalars.return_value = Mock(all=lambda: [mock_indicator])
    stock_storage.db.execute = AsyncMock(return_value=mock_result)
    
    result = await stock_storage.get_indicators(
        "000001", "MA",
        start_date="2025-03-21",
        end_date="2025-03-21"
    )
    
    assert len(result) == 1
    assert result[0] == mock_indicator
    stock_storage.db.execute.assert_called_once()

@pytest.mark.asyncio
async def test_save_indicator_version_success(stock_storage):
    """测试成功保存指标版本信息"""
    version_data = {
        "indicator_type": "MA",
        "version_hash": "new_hash",
        "is_current": True,
        "parameters": {"window": 5}
    }
    
    # Mock当前版本查询结果
    mock_current_version = IndicatorVersion(
        indicator_type="MA",
        version_hash="old_hash",
        is_current=True
    )
    
    # Mock get_current_version and update operation
    mock_current_result = AsyncMock()
    mock_current_result.scalar_one_or_none = Mock(return_value=mock_current_version)
    mock_update_result = AsyncMock()
    stock_storage.db.execute = AsyncMock(side_effect=[mock_current_result, mock_update_result])
    
    result = await stock_storage.save_indicator_version(version_data)
    
    assert isinstance(result, IndicatorVersion)
    assert result.indicator_type == version_data["indicator_type"]
    assert result.version_hash == version_data["version_hash"]
    assert hasattr(result, 'is_current')  # 避免直接比较SQLAlchemy的布尔值
    assert result.parameters == version_data["parameters"]
    stock_storage.db.execute.assert_called()  # 确认执行了查询和更新操作
    stock_storage.db.add.assert_called_once()
    stock_storage.db.commit.assert_called_once()
