"""麦睿数据提供者测试"""
import pytest
from datetime import datetime
from unittest.mock import AsyncMock, patch, MagicMock

from app.services.data_fetcher.provider import (
    MairuiDataFetcher,
    MairuiAdapter
)

# 测试数据
MOCK_STOCK_LIST = [
    {
        "dm": "000001",
        "mc": "平安银行",
        "jys": "sz"
    },
    {
        "dm": "600000",
        "mc": "浦发银行",
        "jys": "sh"
    }
]

MOCK_DAILY_DATA = [
    {
        "d": "2024-03-20",
        "o": "10.50",
        "h": "10.88",
        "l": "10.45",
        "c": "10.78",
        "v": "1000",  # 1000手
        "e": "1078000",
        "zf": "4.11",
        "hs": "2.5",
        "zd": "2.66",
        "zde": "0.28"
    }
]

MOCK_REALTIME_DATA = [
    {
        "fm": "0.5",      # 五分钟涨跌幅
        "h": "10.88",     # 最高价
        "hs": "2.5",      # 换手
        "lb": "1.2",      # 量比
        "l": "10.45",     # 最低价
        "lt": "1000000",  # 流通市值
        "o": "10.50",     # 开盘价
        "pe": "15.5",     # 市盈率
        "pc": "2.66",     # 涨跌幅
        "p": "10.78",     # 当前价格
        "sz": "2000000",  # 总市值
        "cje": "1078000", # 成交额
        "ud": "0.28",     # 涨跌额
        "v": "1000",      # 成交量(手)
        "yc": "10.50",    # 昨收价
        "zf": "4.11",     # 振幅
        "zs": "0.8",      # 涨速
        "sjl": "1.2",     # 市净率
        "zdf60": "10.5",  # 60日涨跌幅
        "zdfnc": "15.2",  # 年初至今涨跌幅
        "t": "2024-03-20 14:30"  # 更新时间
    }
]

@pytest.fixture
def mairui_fetcher():
    """创建麦睿数据获取器实例"""
    return MairuiDataFetcher(licence="test-licence")

@pytest.fixture
def mairui_adapter():
    """创建麦睿数据适配器实例"""
    return MairuiAdapter()

@pytest.mark.asyncio
async def test_get_stock_list(mairui_fetcher):
    """测试获取股票列表"""
    with patch.object(mairui_fetcher, '_request', new_callable=AsyncMock) as mock_request:
        # 设置模拟返回数据
        mock_request.return_value = MOCK_STOCK_LIST
        
        # 调用方法
        result = await mairui_fetcher.get_stock_list()
        
        # 验证结果
        assert len(result) == 2
        assert result[0]["dm"] == "000001"
        assert result[1]["dm"] == "600000"
        
        # 验证API调用
        mock_request.assert_called_once_with(
            f"{mairui_fetcher.BASE_URL}/hslt/list/{mairui_fetcher.licence}"
        )

@pytest.mark.asyncio
async def test_get_daily_data(mairui_fetcher):
    """测试获取日线数据"""
    with patch.object(mairui_fetcher, '_request', new_callable=AsyncMock) as mock_request:
        mock_request.return_value = MOCK_DAILY_DATA
        
        # 测试不带日期参数
        result = await mairui_fetcher.get_daily_data("000001")
        assert len(result) == 1
        assert result[0]["d"] == "2024-03-20"
        
        # 测试带日期参数
        start_date = datetime(2024, 1, 1)
        end_date = datetime(2024, 3, 20)
        result = await mairui_fetcher.get_daily_data(
            "000001",
            start_date=start_date,
            end_date=end_date
        )
        assert len(result) == 1

@pytest.mark.asyncio
async def test_get_realtime_quotes(mairui_fetcher):
    """测试获取实时行情"""
    with patch.object(mairui_fetcher, '_request', new_callable=AsyncMock) as mock_request:
        mock_request.return_value = MOCK_REALTIME_DATA
        
        # 测试单个股票
        result = await mairui_fetcher.get_realtime_quotes(["000001"])
        assert len(result) == 1
        assert float(result[0]["p"]) == 10.78
        
        # 测试多个股票
        mock_request.return_value = MOCK_REALTIME_DATA
        result = await mairui_fetcher.get_realtime_quotes(["000001", "600000"])
        assert len(result) == 2

@pytest.mark.asyncio
async def test_error_handling(mairui_fetcher):
    """测试错误处理"""
    with patch.object(mairui_fetcher, '_request', new_callable=AsyncMock) as mock_request:
        # 模拟API错误
        mock_request.side_effect = RuntimeError("API Error")
        
        # 测试股票列表错误处理
        with pytest.raises(RuntimeError):
            await mairui_fetcher.get_stock_list()
        
        # 测试日线数据错误处理
        with pytest.raises(RuntimeError):
            await mairui_fetcher.get_daily_data("000001")
        
        # 测试实时行情错误处理
        result = await mairui_fetcher.get_realtime_quotes(["000001"])
        assert len(result) == 0  # 应返回空列表而不是抛出异常

@pytest.mark.asyncio
async def test_normalize_stock_info(mairui_adapter):
    """测试股票信息标准化"""
    raw_data = MOCK_STOCK_LIST[0]
    result = mairui_adapter.normalize_stock_info(raw_data)
    
    assert result["code"] == "000001"
    assert result["name"] == "平安银行"
    assert result["exchange"] == "SZ"
    assert result["full_code"] == "SZ000001"
    assert "industry" in result
    assert "listing_date" in result

@pytest.mark.asyncio
async def test_normalize_daily_data(mairui_adapter):
    """测试日线数据标准化"""
    raw_data = MOCK_DAILY_DATA[0]
    result = mairui_adapter.normalize_daily_data(raw_data)
    
    assert result["date"] == "2024-03-20"
    assert result["open"] == 10.50
    assert result["high"] == 10.88
    assert result["low"] == 10.45
    assert result["close"] == 10.78
    assert result["volume"] == 100000  # 1000手 * 100
    assert result["amount"] == 1078000
    assert result["turnover"] == 2.5
    assert result["change_pct"] == 2.66

@pytest.mark.asyncio
async def test_normalize_realtime_quote(mairui_adapter):
    """测试实时行情数据标准化"""
    raw_data = MOCK_REALTIME_DATA[0]
    result = mairui_adapter.normalize_realtime_quote(raw_data)
    
    # 测试基础字段
    assert result["price"] == 10.78
    assert result["change"] == 0.28
    assert result["change_pct"] == 2.66
    assert result["volume"] == 100000  # 1000手 * 100
    assert result["amount"] == 1078000
    assert isinstance(result["time"], datetime)
    
    # 测试扩展字段
    assert result["open"] == 10.50
    assert result["high"] == 10.88
    assert result["low"] == 10.45
    assert result["prev_close"] == 10.50
    assert result["turnover"] == 2.5
    assert result["volume_ratio"] == 1.2
    assert result["amplitude"] == 4.11
    assert result["market_value"] == 2000000
    assert result["circulating_value"] == 1000000
    assert result["pe_ratio"] == 15.5
    assert result["pb_ratio"] == 1.2
    assert result["speed"] == 0.8
    assert result["change_60d"] == 10.5
    assert result["change_ytd"] == 15.2
    assert result["change_5m"] == 0.5

@pytest.mark.asyncio
async def test_parse_datetime(mairui_adapter):
    """测试日期解析功能"""
    # 测试完整格式 YYYY-MM-DD HH:MM:SS
    full_datetime = "2024-03-20 14:30:45"
    result = mairui_adapter._parse_datetime(full_datetime)
    assert isinstance(result, datetime)
    assert result.year == 2024
    assert result.month == 3
    assert result.day == 20
    assert result.hour == 14
    assert result.minute == 30
    assert result.second == 45
    
    # 测试简短格式 YYYY-MM-DD HH:MM
    short_datetime = "2024-03-20 14:30"
    result = mairui_adapter._parse_datetime(short_datetime)
    assert isinstance(result, datetime)
    assert result.year == 2024
    assert result.month == 3
    assert result.day == 20
    assert result.hour == 14
    assert result.minute == 30
    assert result.second == 0
    
    # 测试空字符串
    with pytest.raises(ValueError, match="日期字符串为空"):
        mairui_adapter._parse_datetime("")
    
    # 测试无效格式
    with pytest.raises(ValueError, match="不支持的日期格式"):
        mairui_adapter._parse_datetime("2024-03-20")

@pytest.mark.asyncio
async def test_normalize_realtime_quote_error_handling(mairui_adapter):
    """测试实时行情数据标准化的错误处理"""
    # 测试空数据
    result = mairui_adapter.normalize_realtime_quote({})
    assert result == {}
    
    # 测试无效数据类型
    result = mairui_adapter.normalize_realtime_quote({
        "p": "invalid",
        "t": "invalid"
    })
    assert result == {}

@pytest.mark.asyncio
async def test_session_management(mairui_fetcher):
    """测试HTTP会话管理"""
    # 测试会话创建
    session = await mairui_fetcher._ensure_session()
    assert session is not None
    
    # 测试会话复用
    session2 = await mairui_fetcher._ensure_session()
    assert session2 is session
    
    # 测试会话关闭
    await mairui_fetcher.close()
    assert mairui_fetcher._session is None
