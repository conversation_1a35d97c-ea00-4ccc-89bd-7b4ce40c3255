"""测试数据适配器"""
import pytest
from datetime import datetime
from typing import Dict, Any
from unittest.mock import Mock, patch, AsyncMock

from app.services.data_fetcher.adapter import (
    BaseDataAdapter,
    AdaptedDataFetcher,
    retry_on_error
)
from app.services.data_fetcher.base import DataFetcher

@pytest.mark.asyncio
class TestBaseDataAdapter:
    """测试基础数据适配器"""

    @pytest.fixture
    def adapter(self):
        return BaseDataAdapter()

    async def test_normalize_stock_info(self, adapter):
        """测试股票信息标准化"""
        raw_data = {
            "code": "600000",
            "name": "浦发银行",
            "industry": "银行",
            "market": "SH",
            "list_date": "19991110"
        }
        
        normalized = adapter.normalize_stock_info(raw_data)
        
        assert normalized["code"] == "600000"
        assert normalized["name"] == "浦发银行"
        assert normalized["industry"] == "银行"
        assert normalized["market"] == "SH"
        assert normalized["list_date"] == "19991110"

    async def test_normalize_daily_data(self, adapter):
        """测试日线数据标准化"""
        raw_data = {
            "date": "2021-01-04",
            "open": "10.0",
            "high": "10.5",
            "low": "9.8",
            "close": "10.2",
            "volume": "100000",
            "amount": "1020000.0",
            "turnover": "2.5",
            "change_pct": "1.5"
        }
        
        normalized = adapter.normalize_daily_data(raw_data)
        
        assert normalized["open"] == 10.0
        assert normalized["high"] == 10.5
        assert normalized["low"] == 9.8
        assert normalized["close"] == 10.2
        assert normalized["volume"] == 100000.0
        assert normalized["amount"] == 1020000.0
        assert normalized["turnover"] == 2.5
        assert normalized["change_pct"] == 1.5

    async def test_normalize_realtime_quote(self, adapter):
        """测试实时行情数据标准化"""
        raw_data = {
            "code": "600000",
            "price": "10.2",
            "change": "0.2",
            "change_pct": "1.5",
            "volume": "50000",
            "amount": "510000.0",
            "time": datetime.now()
        }
        
        normalized = adapter.normalize_realtime_quote(raw_data)
        
        assert normalized["code"] == "600000"
        assert normalized["price"] == 10.2
        assert normalized["change"] == 0.2
        assert normalized["change_pct"] == 1.5
        assert normalized["volume"] == 50000.0
        assert normalized["amount"] == 510000.0
        assert isinstance(normalized["time"], datetime)

@pytest.mark.asyncio
class TestAdaptedDataFetcher:
    """测试适配后的数据获取器"""

    @pytest.fixture
    def mock_fetcher(self):
        fetcher = AsyncMock(spec=DataFetcher)
        fetcher.get_stock_list.return_value = [
            {"symbol": "600000", "name": "浦发银行"}
        ]
        return fetcher

    @pytest.fixture
    def mock_adapter(self):
        adapter = Mock(spec=BaseDataAdapter)
        adapter.normalize_stock_info.return_value = {
            "code": "600000",
            "name": "浦发银行",
            "industry": "",
            "market": "",
            "list_date": ""
        }
        return adapter

    async def test_get_stock_list(self, mock_fetcher, mock_adapter):
        """测试获取股票列表的适配过程"""
        adapted_fetcher = AdaptedDataFetcher(mock_fetcher, mock_adapter)
        result = await adapted_fetcher.get_stock_list()
        
        # 验证原始获取器被调用
        mock_fetcher.get_stock_list.assert_called_once()
        
        # 验证适配器被调用进行数据转换
        mock_adapter.normalize_stock_info.assert_called_once_with(
            {"symbol": "600000", "name": "浦发银行"}
        )
        
        # 验证返回结果
        assert len(result) == 1
        assert result[0]["code"] == "600000"
        assert result[0]["name"] == "浦发银行"

@pytest.mark.asyncio
class TestRetryDecorator:
    """测试重试装饰器"""

    async def test_successful_call(self):
        """测试成功调用"""
        mock_func = AsyncMock(return_value="success")
        
        @retry_on_error()
        async def test_func():
            return await mock_func()
        
        result = await test_func()
        assert result == "success"
        assert mock_func.call_count == 1

    async def test_retry_on_error(self):
        """测试失败重试"""
        mock_func = AsyncMock()
        mock_func.side_effect = [
            Exception("First error"),
            Exception("Second error"),
            "success"
        ]
        
        @retry_on_error(max_retries=3, delay=0.1)
        async def test_func():
            return await mock_func()
        
        result = await test_func()
        assert result == "success"
        assert mock_func.call_count == 3

    async def test_max_retries_exceeded(self):
        """测试超过最大重试次数"""
        mock_func = AsyncMock(side_effect=Exception("Persistent error"))
        
        @retry_on_error(max_retries=3, delay=0.1)
        async def test_func():
            return await mock_func()
        
        with pytest.raises(Exception) as exc_info:
            await test_func()
        
        assert str(exc_info.value) == "Persistent error"
        assert mock_func.call_count == 3
