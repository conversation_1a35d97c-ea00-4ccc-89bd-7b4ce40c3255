"""
测试app/main.py的功能
"""
import pytest
from fastapi.testclient import TestClient
from fastapi.middleware.cors import CORSMiddleware
from app.main import app
from app.core.config import settings

@pytest.fixture
def client():
    """创建测试客户端"""
    return TestClient(app)

def test_app_title():
    """测试应用标题配置"""
    assert app.title == settings.APP_NAME
    assert app.description == "股票量化分析系统API"
    assert app.version == settings.APP_VERSION

def test_cors_middleware(client):
    """测试CORS中间件配置"""
    response = client.options(
        "/",
        headers={
            "Origin": "http://localhost",
            "Access-Control-Request-Method": "GET",
        },
    )
    assert response.status_code == 200
    assert response.headers["access-control-allow-origin"] == "http://localhost"
    assert response.headers["access-control-allow-credentials"] == "true"

def test_root_endpoint(client):
    """测试根路由"""
    response = client.get("/")
    assert response.status_code == 200
    assert response.json() == {"message": f"欢迎使用{settings.APP_NAME} API"}

def test_health_check(client):
    """测试健康检查接口"""
    response = client.get("/health")
    assert response.status_code == 200
    assert response.json() == {
        "status": "healthy",
        "version": settings.APP_VERSION
    }

def test_process_time_header(client):
    """测试请求处理时间中间件"""
    response = client.get("/")
    assert "X-Process-Time" in response.headers
    process_time = float(response.headers["X-Process-Time"])
    assert process_time > 0

def test_global_exception_handler(client):
    """测试全局异常处理"""
    response = client.get("/non_existent_path")
    assert response.status_code == 404
    assert "detail" in response.json()

@pytest.mark.asyncio
async def test_startup_event():
    """测试启动事件"""
    # 由于startup_event已经在应用启动时执行过，这里我们只需验证数据库表是否存在
    from app.core.database import engine
    from sqlalchemy import inspect
    from app.models import stock, partition
    
    # 检查所有表是否已创建
    # 对于异步引擎，需要使用异步上下文管理器获取连接
    def get_tables(conn):
        inspector = inspect(conn)
        return inspector.get_table_names()
    
    async with engine.begin() as conn:
        actual_tables = await conn.run_sync(get_tables)
    expected_tables = ['stock_info', 'stock_indicator', 'indicator_version']
    for table in expected_tables:
        assert table in actual_tables, f"表 {table} 未创建，当前存在的表: {actual_tables}"

@pytest.mark.asyncio
async def test_shutdown_event():
    """测试关闭事件
    
    注意：这是一个基本的测试，因为实际的关闭逻辑还未实现（见TODO注释）
    """
    from app.main import shutdown_event
    await shutdown_event()
    # 由于目前shutdown_event只是记录日志，这里仅验证函数可以正常执行
    assert True
