"""
高级技术指标API端点测试

测试新添加的高级技术指标API接口
"""
import pytest
from fastapi.testclient import TestClient
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime, timedelta

from app.main import app
from app.services.indicators.advanced_indicator_service import AdvancedIndicatorService


class TestAdvancedIndicatorsAPI:
    """高级技术指标API测试类"""
    
    @pytest.fixture
    def client(self):
        """创建测试客户端"""
        return TestClient(app)
    
    @pytest.fixture
    def mock_complete_indicators_response(self):
        """模拟完整指标响应数据"""
        return {
            "period_type": "dh",
            "data_points": 100,
            "date_range": {
                "start": "2023-01-01T00:00:00",
                "end": "2023-12-31T00:00:00"
            },
            "price_bollinger": {
                "middle": [100.0] * 100,
                "upper": [110.0] * 100,
                "lower": [90.0] * 100
            },
            "volume_analysis": {
                "in_volume": [50000.0] * 100,
                "ex_volume": [50000.0] * 100,
                "volume_difference": [1000.0] * 100
            },
            "volume_bollinger": {
                "middle": [1000.0] * 100,
                "upper": [1200.0] * 100,
                "lower": [800.0] * 100,
                "smoothed_volume": [1000.0] * 100
            },
            "kdj_indicators": {
                "K": [50.0] * 100,
                "D": [50.0] * 100,
                "J": [50.0] * 100
            },
            "trading_signals": {
                "buy_signals": [10, 30, 50],
                "sell_signals": [20, 40, 60]
            },
            "parameters": {
                "bollinger_window": 20,
                "lag": 2,
                "period_index": 1
            }
        }
    
    @patch('app.api.endpoints.advanced_indicators.get_advanced_indicator_service')
    def test_get_complete_indicators_success(self, mock_service_dep, client, mock_complete_indicators_response):
        """测试完整指标获取成功"""
        # 模拟服务依赖
        mock_service = AsyncMock()
        mock_service.calculate_complete_indicators.return_value = mock_complete_indicators_response
        mock_service_dep.return_value = mock_service
        
        # 发送请求
        response = client.get(
            "/api/v1/advanced-indicators/complete/000001",
            params={
                "start_date": "2023-01-01",
                "end_date": "2023-12-31",
                "period_index": 1,
                "bollinger_window": 20,
                "lag": 2
            }
        )
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        
        # 检查响应数据结构
        assert "period_type" in data
        assert "data_points" in data
        assert "price_bollinger" in data
        assert "volume_analysis" in data
        assert "kdj_indicators" in data
        assert "trading_signals" in data
        
        # 检查具体数据
        assert data["period_type"] == "dh"
        assert data["data_points"] == 100
        assert len(data["price_bollinger"]["middle"]) == 100
        assert len(data["trading_signals"]["buy_signals"]) == 3
    
    @patch('app.api.endpoints.advanced_indicators.get_advanced_indicator_service')
    def test_get_complete_indicators_default_params(self, mock_service_dep, client, mock_complete_indicators_response):
        """测试使用默认参数的完整指标获取"""
        mock_service = AsyncMock()
        mock_service.calculate_complete_indicators.return_value = mock_complete_indicators_response
        mock_service_dep.return_value = mock_service
        
        response = client.get("/api/v1/advanced-indicators/complete/000001")
        
        assert response.status_code == 200
        data = response.json()
        assert "period_type" in data
    
    @patch('app.api.endpoints.advanced_indicators.get_advanced_indicator_service')
    def test_get_bollinger_bands_success(self, mock_service_dep, client):
        """测试Bollinger Bands获取成功"""
        # 模拟服务和数据
        mock_service = AsyncMock()
        mock_df = MagicMock()
        mock_service.get_stock_data.return_value = mock_df
        mock_service.calculate_bollinger_bands.return_value = {
            'middle': MagicMock(tolist=lambda: [100.0] * 50),
            'upper': MagicMock(tolist=lambda: [110.0] * 50),
            'lower': MagicMock(tolist=lambda: [90.0] * 50)
        }
        mock_service_dep.return_value = mock_service
        
        response = client.get(
            "/api/v1/advanced-indicators/bollinger/000001",
            params={
                "window": 20,
                "std_dev": 2.0
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert "middle" in data
        assert "upper" in data
        assert "lower" in data
        assert len(data["middle"]) == 50
    
    @patch('app.api.endpoints.advanced_indicators.get_advanced_indicator_service')
    def test_get_volume_analysis_success(self, mock_service_dep, client):
        """测试成交量分析获取成功"""
        mock_service = AsyncMock()
        mock_df = MagicMock()
        mock_service.get_stock_data.return_value = mock_df
        mock_service.calculate_volume_inout_difference.return_value = {
            'in_vol': MagicMock(tolist=lambda: [50000.0] * 50),
            'ex_vol': MagicMock(tolist=lambda: [50000.0] * 50),
            'diff_vol': MagicMock(tolist=lambda: [1000.0] * 50)
        }
        mock_service_dep.return_value = mock_service
        
        response = client.get("/api/v1/advanced-indicators/volume-analysis/000001")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "in_volume" in data
        assert "ex_volume" in data
        assert "volume_difference" in data
        assert len(data["in_volume"]) == 50
    
    @patch('app.api.endpoints.advanced_indicators.get_advanced_indicator_service')
    def test_get_volume_bollinger_success(self, mock_service_dep, client):
        """测试成交量Bollinger获取成功"""
        mock_service = AsyncMock()
        mock_df = MagicMock()
        mock_service.get_stock_data.return_value = mock_df
        mock_service.calculate_volume_inout_difference.return_value = {
            'diff_vol': MagicMock()
        }
        mock_service.calculate_volume_bollinger.return_value = {
            'vol_middle': MagicMock(tolist=lambda: [1000.0] * 50),
            'vol_upper': MagicMock(tolist=lambda: [1200.0] * 50),
            'vol_lower': MagicMock(tolist=lambda: [800.0] * 50),
            'log_vol': MagicMock(tolist=lambda: [1000.0] * 50)
        }
        mock_service_dep.return_value = mock_service
        
        response = client.get(
            "/api/v1/advanced-indicators/volume-bollinger/000001",
            params={
                "window": 20,
                "lag": 2
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert "middle" in data
        assert "upper" in data
        assert "lower" in data
        assert "smoothed_volume" in data
        assert len(data["middle"]) == 50
    
    @patch('app.api.endpoints.advanced_indicators.get_advanced_indicator_service')
    def test_get_kdj_enhanced_success(self, mock_service_dep, client):
        """测试增强版KDJ获取成功"""
        mock_service = AsyncMock()
        mock_df = MagicMock()
        mock_service.get_stock_data.return_value = mock_df
        mock_service.calculate_kdj.return_value = {
            'K': MagicMock(tolist=lambda: [50.0] * 50),
            'D': MagicMock(tolist=lambda: [50.0] * 50),
            'J': MagicMock(tolist=lambda: [50.0] * 50)
        }
        mock_service_dep.return_value = mock_service
        
        response = client.get(
            "/api/v1/advanced-indicators/kdj-enhanced/000001",
            params={
                "k_period": 9,
                "d_period": 3,
                "j_period": 3
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert "K" in data
        assert "D" in data
        assert "J" in data
        assert len(data["K"]) == 50
    
    @patch('app.api.endpoints.advanced_indicators.get_advanced_indicator_service')
    def test_get_trading_signals_success(self, mock_service_dep, client):
        """测试交易信号获取成功"""
        mock_service = AsyncMock()
        mock_df = MagicMock()
        mock_service.get_stock_data.return_value = mock_df
        mock_service.calculate_volume_inout_difference.return_value = {'diff_vol': MagicMock()}
        mock_service.calculate_volume_bollinger.return_value = {}
        mock_service.calculate_kdj.return_value = {}
        mock_service.calculate_buy_sell_signals.return_value = {
            'buy_signals': [10, 30, 50],
            'sell_signals': [20, 40, 60]
        }
        mock_service_dep.return_value = mock_service
        
        response = client.get("/api/v1/advanced-indicators/trading-signals/000001")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "buy_signals" in data
        assert "sell_signals" in data
        assert len(data["buy_signals"]) == 3
        assert len(data["sell_signals"]) == 3
    
    def test_get_available_periods_success(self, client):
        """测试获取可用周期列表成功"""
        response = client.get("/api/v1/advanced-indicators/periods")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "periods" in data
        periods = data["periods"]
        assert len(periods) == 6
        
        # 检查每个周期的结构
        for period in periods:
            assert "index" in period
            assert "code" in period
            assert "name" in period
            assert "description" in period
        
        # 检查特定周期
        daily_period = next(p for p in periods if p["index"] == 1)
        assert daily_period["code"] == "dh"
        assert daily_period["name"] == "日线"
    
    def test_invalid_stock_code(self, client):
        """测试无效股票代码"""
        response = client.get("/api/v1/advanced-indicators/complete/INVALID")
        # 根据实际错误处理机制，这里可能返回400或500
        assert response.status_code in [400, 500]
    
    def test_invalid_parameters(self, client):
        """测试无效参数"""
        # 测试超出范围的period_index
        response = client.get(
            "/api/v1/advanced-indicators/complete/000001",
            params={"period_index": 7}  # 超出有效范围1-6
        )
        assert response.status_code == 422  # 参数验证错误
        
        # 测试无效的window大小
        response = client.get(
            "/api/v1/advanced-indicators/bollinger/000001",
            params={"window": 200}  # 超出有效范围5-100
        )
        assert response.status_code == 422
    
    def test_invalid_date_format(self, client):
        """测试无效日期格式"""
        response = client.get(
            "/api/v1/advanced-indicators/complete/000001",
            params={
                "start_date": "2023/01/01",  # 错误格式
                "end_date": "2023-12-31"
            }
        )
        # 应该能通过，因为日期验证在查询参数层面比较宽松
        # 但如果在服务层有验证，可能会返回错误
    
    @patch('app.api.endpoints.advanced_indicators.get_advanced_indicator_service')
    def test_service_error_handling(self, mock_service_dep, client):
        """测试服务层错误处理"""
        mock_service = AsyncMock()
        mock_service.calculate_complete_indicators.side_effect = Exception("计算失败")
        mock_service_dep.return_value = mock_service
        
        response = client.get("/api/v1/advanced-indicators/complete/000001")
        
        assert response.status_code == 500
        data = response.json()
        assert "detail" in data
        assert "计算失败" in data["detail"]
    
    @patch('app.core.cache.get_cached_result')
    @patch('app.core.cache.cache_result')
    @patch('app.api.endpoints.advanced_indicators.get_advanced_indicator_service')
    def test_caching_mechanism(self, mock_service_dep, mock_cache_result, mock_get_cached, client, mock_complete_indicators_response):
        """测试缓存机制"""
        # 第一次请求 - 无缓存
        mock_get_cached.return_value = None
        mock_service = AsyncMock()
        mock_service.calculate_complete_indicators.return_value = mock_complete_indicators_response
        mock_service_dep.return_value = mock_service
        
        response = client.get("/api/v1/advanced-indicators/complete/000001")
        
        assert response.status_code == 200
        # 验证设置了缓存
        mock_cache_result.assert_called_once()
        
        # 第二次请求 - 有缓存
        mock_get_cached.return_value = mock_complete_indicators_response
        mock_cache_result.reset_mock()
        
        response = client.get("/api/v1/advanced-indicators/complete/000001")
        
        assert response.status_code == 200
        # 验证没有调用缓存设置（因为直接返回了缓存）
        mock_cache_result.assert_not_called()


class TestAdvancedIndicatorsAPIIntegration:
    """高级技术指标API集成测试"""
    
    @pytest.mark.asyncio
    async def test_full_api_workflow(self):
        """测试完整API工作流程"""
        # 这需要真实的数据库和服务
        pass
    
    def test_api_performance(self):
        """测试API性能"""
        # 性能测试
        pass
    
    def test_concurrent_requests(self):
        """测试并发请求"""
        # 并发测试
        pass


# 运行测试的示例
if __name__ == "__main__":
    pytest.main([__file__, "-v"])
