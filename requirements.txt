aiohappyeyeballs==2.6.1
aiohttp==3.11.13
aiosignal==1.3.2
aiosqlite==0.21.0
akracer==0.0.13
akshare==1.16.72
alembic==1.15.1
annotated-types==0.7.0
anyio==4.9.0
APScheduler==3.8.1
asttokens==3.0.0
attrs==25.3.0
bcrypt==4.0.1
beautifulsoup4==4.13.3
black==25.1.0
bs4==0.0.2
certifi==2025.1.31
charset-normalizer==3.4.1
click==8.1.8
colorama==0.4.6
coverage==7.7.0
decorator==5.2.1
dnspython==2.7.0
email_validator==2.2.0
et_xmlfile==2.0.0
executing==2.2.0
fastapi==0.115.11
flake8==7.1.2
frozendict==2.4.6
frozenlist==1.5.0
gitdb==4.0.12
GitPython==3.1.44
greenlet==3.1.1
h11==0.14.0
html5lib==1.1
httpcore==1.0.7
httpx==0.28.1
httpx-sse==0.4.0
idna==3.10
iniconfig==2.0.0
ipdb==0.13.13
ipython==9.0.2
ipython_pygments_lexers==1.1.1
isort==6.0.1
jedi==0.19.2
jsonpath==0.82.2
lxml==5.3.1
Mako==1.3.9
MarkupSafe==3.0.2
matplotlib-inline==0.1.7
mccabe==0.7.0
mcp==1.5.0
mcp-server-git==2025.1.14
mini-racer==0.12.4
multidict==6.2.0
multitasking==0.0.11
mypy==1.15.0
mypy-extensions==1.0.0
nest-asyncio==1.6.0
numpy==2.2.4
openpyxl==3.1.5
packaging==24.2
pandas==2.2.3
pandas_ta==0.3.14b0
parso==0.8.4
passlib==1.7.4
pathspec==0.12.1
peewee==3.17.9
pexpect==4.9.0
platformdirs==4.3.6
pluggy==1.5.0
prompt_toolkit==3.0.50
propcache==0.3.0
psycopg2-binary==2.9.10
ptyprocess==0.7.0
pure_eval==0.2.3
py-mini-racer==0.6.0
pycodestyle==2.12.1
pydantic==2.11.0b2
pydantic-settings==2.8.1
pydantic_core==2.32.0
pyflakes==3.2.0
Pygments==2.19.1
pytest==7.4.0
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-lazy-fixture==0.6.3
pytest-mock==3.11.1
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
pytz==2025.1
requests==2.32.3
setuptools==76.1.0
simplejson==3.20.1
six==1.17.0
smmap==5.0.2
sniffio==1.3.1
soupsieve==2.6
SQLAlchemy==2.0.39
sse-starlette==2.2.1
stack-data==0.6.3
starlette==0.46.1
tabulate==0.9.0
tenacity==8.5.0
tqdm==4.67.1
traitlets==5.14.3
tushare==1.4.19
typing-inspection==0.4.0
typing_extensions==4.13.0rc1
tzdata==2025.1
tzlocal==2.1
urllib3==2.3.0
uv==0.6.9
uvicorn==0.34.0
wcwidth==0.2.13
webencodings==0.5.1
websocket-client==1.8.0
xlrd==2.0.1
yarl==1.18.3
yfinance==0.2.54
