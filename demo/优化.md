量化交易策略最终交付文档
版本: 1.0
日期: 2025年6月14日
策略名称: 成交量压力与动量双驱策略 (Volume-Momentum Dual-Thrust Strategy)
一、 量化策略最终说明书 (Final Strategy Description)
1. 策略概述
本策略是一个旨在A股市场中捕捉中期上涨趋势的高确定性动量策略。其核心思想是通过双重验证机制，即市场买卖情绪与价格动量必须同时确认进入强势状态时，才产生买入信号。策略集成了完整的资金管理、风险控制和退出机制，构成一个完整的交易系统。
2. 策略逻辑
•	核心逻辑: 寻找“成交量压力指标显示买方力量占据优势” 并且 “KDJ指标显示价格进入上涨动能”的精确时刻。
•	适应市场: 中国A股市场。
•	交易周期: 日线 (dh) 或 周线 (wh)。
3. 交易系统构成
3.1. 核心指标
1.	价格布林带 (Price Bollinger Bands): 周期为20，用于设定动态止损位。
2.	成交量压力指标 (Volume Pressure): 基于内外盘成交量差值计算并经指数移动平均（EMA）平滑处理的自定义指标。
3.	成交量布林带 (Volume Bollinger Bands): 对“成交量压力指标”再次计算布林带，其中轨代表了近期买卖情绪的均衡线。
4.	KDJ指标: 经典的动量震荡指标。
3.2. 信号系统
•	买入信号 (高精度 AND 逻辑)
必须同时满足以下两个条件：
1.	成交量压力突破: 当日的“成交量压力指标” (log_vol) 从下向上穿过其布林带中轨 (average_vol)。
2.	KDJ金叉: 当日的KDJ指标发生金叉，即 price_k 从下向上穿过 price_d。
•	卖出（止盈）信号 (OR 逻辑)
满足以下任一条件即可触发止盈：
1.	成交量压力衰竭: 当日的“成交量压力指标” (log_vol) 从上向下穿过其布林带中轨 (average_vol)。
2.	KDJ死叉: 当日的KDJ指标发生死叉，即 price_k 从上向下穿过 price_d。
•	止损信号 (最后防线)
o	规则: 持仓期间，若任一交易日的收盘价跌破了当日的价格布林带下轨 (dis_Lowr)，则在下一个交易日无条件卖出。
3.3. 风险与资金管理
1.	股票池: 策略运行于一个预先筛选的股票池，剔除了低流动性、高风险及市值过大/过小的股票。
2.	仓位分配: 采用等金额分配原则。例如，总资金100万，最多持仓10只，则单只股票分配资金10万元。
3.	流动性控制: 单笔下单量不得超过目标股票过去20日平均成交量的1%，以避免冲击成本并保证可执行性。
4. 交易流程
1.	选池: 加载预设的股票池。
2.	择时: 每个交易日收盘后，对池中所有未持仓股票进行扫描，寻找满足买入 AND 逻辑的股票。
3.	执行买入: 若发现信号，根据仓位分配和流动性控制规则计算股数，在次日开盘时下单买入。
4.	监控: 对已持仓股票，每日监控止盈 OR 逻辑和布林带下轨止损位。
5.	执行退出: 若触发止盈或止损信号，在次日开盘时下单卖出。
二、 策略开发文档 (Development Document)
1. 开发概述
本文档旨在为技术开发人员提供实现“成交量压力与动量双驱策略”所需的技术细节和逻辑流程。
2. 数据需求
•	输入数据: 标准OHLCV（开盘价, 最高价, 最低价, 收盘价, 成交量）及交易日期。需要至少包含过去30个交易日的数据以完成指标初始化计算。
•	数据源: 可靠的金融数据API，能够提供准确的A股历史日线/周线数据及内外盘数据（或提供可用于计算内外盘的逐笔成交数据）。
3. 指标计算模块 (indicators.py)
需要实现以下函数来计算各项指标：
1.	calculate_price_bollinger(prices, window=20, std_dev=2):
o	输入: 收盘价序列。
o	输出: 上轨、中轨、下轨 (dis_Lowr) 序列。
2.	calculate_volume_pressure(tick_data, ema_period):
o	输入: 逐笔成交数据或内外盘数据，EMA平滑周期 (lag*2)。
o	处理: 计算 ex_vol - in_vol，进行标准化处理，然后应用EMA平滑。
o	输出: log_vol 序列。
3.	calculate_volume_bollinger(volume_pressure_series, window=20, std_dev=2):
o	输入: log_vol 序列。
o	输出: 成交量布林带的中轨 (average_vol) 序列。
4.	calculate_kdj(highs, lows, closes, n=9, m1=3, m2=3):
o	输入: 最高价、最低价、收盘价序列。
o	输出: price_k, price_d, price_j 序列。
4. 核心交易逻辑 (strategy_logic.py)
# Pseudo-code for core logic
class DualThrustStrategy:
    def __init__(self, stock_data, balance, max_positions):
        self.data = stock_data # All data for the stock pool
        self.balance = balance
        self.max_positions = max_positions
        self.portfolio = {} # Holds current positions

    def run_daily_scan(self, trade_date):
        # 1. Scan for selling/stop-loss signals in current portfolio
        for stock_code in list(self.portfolio.keys()):
            stock_series = self.data[stock_code]
            
            # Check Stop-Loss (Highest Priority)
            if self.check_stop_loss(stock_series, trade_date):
                self.execute_sell(stock_code, trade_date, "Stop-Loss")
                continue

            # Check Take-Profit
            if self.check_take_profit_signal(stock_series, trade_date):
                self.execute_sell(stock_code, trade_date, "Take-Profit")

        # 2. Scan for buying signals in stock pool
        available_slots = self.max_positions - len(self.portfolio)
        if available_slots > 0:
            for stock_code in self.data.keys():
                if stock_code not in self.portfolio:
                    stock_series = self.data[stock_code]
                    if self.check_buy_signal(stock_series, trade_date):
                        self.execute_buy(stock_code, trade_date)
                        if len(self.portfolio) >= self.max_positions:
                            break

    def check_buy_signal(self, series, date):
        # AND Logic Implementation
        vol_pressure_breakout = (series['log_vol'][date-1] < series['average_vol'][date-1] and 
                                 series['log_vol'][date] > series['average_vol'][date])
        
        kdj_golden_cross = (series['price_k'][date-1] < series['price_d'][date-1] and 
                            series['price_k'][date] > series['price_d'][date])
        
        return vol_pressure_breakout and kdj_golden_cross

    def check_take_profit_signal(self, series, date):
        # OR Logic Implementation
        vol_pressure_exhaustion = (series['log_vol'][date-1] > series['average_vol'][date-1] and 
                                   series['log_vol'][date] < series['average_vol'][date])
        
        kdj_death_cross = (series['price_k'][date-1] > series['price_d'][date-1] and 
                           series['price_k'][date] < series['price_d'][date])
                           
        return vol_pressure_exhaustion or kdj_death_cross

    def check_stop_loss(self, series, date):
        return series['close'][date] < series['price_bollinger_lower'][date]

    def execute_buy(self, stock_code, date):
        # Implement position sizing and liquidity checks here
        pass

    def execute_sell(self, stock_code, date, reason):
        # Implement sell logic here
        pass

三、 策略测试案例文档 (Test Case Document)
1. 测试目的
确保策略的各项指标计算准确，交易逻辑执行无误，风控规则有效。
2. 单元测试
测试模块	测试项	输入数据	预期输出
calculate_kdj	KDJ值计算	已知的OHLC序列	与主流金融软件计算出的KDJ值一致
calculate_price_bollinger	布林带计算	已知的收盘价序列	与主流金融软件计算出的BBands值一致
calculate_volume_pressure	成交量压力指标	已知的内外盘数据	指标曲线平滑，无异常尖峰
3. 集成测试
测试场景	条件描述	预期行为
买入：AND逻辑	仅KDJ金叉，成交量压力未突破	不产生买入信号
买入：AND逻辑	仅成交量压力突破，KDJ未金叉	不产生买入信号
买入：AND逻辑	KDJ金叉 且 成交量压力突破	成功产生买入信号
卖出：OR逻辑	仅KDJ死叉	成功产生止盈信号
卖出：OR逻辑	仅成交量压力衰竭	成功产生止盈信号
止损	股价收盘跌破布林带下轨	成功产生止损信号，优先于止盈信号
资金管理	某股票流动性不足	买入股数被限制在日均成交量的1%以内
4. 系统/回归测试
•	回测: 使用至少5年的历史数据对策略进行回测，评估其夏普比率、最大回撤、年化收益率等关键性能指标。
•	场景测试: 在牛市、熊市、震荡市等不同市场环境下分别进行回测，检验策略的适应性。
•	回归测试: 在代码有任何修改后，重新运行所有测试用例，确保未引入新的BUG。
四、 策略使用指导文档 (User Guide Document)
1. 策略简介
欢迎使用“成交量压力与动量双驱策略”！本策略是一个自动化的股票交易程序，它通过严格的条件筛选，旨在为您捕捉稳健的股票上涨机会。请注意：所有投资均有风险，本策略不保证盈利。
2. 首次配置
在运行策略前，您需要完成以下配置：
•	数据源配置: 在 config.ini 文件中填入您的金融数据API密钥。
•	交易账户配置: (如需实盘) 填入您的券商交易API相关信息。
•	资金配置:
o	total_capital: 您计划投入的总资金。
o	max_positions: 您希望同时持有的最大股票数量。
•	股票池配置: 在 stock_pool.csv 文件中，填入您希望策略监控的股票代码列表，每行一个。
3. 运行与监控
•	启动策略: 运行 main.py 文件即可启动策略。
•	python main.py

•	监控界面: 程序启动后，将在控制台实时打印日志。
o	INFO: 常规信息，如程序启动、每日扫描开始/结束。
o	BUY: 记录买入信号及下单详情。
o	SELL: 记录卖出信号（止盈/止损）及下单详情。
o	WARNING: 警告信息，如流动性不足导致下单量被限制。
•	停止策略: 在控制台按 Ctrl+C 即可安全停止程序。
4. 风险提示
•	市场风险: 任何量化策略都无法预测极端市场事件（如“黑天鹅”）。
•	流动性风险: 在市场流动性枯竭时，可能无法按计划价格完成交易。
•	技术风险: 程序BUG、网络中断、API故障等都可能导致非预期的交易行为。
•	建议: 在投入真实资金前，强烈建议您使用模拟盘或小资金运行本策略至少一个月，以熟悉其特性并确认其表现符合您的预期。

优化了买入部分的逻辑，补充了止盈止损条件和资金风控逻辑。
我感觉是OK的，它这个策略是自己研发了一个成交量压力指标，结合KDJ用来选股开仓。然后我用布林带的下轨用来止损，成交量压力指标和KDJ的反向用来止盈。仓位分成10份，每份占比不超过目标股的日成交的1%，减少冲击。如果只是用在A股的话，直接选日K做周期就好了，其他周期全部不用。周线和月线太长了，不适用。小时线分钟线没办法在T+1市场里面灵活买卖，也不适用。这个买卖点可以标注成建议买卖，不用自动化交易