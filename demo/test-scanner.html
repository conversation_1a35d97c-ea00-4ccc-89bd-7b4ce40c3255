<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>股票扫描器测试</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #1a1a1a;
            color: #ffffff;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background-color: #2a2a2a;
            border-radius: 8px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #4ade80;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .test-pass {
            background-color: #065f46;
            color: #d1fae5;
        }
        .test-fail {
            background-color: #7f1d1d;
            color: #fecaca;
        }
        .test-info {
            background-color: #1e3a8a;
            color: #dbeafe;
        }
        button {
            background-color: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #2563eb;
        }
        button:disabled {
            background-color: #6b7280;
            cursor: not-allowed;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #374151;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background-color: #3b82f6;
            transition: width 0.3s ease;
        }
        .results-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .results-table th,
        .results-table td {
            border: 1px solid #374151;
            padding: 8px;
            text-align: left;
        }
        .results-table th {
            background-color: #374151;
        }
        .signal-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .signal-kdj-golden { background-color: #065f46; color: #d1fae5; }
        .signal-bollinger { background-color: #1e3a8a; color: #dbeafe; }
        .signal-volume { background-color: #581c87; color: #e9d5ff; }
        .signal-oversold { background-color: #0f766e; color: #ccfbf1; }
    </style>
</head>
<body>
    <div class="container">
        <h1>股票扫描器功能测试</h1>
        
        <!-- 数据模拟器测试 -->
        <div class="test-section">
            <div class="test-title">1. 数据模拟器测试</div>
            <div id="simulatorTest"></div>
            <button onclick="testDataSimulator()">测试数据模拟器</button>
        </div>

        <!-- 扫描器基础功能测试 -->
        <div class="test-section">
            <div class="test-title">2. 扫描器基础功能测试</div>
            <div id="scannerTest"></div>
            <button onclick="testScanner()">测试扫描器</button>
        </div>

        <!-- 实际扫描测试 -->
        <div class="test-section">
            <div class="test-title">3. 实际扫描测试</div>
            <div id="scanTest"></div>
            <div class="progress-bar" id="scanProgress" style="display: none;">
                <div class="progress-fill" id="scanProgressFill"></div>
            </div>
            <div id="scanStatus" style="margin: 10px 0;"></div>
            <button onclick="startTestScan()" id="startScanBtn">开始扫描测试</button>
            <button onclick="stopTestScan()" id="stopScanBtn" disabled>停止扫描</button>
            
            <!-- 扫描结果表格 -->
            <table class="results-table" id="resultsTable" style="display: none;">
                <thead>
                    <tr>
                        <th>股票代码</th>
                        <th>股票名称</th>
                        <th>当前价格</th>
                        <th>信号类型</th>
                        <th>信号描述</th>
                    </tr>
                </thead>
                <tbody id="resultsBody">
                </tbody>
            </table>
        </div>

        <!-- 性能测试 -->
        <div class="test-section">
            <div class="test-title">4. 性能测试</div>
            <div id="performanceTest"></div>
            <button onclick="testPerformance()">测试性能</button>
        </div>
    </div>

    <!-- 引入模块 -->
    <script src="js/indicators/indicator-utils.js"></script>
    <script src="js/indicators/bollinger-bands.js"></script>
    <script src="js/indicators/volume-analysis.js"></script>
    <script src="js/indicators/kdj-indicator.js"></script>
    <script src="js/data/stock-data-simulator.js"></script>
    <script src="js/scanner/stock-scanner.js"></script>

    <script>
        let dataSimulator;
        let stockScanner;
        let isScanning = false;

        // 测试工具函数
        function addTestResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const result = document.createElement('div');
            result.className = `test-result test-${type}`;
            result.textContent = message;
            container.appendChild(result);
        }

        function clearTestResults(containerId) {
            const container = document.getElementById(containerId);
            container.innerHTML = '';
        }

        // 1. 测试数据模拟器
        function testDataSimulator() {
            clearTestResults('simulatorTest');
            
            try {
                // 创建数据模拟器
                dataSimulator = new StockDataSimulator();
                addTestResult('simulatorTest', '✓ 数据模拟器创建成功', 'pass');

                // 测试股票列表
                const stockList = dataSimulator.getStockList();
                addTestResult('simulatorTest', `✓ 获取股票列表成功，共 ${stockList.length} 只股票`, 'pass');

                // 测试单只股票数据生成
                const testStock = stockList[0];
                const stockData = dataSimulator.generateStockData(testStock, 60);
                
                if (stockData && stockData.close && stockData.close.length === 60) {
                    addTestResult('simulatorTest', `✓ 生成 ${testStock.name} 60天数据成功`, 'pass');
                } else {
                    addTestResult('simulatorTest', '✗ 股票数据生成失败', 'fail');
                }

                // 测试行业和板块
                const industries = dataSimulator.getIndustries();
                const sectors = dataSimulator.getSectors();
                addTestResult('simulatorTest', `✓ 获取行业列表: ${industries.length} 个行业`, 'info');
                addTestResult('simulatorTest', `✓ 获取板块列表: ${sectors.length} 个板块`, 'info');

            } catch (error) {
                addTestResult('simulatorTest', `✗ 数据模拟器测试失败: ${error.message}`, 'fail');
            }
        }

        // 2. 测试扫描器
        function testScanner() {
            clearTestResults('scannerTest');
            
            try {
                if (!dataSimulator) {
                    dataSimulator = new StockDataSimulator();
                }

                // 创建扫描器
                stockScanner = new StockScanner(dataSimulator);
                addTestResult('scannerTest', '✓ 股票扫描器创建成功', 'pass');

                // 测试扫描条件设置
                const conditions = {
                    kdjGoldenCross: true,
                    bollingerBreakout: true,
                    volumeAnomaly: false
                };
                stockScanner.setScanConditions(conditions);
                
                const retrievedConditions = stockScanner.getScanConditions();
                if (retrievedConditions.kdjGoldenCross === true) {
                    addTestResult('scannerTest', '✓ 扫描条件设置成功', 'pass');
                } else {
                    addTestResult('scannerTest', '✗ 扫描条件设置失败', 'fail');
                }

                // 测试单只股票分析
                const stockList = dataSimulator.getStockList();
                const testStock = stockList[0];
                const stockData = dataSimulator.generateStockData(testStock, 60);
                
                stockScanner.analyzeStock(stockData).then(analysis => {
                    if (analysis && analysis.bollinger && analysis.kdj) {
                        addTestResult('scannerTest', '✓ 单只股票分析成功', 'pass');
                    } else {
                        addTestResult('scannerTest', '✗ 单只股票分析失败', 'fail');
                    }
                }).catch(error => {
                    addTestResult('scannerTest', `✗ 股票分析异常: ${error.message}`, 'fail');
                });

            } catch (error) {
                addTestResult('scannerTest', `✗ 扫描器测试失败: ${error.message}`, 'fail');
            }
        }

        // 3. 开始扫描测试
        async function startTestScan() {
            if (isScanning) return;

            clearTestResults('scanTest');
            
            try {
                if (!stockScanner) {
                    dataSimulator = new StockDataSimulator();
                    stockScanner = new StockScanner(dataSimulator);
                }

                isScanning = true;
                updateScanUI(true);

                // 设置扫描条件
                stockScanner.setScanConditions({
                    kdjGoldenCross: true,
                    bollingerBreakout: true,
                    volumeAnomaly: true,
                    kdjOversold: true
                });

                addTestResult('scanTest', '开始扫描测试...', 'info');

                // 开始扫描（只扫描前10只股票以节省时间）
                const stockList = dataSimulator.getStockList().slice(0, 10);
                const results = await stockScanner.startScan(
                    { filters: {} },
                    onScanProgress,
                    onScanResult
                );

                addTestResult('scanTest', `✓ 扫描完成，找到 ${results.length} 只符合条件的股票`, 'pass');

            } catch (error) {
                addTestResult('scanTest', `✗ 扫描失败: ${error.message}`, 'fail');
            } finally {
                isScanning = false;
                updateScanUI(false);
            }
        }

        // 停止扫描测试
        function stopTestScan() {
            if (stockScanner) {
                stockScanner.stopScan();
            }
            isScanning = false;
            updateScanUI(false);
            addTestResult('scanTest', '扫描已停止', 'info');
        }

        // 扫描进度回调
        function onScanProgress(progress) {
            const { current, total, percentage, currentStock, foundCount } = progress;
            
            const progressFill = document.getElementById('scanProgressFill');
            const statusEl = document.getElementById('scanStatus');
            
            if (progressFill) {
                progressFill.style.width = `${percentage}%`;
            }
            
            if (statusEl) {
                statusEl.textContent = `正在分析: ${currentStock} (${current}/${total}) - 已找到: ${foundCount} 只`;
            }
        }

        // 扫描结果回调
        function onScanResult(result) {
            const table = document.getElementById('resultsTable');
            const tbody = document.getElementById('resultsBody');
            
            if (table && tbody) {
                table.style.display = 'table';
                
                result.signals.forEach(signal => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${result.code}</td>
                        <td>${result.name}</td>
                        <td>${result.currentPrice.toFixed(2)}</td>
                        <td><span class="signal-badge ${getSignalClass(signal.type)}">${signal.type}</span></td>
                        <td>${signal.description}</td>
                    `;
                    tbody.appendChild(row);
                });
            }
        }

        // 获取信号样式类
        function getSignalClass(signalType) {
            const classMap = {
                'KDJ金叉': 'signal-kdj-golden',
                '布林带上轨突破': 'signal-bollinger',
                '布林带下轨突破': 'signal-bollinger',
                '成交量异常': 'signal-volume',
                'KDJ超卖': 'signal-oversold'
            };
            return classMap[signalType] || '';
        }

        // 更新扫描UI
        function updateScanUI(scanning) {
            const startBtn = document.getElementById('startScanBtn');
            const stopBtn = document.getElementById('stopScanBtn');
            const progress = document.getElementById('scanProgress');
            
            if (startBtn) {
                startBtn.disabled = scanning;
                startBtn.textContent = scanning ? '扫描中...' : '开始扫描测试';
            }
            
            if (stopBtn) {
                stopBtn.disabled = !scanning;
            }
            
            if (progress) {
                progress.style.display = scanning ? 'block' : 'none';
            }
        }

        // 4. 性能测试
        function testPerformance() {
            clearTestResults('performanceTest');
            
            if (!dataSimulator) {
                dataSimulator = new StockDataSimulator();
            }

            const startTime = performance.now();
            
            // 测试批量数据生成
            const stockList = dataSimulator.getStockList().slice(0, 5);
            
            Promise.all(stockList.map(stock => {
                return new Promise(resolve => {
                    const data = dataSimulator.generateStockData(stock, 60);
                    resolve(data);
                });
            })).then(results => {
                const endTime = performance.now();
                const duration = endTime - startTime;
                
                addTestResult('performanceTest', `✓ 生成 ${results.length} 只股票数据用时: ${duration.toFixed(2)}ms`, 'pass');
                
                if (duration < 1000) {
                    addTestResult('performanceTest', '✓ 性能测试通过 (< 1秒)', 'pass');
                } else {
                    addTestResult('performanceTest', '⚠ 性能较慢 (> 1秒)', 'info');
                }
            });
        }

        // 页面加载完成后自动运行基础测试
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                testDataSimulator();
            }, 100);
        });
    </script>
</body>
</html>
