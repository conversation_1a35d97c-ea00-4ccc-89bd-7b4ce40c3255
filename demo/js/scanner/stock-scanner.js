/**
 * 股票扫描器
 * 根据技术指标条件扫描A股市场，筛选符合条件的股票
 */

class StockScanner {
    constructor(dataSimulator) {
        this.dataSimulator = dataSimulator;
        this.isScanning = false;
        this.scanResults = [];
        this.scanProgress = { current: 0, total: 0, percentage: 0 };
        
        // 技术指标计算器
        this.indicators = {
            bollinger: new BollingerBands(),
            kdj: new KDJIndicator(),
            volume: new VolumeAnalysis()
        };

        // 扫描条件配置
        this.scanConditions = {
            kdjGoldenCross: true,      // KDJ金叉
            kdjDeathCross: false,      // KDJ死叉
            bollingerBreakout: true,   // 布林带突破
            volumeAnomaly: true,       // 成交量异常
            priceNearBollinger: false, // 价格接近布林带
            kdjOversold: true,         // KDJ超卖
            kdjOverbought: false       // KDJ超买
        };
    }

    /**
     * 开始扫描
     * @param {Object} options - 扫描选项
     * @param {Function} progressCallback - 进度回调
     * @param {Function} resultCallback - 结果回调
     * @returns {Promise} 扫描结果
     */
    async startScan(options = {}, progressCallback = null, resultCallback = null) {
        if (this.isScanning) {
            throw new Error('扫描正在进行中，请等待完成');
        }

        this.isScanning = true;
        this.scanResults = [];
        
        try {
            // 获取股票列表
            const stockList = this.dataSimulator.getStockList(options.filters || {});
            this.scanProgress.total = stockList.length;
            this.scanProgress.current = 0;

            console.log(`开始扫描 ${stockList.length} 只股票...`);

            // 批量处理股票
            const batchSize = options.batchSize || 10;
            for (let i = 0; i < stockList.length; i += batchSize) {
                if (!this.isScanning) break; // 支持中断

                const batch = stockList.slice(i, Math.min(i + batchSize, stockList.length));
                await this.processBatch(batch, progressCallback, resultCallback);
                
                // 短暂延迟，避免阻塞UI
                await new Promise(resolve => setTimeout(resolve, 50));
            }

            console.log(`扫描完成，找到 ${this.scanResults.length} 只符合条件的股票`);
            return this.scanResults;

        } catch (error) {
            console.error('扫描过程中发生错误:', error);
            throw error;
        } finally {
            this.isScanning = false;
        }
    }

    /**
     * 处理一批股票
     * @param {Array} batch - 股票批次
     * @param {Function} progressCallback - 进度回调
     * @param {Function} resultCallback - 结果回调
     */
    async processBatch(batch, progressCallback, resultCallback) {
        for (const stock of batch) {
            try {
                // 生成股票数据
                const stockData = this.dataSimulator.generateStockData(stock, 60);
                
                // 分析股票
                const analysis = await this.analyzeStock(stockData);
                
                // 检查是否符合条件
                const signals = this.checkConditions(analysis);
                
                if (signals.length > 0) {
                    const result = {
                        code: stock.code,
                        name: stock.name,
                        market: stock.market,
                        industry: stock.industry,
                        sector: stock.sector,
                        currentPrice: stockData.close[stockData.close.length - 1],
                        signals: signals,
                        analysis: analysis,
                        scanTime: new Date().toISOString()
                    };
                    
                    this.scanResults.push(result);
                    
                    // 实时返回结果
                    if (resultCallback) {
                        resultCallback(result);
                    }
                }

            } catch (error) {
                console.warn(`分析股票 ${stock.code} 失败:`, error);
            }

            // 更新进度
            this.scanProgress.current++;
            this.scanProgress.percentage = Math.round(
                (this.scanProgress.current / this.scanProgress.total) * 100
            );

            if (progressCallback) {
                progressCallback({
                    ...this.scanProgress,
                    currentStock: stock.name,
                    foundCount: this.scanResults.length
                });
            }
        }
    }

    /**
     * 分析单只股票
     * @param {Object} stockData - 股票数据
     * @returns {Object} 分析结果
     */
    async analyzeStock(stockData) {
        try {
            // 计算布林带
            const bollinger = this.indicators.bollinger.calculate(
                stockData.close, 20, 2
            );

            // 计算KDJ
            const kdj = this.indicators.kdj.calculate({
                high: stockData.high,
                low: stockData.low,
                close: stockData.close
            }, 9, 3, 3);

            // 计算成交量分析
            const volumeAnalysis = this.indicators.volume.calculateInOutDifference({
                open: stockData.open,
                high: stockData.high,
                low: stockData.low,
                close: stockData.close,
                volume: stockData.volume
            });

            // 计算成交量布林带
            const volumeBollinger = this.indicators.volume.calculateVolumeBollinger(
                volumeAnalysis.positiveDiff, 20, 6
            );

            // 识别信号
            const bollingerBreakouts = this.indicators.bollinger.identifyBreakouts(
                stockData.close, bollinger
            );

            const kdjCross = this.indicators.kdj.identifyGoldenDeathCross(kdj);
            
            const kdjOverboughtOversold = this.indicators.kdj.identifyOverboughtOversold(
                kdj, 80, 20
            );

            const volumeBreakouts = this.indicators.volume.identifyVolumeBreakouts(
                volumeBollinger, 2.0
            );

            return {
                bollinger,
                kdj,
                volumeAnalysis,
                volumeBollinger,
                signals: {
                    bollingerBreakouts,
                    kdjCross,
                    kdjOverboughtOversold,
                    volumeBreakouts
                },
                dataLength: stockData.close.length
            };

        } catch (error) {
            console.error('股票分析失败:', error);
            return null;
        }
    }

    /**
     * 检查扫描条件
     * @param {Object} analysis - 分析结果
     * @returns {Array} 符合的信号列表
     */
    checkConditions(analysis) {
        if (!analysis) return [];

        const signals = [];
        const { bollinger, kdj, signals: detectedSignals } = analysis;

        // 检查KDJ金叉
        if (this.scanConditions.kdjGoldenCross && detectedSignals.kdjCross.goldenCross.length > 0) {
            const recentCross = detectedSignals.kdjCross.goldenCross
                .filter(cross => cross.index > analysis.dataLength - 5);
            
            if (recentCross.length > 0) {
                signals.push({
                    type: 'KDJ金叉',
                    description: 'K线上穿D线，买入信号',
                    strength: recentCross[0].strength,
                    index: recentCross[0].index,
                    kValue: recentCross[0].kValue,
                    dValue: recentCross[0].dValue
                });
            }
        }

        // 检查KDJ死叉
        if (this.scanConditions.kdjDeathCross && detectedSignals.kdjCross.deathCross.length > 0) {
            const recentCross = detectedSignals.kdjCross.deathCross
                .filter(cross => cross.index > analysis.dataLength - 5);
            
            if (recentCross.length > 0) {
                signals.push({
                    type: 'KDJ死叉',
                    description: 'K线下穿D线，卖出信号',
                    strength: recentCross[0].strength,
                    index: recentCross[0].index,
                    kValue: recentCross[0].kValue,
                    dValue: recentCross[0].dValue
                });
            }
        }

        // 检查布林带突破
        if (this.scanConditions.bollingerBreakout) {
            const recentUpperBreakouts = detectedSignals.bollingerBreakouts.upperBreakouts
                .filter(breakout => breakout.index > analysis.dataLength - 3);
            
            if (recentUpperBreakouts.length > 0) {
                signals.push({
                    type: '布林带上轨突破',
                    description: '价格突破布林带上轨，强势信号',
                    index: recentUpperBreakouts[0].index,
                    price: recentUpperBreakouts[0].price,
                    upperBand: recentUpperBreakouts[0].upperBand
                });
            }

            const recentLowerBreakouts = detectedSignals.bollingerBreakouts.lowerBreakouts
                .filter(breakout => breakout.index > analysis.dataLength - 3);
            
            if (recentLowerBreakouts.length > 0) {
                signals.push({
                    type: '布林带下轨突破',
                    description: '价格跌破布林带下轨，超卖信号',
                    index: recentLowerBreakouts[0].index,
                    price: recentLowerBreakouts[0].price,
                    lowerBand: recentLowerBreakouts[0].lowerBand
                });
            }
        }

        // 检查成交量异常
        if (this.scanConditions.volumeAnomaly && detectedSignals.volumeBreakouts) {
            const recentVolumeBreakouts = [
                ...detectedSignals.volumeBreakouts.buySignals,
                ...detectedSignals.volumeBreakouts.sellSignals
            ].filter(signal => signal.index > analysis.dataLength - 5);

            if (recentVolumeBreakouts.length > 0) {
                signals.push({
                    type: '成交量异常',
                    description: '成交量出现异常放大，关注资金流向',
                    index: recentVolumeBreakouts[0].index,
                    value: recentVolumeBreakouts[0].value,
                    signalType: recentVolumeBreakouts[0].type
                });
            }
        }

        // 检查KDJ超卖
        if (this.scanConditions.kdjOversold && detectedSignals.kdjOverboughtOversold.oversold.length > 0) {
            const recentOversold = detectedSignals.kdjOverboughtOversold.oversold
                .filter(signal => signal.index > analysis.dataLength - 5);
            
            if (recentOversold.length > 0) {
                signals.push({
                    type: 'KDJ超卖',
                    description: 'KDJ指标显示超卖，可能反弹',
                    index: recentOversold[0].index,
                    kValue: recentOversold[0].kValue,
                    dValue: recentOversold[0].dValue
                });
            }
        }

        // 检查KDJ超买
        if (this.scanConditions.kdjOverbought && detectedSignals.kdjOverboughtOversold.overbought.length > 0) {
            const recentOverbought = detectedSignals.kdjOverboughtOversold.overbought
                .filter(signal => signal.index > analysis.dataLength - 5);
            
            if (recentOverbought.length > 0) {
                signals.push({
                    type: 'KDJ超买',
                    description: 'KDJ指标显示超买，注意回调风险',
                    index: recentOverbought[0].index,
                    kValue: recentOverbought[0].kValue,
                    dValue: recentOverbought[0].dValue
                });
            }
        }

        // 检查价格接近布林带
        if (this.scanConditions.priceNearBollinger && bollinger) {
            const lastIndex = analysis.dataLength - 1;
            const lastPrice = analysis.dataLength > 0 ? analysis.bollinger.middle[lastIndex] : 0;
            const upperBand = bollinger.upper[lastIndex];
            const lowerBand = bollinger.lower[lastIndex];
            const middle = bollinger.middle[lastIndex];

            if (lastPrice && upperBand && lowerBand && middle) {
                const upperDistance = Math.abs(lastPrice - upperBand) / upperBand;
                const lowerDistance = Math.abs(lastPrice - lowerBand) / lowerBand;

                if (upperDistance < 0.02) { // 距离上轨2%以内
                    signals.push({
                        type: '接近布林带上轨',
                        description: '价格接近布林带上轨，关注突破或回调',
                        price: lastPrice,
                        upperBand: upperBand,
                        distance: upperDistance
                    });
                }

                if (lowerDistance < 0.02) { // 距离下轨2%以内
                    signals.push({
                        type: '接近布林带下轨',
                        description: '价格接近布林带下轨，关注支撑或破位',
                        price: lastPrice,
                        lowerBand: lowerBand,
                        distance: lowerDistance
                    });
                }
            }
        }

        return signals;
    }

    /**
     * 停止扫描
     */
    stopScan() {
        this.isScanning = false;
    }

    /**
     * 设置扫描条件
     * @param {Object} conditions - 扫描条件
     */
    setScanConditions(conditions) {
        this.scanConditions = { ...this.scanConditions, ...conditions };
    }

    /**
     * 获取扫描条件
     * @returns {Object} 当前扫描条件
     */
    getScanConditions() {
        return { ...this.scanConditions };
    }

    /**
     * 获取扫描结果
     * @returns {Array} 扫描结果
     */
    getScanResults() {
        return [...this.scanResults];
    }

    /**
     * 获取扫描进度
     * @returns {Object} 扫描进度
     */
    getScanProgress() {
        return { ...this.scanProgress };
    }

    /**
     * 清除扫描结果
     */
    clearResults() {
        this.scanResults = [];
    }

    /**
     * 导出扫描结果
     * @param {string} format - 导出格式 ('json' | 'csv')
     * @returns {string} 导出数据
     */
    exportResults(format = 'json') {
        if (format === 'csv') {
            const headers = ['股票代码', '股票名称', '市场', '行业', '当前价格', '信号类型', '信号描述', '扫描时间'];
            const rows = [headers.join(',')];
            
            this.scanResults.forEach(result => {
                result.signals.forEach(signal => {
                    const row = [
                        result.code,
                        result.name,
                        result.market,
                        result.industry,
                        result.currentPrice,
                        signal.type,
                        `"${signal.description}"`,
                        result.scanTime
                    ];
                    rows.push(row.join(','));
                });
            });
            
            return rows.join('\n');
        } else {
            return JSON.stringify(this.scanResults, null, 2);
        }
    }
}

// 导出股票扫描器
if (typeof module !== 'undefined' && module.exports) {
    module.exports = StockScanner;
} else if (typeof window !== 'undefined') {
    window.StockScanner = StockScanner;
}
