/**
 * 参数控制面板组件
 * 提供技术指标参数的实时调整界面
 */

class ParameterPanel {
    constructor(containerId, options = {}) {
        this.container = document.getElementById(containerId);
        this.options = {
            onChange: options.onChange || (() => {}),
            onReset: options.onReset || (() => {}),
            debounceDelay: options.debounceDelay || 500,
            ...options
        };
        
        this.parameters = {
            bollinger: {
                window: 20,
                stdDev: 2.0
            },
            kdj: {
                kPeriod: 9,
                dPeriod: 3,
                jPeriod: 3
            },
            volume: {
                triangleWindow: 6,
                emaAlpha: 0.3,
                bollingerWindow: 20,
                lag: 6
            },
            display: {
                showBollinger: true,
                showKDJ: true,
                showVolume: true,
                showSignals: true
            }
        };

        this.init();
    }

    /**
     * 初始化面板
     */
    init() {
        this.render();
        this.bindEvents();
    }

    /**
     * 渲染面板HTML
     */
    render() {
        this.container.innerHTML = `
            <div class="parameter-panel card p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-white">技术指标参数</h3>
                    <div class="flex space-x-2">
                        <button id="resetParams" class="btn-secondary text-sm px-3 py-1">
                            <i class="fas fa-undo mr-1"></i>重置
                        </button>
                        <button id="togglePanel" class="btn-secondary text-sm px-3 py-1">
                            <i class="fas fa-chevron-up" id="toggleIcon"></i>
                        </button>
                    </div>
                </div>

                <div id="panelContent" class="space-y-6">
                    <!-- 布林带参数 -->
                    <div class="parameter-group">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="text-md font-medium text-blue-400">布林带参数</h4>
                            <label class="flex items-center">
                                <input type="checkbox" id="showBollinger" class="mr-2" ${this.parameters.display.showBollinger ? 'checked' : ''}>
                                <span class="text-sm text-gray-400">显示</span>
                            </label>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="parameter-item">
                                <label class="block text-sm text-gray-400 mb-2">窗口期</label>
                                <div class="flex items-center space-x-2">
                                    <input type="range" id="bollingerWindow" min="5" max="100" value="${this.parameters.bollinger.window}" 
                                           class="flex-1 h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer">
                                    <span id="bollingerWindowValue" class="text-sm text-white mono-font w-8">${this.parameters.bollinger.window}</span>
                                </div>
                            </div>
                            <div class="parameter-item">
                                <label class="block text-sm text-gray-400 mb-2">标准差倍数</label>
                                <div class="flex items-center space-x-2">
                                    <input type="range" id="bollingerStdDev" min="1" max="5" step="0.1" value="${this.parameters.bollinger.stdDev}" 
                                           class="flex-1 h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer">
                                    <span id="bollingerStdDevValue" class="text-sm text-white mono-font w-8">${this.parameters.bollinger.stdDev}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- KDJ参数 -->
                    <div class="parameter-group">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="text-md font-medium text-purple-400">KDJ参数</h4>
                            <label class="flex items-center">
                                <input type="checkbox" id="showKDJ" class="mr-2" ${this.parameters.display.showKDJ ? 'checked' : ''}>
                                <span class="text-sm text-gray-400">显示</span>
                            </label>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div class="parameter-item">
                                <label class="block text-sm text-gray-400 mb-2">K周期</label>
                                <div class="flex items-center space-x-2">
                                    <input type="range" id="kdjKPeriod" min="3" max="30" value="${this.parameters.kdj.kPeriod}" 
                                           class="flex-1 h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer">
                                    <span id="kdjKPeriodValue" class="text-sm text-white mono-font w-8">${this.parameters.kdj.kPeriod}</span>
                                </div>
                            </div>
                            <div class="parameter-item">
                                <label class="block text-sm text-gray-400 mb-2">D周期</label>
                                <div class="flex items-center space-x-2">
                                    <input type="range" id="kdjDPeriod" min="2" max="10" value="${this.parameters.kdj.dPeriod}" 
                                           class="flex-1 h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer">
                                    <span id="kdjDPeriodValue" class="text-sm text-white mono-font w-8">${this.parameters.kdj.dPeriod}</span>
                                </div>
                            </div>
                            <div class="parameter-item">
                                <label class="block text-sm text-gray-400 mb-2">J周期</label>
                                <div class="flex items-center space-x-2">
                                    <input type="range" id="kdjJPeriod" min="2" max="10" value="${this.parameters.kdj.jPeriod}" 
                                           class="flex-1 h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer">
                                    <span id="kdjJPeriodValue" class="text-sm text-white mono-font w-8">${this.parameters.kdj.jPeriod}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 成交量分析参数 -->
                    <div class="parameter-group">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="text-md font-medium text-yellow-400">成交量分析参数</h4>
                            <label class="flex items-center">
                                <input type="checkbox" id="showVolume" class="mr-2" ${this.parameters.display.showVolume ? 'checked' : ''}>
                                <span class="text-sm text-gray-400">显示</span>
                            </label>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="parameter-item">
                                <label class="block text-sm text-gray-400 mb-2">三角窗口期</label>
                                <div class="flex items-center space-x-2">
                                    <input type="range" id="volumeTriangleWindow" min="3" max="20" value="${this.parameters.volume.triangleWindow}" 
                                           class="flex-1 h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer">
                                    <span id="volumeTriangleWindowValue" class="text-sm text-white mono-font w-8">${this.parameters.volume.triangleWindow}</span>
                                </div>
                            </div>
                            <div class="parameter-item">
                                <label class="block text-sm text-gray-400 mb-2">EMA平滑系数</label>
                                <div class="flex items-center space-x-2">
                                    <input type="range" id="volumeEmaAlpha" min="0.1" max="0.9" step="0.1" value="${this.parameters.volume.emaAlpha}" 
                                           class="flex-1 h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer">
                                    <span id="volumeEmaAlphaValue" class="text-sm text-white mono-font w-8">${this.parameters.volume.emaAlpha}</span>
                                </div>
                            </div>
                            <div class="parameter-item">
                                <label class="block text-sm text-gray-400 mb-2">布林带窗口</label>
                                <div class="flex items-center space-x-2">
                                    <input type="range" id="volumeBollingerWindow" min="10" max="50" value="${this.parameters.volume.bollingerWindow}" 
                                           class="flex-1 h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer">
                                    <span id="volumeBollingerWindowValue" class="text-sm text-white mono-font w-8">${this.parameters.volume.bollingerWindow}</span>
                                </div>
                            </div>
                            <div class="parameter-item">
                                <label class="block text-sm text-gray-400 mb-2">滞后期</label>
                                <div class="flex items-center space-x-2">
                                    <input type="range" id="volumeLag" min="1" max="20" value="${this.parameters.volume.lag}" 
                                           class="flex-1 h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer">
                                    <span id="volumeLagValue" class="text-sm text-white mono-font w-8">${this.parameters.volume.lag}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 显示控制 -->
                    <div class="parameter-group">
                        <h4 class="text-md font-medium text-green-400 mb-3">显示控制</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <label class="flex items-center p-3 bg-gray-800 rounded-lg cursor-pointer hover:bg-gray-700 transition-colors">
                                <input type="checkbox" id="showSignals" class="mr-3" ${this.parameters.display.showSignals ? 'checked' : ''}>
                                <div>
                                    <span class="text-sm font-medium text-white">交易信号</span>
                                    <p class="text-xs text-gray-400">显示买卖点标记</p>
                                </div>
                            </label>
                            <div class="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
                                <div>
                                    <span class="text-sm font-medium text-white">实时更新</span>
                                    <p class="text-xs text-gray-400">参数变化时自动更新</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" id="autoUpdate" class="sr-only peer" checked>
                                    <div class="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- 预设方案 -->
                    <div class="parameter-group">
                        <h4 class="text-md font-medium text-cyan-400 mb-3">预设方案</h4>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                            <button class="preset-btn btn-secondary text-sm" data-preset="conservative">
                                <i class="fas fa-shield-alt mr-2"></i>保守型
                            </button>
                            <button class="preset-btn btn-secondary text-sm" data-preset="balanced">
                                <i class="fas fa-balance-scale mr-2"></i>平衡型
                            </button>
                            <button class="preset-btn btn-secondary text-sm" data-preset="aggressive">
                                <i class="fas fa-rocket mr-2"></i>激进型
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 防抖处理参数变化
        const debouncedOnChange = IndicatorUtils.debounce(
            this.options.onChange.bind(this),
            this.options.debounceDelay
        );

        // 布林带参数
        this.bindRangeInput('bollingerWindow', 'bollinger.window', debouncedOnChange);
        this.bindRangeInput('bollingerStdDev', 'bollinger.stdDev', debouncedOnChange);

        // KDJ参数
        this.bindRangeInput('kdjKPeriod', 'kdj.kPeriod', debouncedOnChange);
        this.bindRangeInput('kdjDPeriod', 'kdj.dPeriod', debouncedOnChange);
        this.bindRangeInput('kdjJPeriod', 'kdj.jPeriod', debouncedOnChange);

        // 成交量参数
        this.bindRangeInput('volumeTriangleWindow', 'volume.triangleWindow', debouncedOnChange);
        this.bindRangeInput('volumeEmaAlpha', 'volume.emaAlpha', debouncedOnChange);
        this.bindRangeInput('volumeBollingerWindow', 'volume.bollingerWindow', debouncedOnChange);
        this.bindRangeInput('volumeLag', 'volume.lag', debouncedOnChange);

        // 显示控制
        this.bindCheckbox('showBollinger', 'display.showBollinger', debouncedOnChange);
        this.bindCheckbox('showKDJ', 'display.showKDJ', debouncedOnChange);
        this.bindCheckbox('showVolume', 'display.showVolume', debouncedOnChange);
        this.bindCheckbox('showSignals', 'display.showSignals', debouncedOnChange);

        // 重置按钮
        document.getElementById('resetParams').addEventListener('click', () => {
            this.resetToDefaults();
            this.options.onReset(this.parameters);
        });

        // 面板折叠
        document.getElementById('togglePanel').addEventListener('click', () => {
            this.togglePanel();
        });

        // 预设方案
        document.querySelectorAll('.preset-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const preset = e.target.dataset.preset;
                this.applyPreset(preset);
                debouncedOnChange(this.parameters);
            });
        });

        // 自动更新开关
        document.getElementById('autoUpdate').addEventListener('change', (e) => {
            this.options.autoUpdate = e.target.checked;
        });
    }

    /**
     * 绑定范围输入控件
     * @param {string} inputId - 输入控件ID
     * @param {string} paramPath - 参数路径
     * @param {Function} onChange - 变化回调
     */
    bindRangeInput(inputId, paramPath, onChange) {
        const input = document.getElementById(inputId);
        const valueSpan = document.getElementById(inputId + 'Value');

        if (!input || !valueSpan) return;

        input.addEventListener('input', (e) => {
            const value = parseFloat(e.target.value);
            this.setParameterByPath(paramPath, value);
            valueSpan.textContent = value;

            if (this.options.autoUpdate) {
                onChange(this.parameters);
            }
        });
    }

    /**
     * 绑定复选框控件
     * @param {string} inputId - 输入控件ID
     * @param {string} paramPath - 参数路径
     * @param {Function} onChange - 变化回调
     */
    bindCheckbox(inputId, paramPath, onChange) {
        const input = document.getElementById(inputId);

        if (!input) return;

        input.addEventListener('change', (e) => {
            const value = e.target.checked;
            this.setParameterByPath(paramPath, value);

            if (this.options.autoUpdate) {
                onChange(this.parameters);
            }
        });
    }

    /**
     * 通过路径设置参数值
     * @param {string} path - 参数路径（如 'bollinger.window'）
     * @param {*} value - 参数值
     */
    setParameterByPath(path, value) {
        const keys = path.split('.');
        let obj = this.parameters;

        for (let i = 0; i < keys.length - 1; i++) {
            obj = obj[keys[i]];
        }

        obj[keys[keys.length - 1]] = value;
    }

    /**
     * 通过路径获取参数值
     * @param {string} path - 参数路径
     * @returns {*} 参数值
     */
    getParameterByPath(path) {
        const keys = path.split('.');
        let obj = this.parameters;

        for (const key of keys) {
            obj = obj[key];
        }

        return obj;
    }

    /**
     * 重置为默认值
     */
    resetToDefaults() {
        this.parameters = {
            bollinger: {
                window: 20,
                stdDev: 2.0
            },
            kdj: {
                kPeriod: 9,
                dPeriod: 3,
                jPeriod: 3
            },
            volume: {
                triangleWindow: 6,
                emaAlpha: 0.3,
                bollingerWindow: 20,
                lag: 6
            },
            display: {
                showBollinger: true,
                showKDJ: true,
                showVolume: true,
                showSignals: true
            }
        };

        this.updateUI();
    }

    /**
     * 应用预设方案
     * @param {string} preset - 预设名称
     */
    applyPreset(preset) {
        const presets = {
            conservative: {
                bollinger: { window: 30, stdDev: 2.5 },
                kdj: { kPeriod: 14, dPeriod: 5, jPeriod: 5 },
                volume: { triangleWindow: 10, emaAlpha: 0.2, bollingerWindow: 30, lag: 10 }
            },
            balanced: {
                bollinger: { window: 20, stdDev: 2.0 },
                kdj: { kPeriod: 9, dPeriod: 3, jPeriod: 3 },
                volume: { triangleWindow: 6, emaAlpha: 0.3, bollingerWindow: 20, lag: 6 }
            },
            aggressive: {
                bollinger: { window: 10, stdDev: 1.5 },
                kdj: { kPeriod: 5, dPeriod: 2, jPeriod: 2 },
                volume: { triangleWindow: 3, emaAlpha: 0.5, bollingerWindow: 10, lag: 3 }
            }
        };

        if (presets[preset]) {
            Object.assign(this.parameters.bollinger, presets[preset].bollinger);
            Object.assign(this.parameters.kdj, presets[preset].kdj);
            Object.assign(this.parameters.volume, presets[preset].volume);

            this.updateUI();
        }
    }

    /**
     * 更新UI显示
     */
    updateUI() {
        // 更新所有输入控件的值
        const updates = [
            ['bollingerWindow', this.parameters.bollinger.window],
            ['bollingerStdDev', this.parameters.bollinger.stdDev],
            ['kdjKPeriod', this.parameters.kdj.kPeriod],
            ['kdjDPeriod', this.parameters.kdj.dPeriod],
            ['kdjJPeriod', this.parameters.kdj.jPeriod],
            ['volumeTriangleWindow', this.parameters.volume.triangleWindow],
            ['volumeEmaAlpha', this.parameters.volume.emaAlpha],
            ['volumeBollingerWindow', this.parameters.volume.bollingerWindow],
            ['volumeLag', this.parameters.volume.lag]
        ];

        updates.forEach(([id, value]) => {
            const input = document.getElementById(id);
            const valueSpan = document.getElementById(id + 'Value');

            if (input) input.value = value;
            if (valueSpan) valueSpan.textContent = value;
        });

        // 更新复选框
        const checkboxes = [
            ['showBollinger', this.parameters.display.showBollinger],
            ['showKDJ', this.parameters.display.showKDJ],
            ['showVolume', this.parameters.display.showVolume],
            ['showSignals', this.parameters.display.showSignals]
        ];

        checkboxes.forEach(([id, checked]) => {
            const input = document.getElementById(id);
            if (input) input.checked = checked;
        });
    }

    /**
     * 切换面板显示/隐藏
     */
    togglePanel() {
        const content = document.getElementById('panelContent');
        const icon = document.getElementById('toggleIcon');

        if (content.style.display === 'none') {
            content.style.display = 'block';
            icon.className = 'fas fa-chevron-up';
        } else {
            content.style.display = 'none';
            icon.className = 'fas fa-chevron-down';
        }
    }

    /**
     * 获取当前参数
     * @returns {Object} 当前参数对象
     */
    getParameters() {
        return { ...this.parameters };
    }

    /**
     * 设置参数
     * @param {Object} params - 参数对象
     */
    setParameters(params) {
        this.parameters = { ...this.parameters, ...params };
        this.updateUI();
    }

    /**
     * 验证参数有效性
     * @returns {Object} 验证结果
     */
    validateParameters() {
        const errors = [];
        const warnings = [];

        // 布林带参数验证
        if (this.parameters.bollinger.window < 5) {
            errors.push('布林带窗口期不能小于5');
        }
        if (this.parameters.bollinger.stdDev < 1) {
            errors.push('标准差倍数不能小于1');
        }

        // KDJ参数验证
        if (this.parameters.kdj.kPeriod < 3) {
            errors.push('KDJ K周期不能小于3');
        }
        if (this.parameters.kdj.dPeriod < 2) {
            errors.push('KDJ D周期不能小于2');
        }

        // 成交量参数验证
        if (this.parameters.volume.triangleWindow < 3) {
            errors.push('三角窗口期不能小于3');
        }

        // 参数合理性警告
        if (this.parameters.bollinger.window > 50) {
            warnings.push('布林带窗口期过大可能导致滞后');
        }
        if (this.parameters.kdj.kPeriod > 20) {
            warnings.push('KDJ K周期过大可能降低敏感性');
        }

        return { errors, warnings, isValid: errors.length === 0 };
    }

    /**
     * 导出参数配置
     * @returns {string} JSON字符串
     */
    exportConfig() {
        return JSON.stringify(this.parameters, null, 2);
    }

    /**
     * 导入参数配置
     * @param {string} configJson - JSON配置字符串
     * @returns {boolean} 是否成功导入
     */
    importConfig(configJson) {
        try {
            const config = JSON.parse(configJson);

            // 验证配置结构
            if (this.validateConfigStructure(config)) {
                this.setParameters(config);
                return true;
            } else {
                console.error('配置结构不正确');
                return false;
            }
        } catch (error) {
            console.error('配置解析失败:', error);
            return false;
        }
    }

    /**
     * 验证配置结构
     * @param {Object} config - 配置对象
     * @returns {boolean} 是否有效
     */
    validateConfigStructure(config) {
        const requiredKeys = ['bollinger', 'kdj', 'volume', 'display'];

        return requiredKeys.every(key => config.hasOwnProperty(key));
    }

    /**
     * 获取参数变化历史
     * @returns {Array} 历史记录
     */
    getHistory() {
        return this.history || [];
    }

    /**
     * 保存当前参数到历史
     */
    saveToHistory() {
        if (!this.history) {
            this.history = [];
        }

        this.history.push({
            timestamp: new Date().toISOString(),
            parameters: { ...this.parameters }
        });

        // 限制历史记录数量
        if (this.history.length > 10) {
            this.history.shift();
        }
    }

    /**
     * 从历史恢复参数
     * @param {number} index - 历史记录索引
     */
    restoreFromHistory(index) {
        if (this.history && this.history[index]) {
            this.setParameters(this.history[index].parameters);
            this.options.onChange(this.parameters);
        }
    }

    /**
     * 显示参数提示信息
     * @param {string} paramName - 参数名称
     * @returns {string} 提示信息
     */
    getParameterTooltip(paramName) {
        const tooltips = {
            'bollingerWindow': '布林带计算的移动平均窗口期，值越大越平滑但滞后性增加',
            'bollingerStdDev': '标准差倍数，控制布林带的宽度，值越大带宽越宽',
            'kdjKPeriod': 'KDJ指标的K值计算周期，影响指标的敏感性',
            'kdjDPeriod': 'D值平滑周期，对K值进行平滑处理',
            'kdjJPeriod': 'J值计算周期，J值变化最为敏感',
            'volumeTriangleWindow': '三角移动平均的窗口期，用于平滑成交量数据',
            'volumeEmaAlpha': '指数移动平均的平滑系数，值越大对近期数据权重越高',
            'volumeBollingerWindow': '成交量布林带的计算窗口期',
            'volumeLag': '滞后期参数，用于计算成交量布林带指数'
        };

        return tooltips[paramName] || '暂无说明';
    }

    /**
     * 销毁面板
     */
    destroy() {
        if (this.container) {
            this.container.innerHTML = '';
        }

        // 清理事件监听器
        this.parameters = null;
        this.options = null;
        this.history = null;
    }
}

// 导出参数面板类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ParameterPanel;
} else if (typeof window !== 'undefined') {
    window.ParameterPanel = ParameterPanel;
}
