# 股票交易系统 Demo

这是一个基于HTML、CSS和JavaScript的股票交易系统演示界面，不依赖后端服务，使用模拟数据进行交互演示。

## 功能特性

### 🏦 股票池模块
- **股票列表展示**：显示股票代码、名称、现价、涨跌幅、成交量等信息
- **实时数据更新**：模拟股价实时波动（每3秒更新一次）
- **搜索功能**：支持按股票代码或名称搜索
- **排序功能**：支持按代码、涨跌幅、成交量排序
- **股票管理**：添加/移除股票到股票池
- **快速交易**：直接从股票池跳转到交易界面

### 📈 交易下单模块
- **股票选择**：从股票池中选择要交易的股票
- **实时行情**：显示选中股票的基本信息和OHLC数据
- **五档行情**：模拟显示买卖五档价格和数量
- **交易表单**：
  - 买入/卖出方向切换
  - 限价单/市价单类型选择
  - 价格和数量输入
  - 快速价格设置（买一、卖一、现价）
  - 快速数量设置（1000、2000、5000、最大）
- **资金检查**：显示可用资金和可卖股数
- **交易确认**：提交前显示确认对话框
- **风险提示**：交易确认时显示风险警告

### 💼 持仓管理模块
- **持仓概况**：总市值、持仓成本、盈亏金额、收益率
- **持仓明细**：
  - 股票代码、名称
  - 持仓股数、可用股数
  - 成本价、现价
  - 市值、盈亏金额、收益率
- **持仓操作**：卖出、加仓功能
- **搜索功能**：按股票代码或名称搜索持仓
- **实时更新**：持仓盈亏随股价实时变化

### 📊 账户信息
- **资金概况**：总资产、可用资金、持仓市值、今日盈亏
- **委托管理**：当日委托列表，支持撤单操作
- **成交记录**：显示最近的成交情况

## 技术特点

### 🎨 界面设计
- **专业外观**：仿照真实股票交易软件界面
- **响应式布局**：使用Tailwind CSS，适配不同屏幕尺寸
- **颜色规范**：遵循中国股市习惯（红涨绿跌）
- **图标支持**：使用Font Awesome图标库

### 🔧 技术实现
- **纯前端**：HTML + CSS + JavaScript，无需后端服务
- **模块化设计**：三个主要功能模块独立实现
- **数据模拟**：完整的假数据体系，模拟真实交易环境
- **实时更新**：定时器模拟数据实时变化
- **交互体验**：丰富的用户交互和反馈

### 📱 用户体验
- **标签页导航**：清晰的模块切换
- **模态对话框**：重要操作的确认机制
- **通知系统**：操作结果的即时反馈
- **键盘支持**：ESC键关闭对话框等快捷操作

## 使用方法

### 启动方式
1. 直接在浏览器中打开 `index.html` 文件
2. 或者使用本地服务器（如Live Server）运行

### 操作流程

#### 1. 股票池管理
- 点击"添加股票"按钮添加新股票到股票池
- 使用搜索框快速查找股票
- 使用排序下拉框对股票进行排序
- 点击"买入"/"卖出"按钮快速跳转到交易界面

#### 2. 交易下单
- 在"交易下单"标签页选择要交易的股票
- 选择买入或卖出方向
- 选择订单类型（限价单/市价单）
- 输入价格和数量，或使用快速设置按钮
- 输入交易密码（任意密码即可）
- 点击"买入"/"卖出"按钮提交订单
- 在确认对话框中确认交易信息

#### 3. 持仓管理
- 在"持仓管理"标签页查看所有持仓
- 查看持仓概况和明细信息
- 点击"卖出"/"加仓"按钮进行持仓操作
- 使用搜索框查找特定持仓

## 模拟数据说明

### 初始数据
- **股票池**：包含5只预设股票（平安银行、万科A、招商银行、贵州茅台、五粮液）
- **持仓**：包含3只股票的持仓记录
- **资金**：总资产150万，可用资金100万
- **委托**：包含2条历史委托记录

### 数据更新
- **股价波动**：每3秒随机波动±1%
- **成交量**：持续增长模拟
- **订单处理**：70%概率成交，30%概率待成交
- **持仓更新**：根据股价变化实时计算盈亏

## 注意事项

1. **仅供演示**：这是一个演示系统，不连接真实的股票交易接口
2. **数据模拟**：所有数据都是模拟生成，不代表真实市场情况
3. **浏览器兼容**：建议使用现代浏览器（Chrome、Firefox、Safari、Edge）
4. **本地存储**：数据不会持久化，刷新页面后恢复初始状态

## 文件结构

```
demo/
├── index.html          # 主页面文件
└── README.md          # 说明文档
```

## 技术依赖

- **Tailwind CSS**: https://cdn.staticfile.net/tailwindcss/2.2.9/tailwind.min.css
- **Font Awesome**: https://cdn.staticfile.net/font-awesome/6.5.1/css/all.min.css

## 扩展建议

如需进一步开发，可以考虑：

1. **数据持久化**：使用localStorage保存用户数据
2. **图表集成**：添加K线图和技术指标
3. **WebSocket连接**：连接真实的行情数据源
4. **移动端优化**：针对手机端进行界面优化
5. **主题切换**：支持深色/浅色主题切换
6. **更多订单类型**：支持止损单、条件单等
7. **风控系统**：添加交易限额和风险控制
8. **报表功能**：添加交易统计和分析报表

## 许可证

本项目仅供学习和演示使用。
