# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 虚拟环境
venv/
env/
ENV/
.env

# IDE
.idea/
.vscode/
*.swp
*.swo

# 数据库
*.db
*.sqlite
*.sqlite3

# 日志
logs/
*.log

# 缓存
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
.hypothesis/
.coverage.*

# 本地配置
.env.local

# 临时文件
.DS_Store
Thumbs.db
