# 股票交易系统前端界面设计文档

## 1. 项目概述

### 1.1 设计目标
基于现有量化分析系统架构，扩展设计一个完整的股票交易系统前端界面，包含股票池管理、开仓交易、持仓管理等核心功能模块。

### 1.2 技术基础
- **前端框架**: Vue 3 + Composition API
- **UI组件库**: Element Plus
- **图表库**: ECharts
- **状态管理**: Pinia (推荐) 或 Vuex
- **路由管理**: Vue Router
- **HTTP客户端**: Axios
- **构建工具**: Vite

### 1.3 设计原则
- **用户体验优先**: 界面简洁清晰，操作流程顺畅
- **专业化设计**: 符合专业交易软件的视觉和交互标准
- **响应式布局**: 适配不同屏幕尺寸和设备
- **实时性**: 支持实时数据更新和交互反馈
- **安全性**: 交易操作需要确认机制，防止误操作

## 2. 整体架构设计

### 2.1 页面结构层次
```
├── 主界面 (MainLayout)
    ├── 顶部导航栏 (HeaderNav)
    ├── 侧边导航栏 (SideNav) 
    └── 主内容区域 (MainContent)
        ├── 股票池模块 (StockPool)
        ├── 开仓交易模块 (Trading)
        ├── 持仓管理模块 (Position)
        └── 市场分析模块 (Analysis) - 基于现有功能
```

### 2.2 路由设计
```javascript
const routes = [
  {
    path: '/',
    redirect: '/trading'
  },
  {
    path: '/trading',
    name: 'Trading',
    component: TradingLayout,
    children: [
      {
        path: 'pool',
        name: 'StockPool',
        component: StockPool,
        meta: { title: '股票池', icon: 'el-icon-collection' }
      },
      {
        path: 'order',
        name: 'OrderTrading', 
        component: OrderTrading,
        meta: { title: '交易下单', icon: 'el-icon-s-finance' }
      },
      {
        path: 'position',
        name: 'Position',
        component: Position,
        meta: { title: '持仓管理', icon: 'el-icon-wallet' }
      }
    ]
  },
  {
    path: '/analysis',
    name: 'Analysis',
    component: Analysis,
    meta: { title: '技术分析', icon: 'el-icon-data-line' }
  }
]
```

### 2.3 状态管理设计
```javascript
// stores/trading.js
export const useTradingStore = defineStore('trading', {
  state: () => ({
    // 股票池数据
    stockPool: [],
    selectedStocks: [],
    
    // 持仓数据
    positions: [],
    totalAssets: 0,
    availableFunds: 0,
    
    // 交易数据
    activeOrders: [],
    tradingHistory: [],
    
    // 实时行情数据
    marketData: {},
    
    // UI状态
    currentTab: 'pool',
    loading: false
  }),
  
  actions: {
    // 股票池操作
    async fetchStockPool() { },
    addToStockPool(stock) { },
    removeFromStockPool(stockCode) { },
    
    // 交易操作
    async submitOrder(orderData) { },
    async cancelOrder(orderId) { },
    
    // 持仓操作
    async fetchPositions() { },
    async updatePosition(position) { },
    
    // 实时数据更新
    updateMarketData(data) { },
    subscribeToRealtimeData() { }
  }
})
```

## 3. 核心模块设计

### 3.1 股票池模块 (StockPool)

#### 3.1.1 功能描述
- 展示和管理用户自选的股票池
- 支持股票的添加、删除、排序操作
- 显示股票基本信息和实时行情
- 提供快速交易入口

#### 3.1.2 界面布局
```vue
<template>
  <div class="stock-pool-container">
    <!-- 顶部工具栏 -->
    <div class="toolbar">
      <el-button type="primary" @click="showAddStockDialog">
        <el-icon><Plus /></el-icon>
        添加股票
      </el-button>
      <el-input
        v-model="searchKeyword"
        placeholder="搜索股票代码或名称"
        style="width: 300px"
        clearable
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
      <el-button-group>
        <el-button :type="sortBy === 'code' ? 'primary' : ''" @click="sortBy = 'code'">
          代码
        </el-button>
        <el-button :type="sortBy === 'change' ? 'primary' : ''" @click="sortBy = 'change'">
          涨跌幅
        </el-button>
        <el-button :type="sortBy === 'volume' ? 'primary' : ''" @click="sortBy = 'volume'">
          成交量
        </el-button>
      </el-button-group>
    </div>

    <!-- 股票列表 -->
    <div class="stock-list">
      <el-table
        :data="filteredStockPool"
        style="width: 100%"
        height="600"
        @row-click="handleRowClick"
        @row-dblclick="handleQuickTrade"
      >
        <el-table-column prop="code" label="代码" width="80" fixed="left">
          <template #default="scope">
            <span class="stock-code">{{ scope.row.code }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="name" label="名称" width="120" fixed="left">
          <template #default="scope">
            <span class="stock-name">{{ scope.row.name }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="current_price" label="现价" width="100" align="right">
          <template #default="scope">
            <span :class="getPriceClass(scope.row.change_percent)">
              {{ scope.row.current_price?.toFixed(2) }}
            </span>
          </template>
        </el-table-column>
        
        <el-table-column prop="change_amount" label="涨跌额" width="100" align="right">
          <template #default="scope">
            <span :class="getPriceClass(scope.row.change_percent)">
              {{ scope.row.change_amount > 0 ? '+' : '' }}{{ scope.row.change_amount?.toFixed(2) }}
            </span>
          </template>
        </el-table-column>
        
        <el-table-column prop="change_percent" label="涨跌幅" width="100" align="right">
          <template #default="scope">
            <span :class="getPriceClass(scope.row.change_percent)">
              {{ scope.row.change_percent > 0 ? '+' : '' }}{{ scope.row.change_percent?.toFixed(2) }}%
            </span>
          </template>
        </el-table-column>
        
        <el-table-column prop="volume" label="成交量" width="120" align="right">
          <template #default="scope">
            <span>{{ formatVolume(scope.row.volume) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="turnover" label="成交额" width="120" align="right">
          <template #default="scope">
            <span>{{ formatAmount(scope.row.turnover) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button size="small" type="success" @click.stop="handleBuy(scope.row)">
              买入
            </el-button>
            <el-button size="small" type="danger" @click.stop="handleSell(scope.row)">
              卖出
            </el-button>
            <el-button size="small" @click.stop="handleAnalysis(scope.row)">
              分析
            </el-button>
            <el-popconfirm
              title="确定要从股票池中移除这只股票吗?"
              @confirm="removeStock(scope.row.code)"
            >
              <template #reference>
                <el-button size="small" type="info" @click.stop>
                  移除
                </el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 添加股票对话框 -->
    <add-stock-dialog
      v-model="showAddDialog"
      @confirm="handleAddStock"
    />
  </div>
</template>
```

#### 3.1.3 数据结构
```javascript
// 股票池数据模型
const stockPoolItem = {
  code: '000001',           // 股票代码
  name: '平安银行',          // 股票名称
  current_price: 12.85,     // 当前价格
  open_price: 12.90,        // 开盘价
  high_price: 13.10,        // 最高价
  low_price: 12.80,         // 最低价
  prev_close: 12.88,        // 昨收价
  change_amount: -0.03,     // 涨跌额
  change_percent: -0.23,    // 涨跌幅
  volume: 15420000,         // 成交量(股)
  turnover: 198450000,      // 成交额(元)
  market_cap: 248950000000, // 总市值
  pe_ratio: 5.82,           // 市盈率
  pb_ratio: 0.67,           // 市净率
  add_time: '2024-01-15',   // 加入股票池时间
  tags: ['银行', '低估值'],  // 标签
  note: '关注业绩拐点'        // 备注
}
```

### 3.2 开仓交易模块 (OrderTrading)

#### 3.2.1 功能描述
- 提供专业的股票交易下单界面
- 支持市价单、限价单等多种订单类型
- 实时显示五档行情和成交明细
- 提供交易确认和风险提示

#### 3.2.2 界面布局
```vue
<template>
  <div class="trading-container">
    <el-row :gutter="20">
      <!-- 左侧：行情信息 -->
      <el-col :span="8">
        <div class="market-info-panel">
          <!-- 股票选择器 -->
          <div class="stock-selector">
            <el-select
              v-model="selectedStock"
              filterable
              remote
              placeholder="请输入股票代码或名称"
              :remote-method="searchStock"
              :loading="searchLoading"
              style="width: 100%"
              @change="handleStockChange"
            >
              <el-option
                v-for="stock in stockOptions"
                :key="stock.code"
                :label="`${stock.code} ${stock.name}`"
                :value="stock.code"
              />
            </el-select>
          </div>

          <!-- 股票基本信息 -->
          <div class="stock-info" v-if="currentStockInfo">
            <div class="stock-header">
              <h3>{{ currentStockInfo.name }} ({{ currentStockInfo.code }})</h3>
              <div class="price-info">
                <span class="current-price" :class="getPriceClass(currentStockInfo.change_percent)">
                  {{ currentStockInfo.current_price?.toFixed(2) }}
                </span>
                <span class="change-info" :class="getPriceClass(currentStockInfo.change_percent)">
                  {{ currentStockInfo.change_amount > 0 ? '+' : '' }}{{ currentStockInfo.change_amount?.toFixed(2) }}
                  ({{ currentStockInfo.change_percent > 0 ? '+' : '' }}{{ currentStockInfo.change_percent?.toFixed(2) }}%)
                </span>
              </div>
            </div>
            
            <!-- OHLC信息 -->
            <div class="ohlc-info">
              <el-row :gutter="10">
                <el-col :span="12">
                  <div class="info-item">
                    <span class="label">开盘:</span>
                    <span class="value">{{ currentStockInfo.open_price?.toFixed(2) }}</span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="info-item">
                    <span class="label">昨收:</span>
                    <span class="value">{{ currentStockInfo.prev_close?.toFixed(2) }}</span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="info-item">
                    <span class="label">最高:</span>
                    <span class="value price-up">{{ currentStockInfo.high_price?.toFixed(2) }}</span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="info-item">
                    <span class="label">最低:</span>
                    <span class="value price-down">{{ currentStockInfo.low_price?.toFixed(2) }}</span>
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>

          <!-- 五档行情 -->
          <div class="depth-panel">
            <h4>五档行情</h4>
            <div class="depth-table">
              <!-- 卖档 -->
              <div class="sell-depth">
                <div 
                  v-for="(item, index) in sellDepth" 
                  :key="`sell-${index}`"
                  class="depth-row sell-row"
                >
                  <span class="level">卖{{ 5 - index }}</span>
                  <span class="price price-down">{{ item.price?.toFixed(2) }}</span>
                  <span class="volume">{{ item.volume }}</span>
                </div>
              </div>
              
              <!-- 买档 -->
              <div class="buy-depth">
                <div 
                  v-for="(item, index) in buyDepth" 
                  :key="`buy-${index}`"
                  class="depth-row buy-row"
                >
                  <span class="level">买{{ index + 1 }}</span>
                  <span class="price price-up">{{ item.price?.toFixed(2) }}</span>
                  <span class="volume">{{ item.volume }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 成交明细 -->
          <div class="trade-detail-panel">
            <h4>成交明细</h4>
            <div class="trade-list">
              <div 
                v-for="trade in tradeDetails" 
                :key="trade.id"
                class="trade-item"
              >
                <span class="trade-time">{{ trade.time }}</span>
                <span class="trade-price" :class="trade.direction === 'up' ? 'price-up' : 'price-down'">
                  {{ trade.price?.toFixed(2) }}
                </span>
                <span class="trade-volume">{{ trade.volume }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-col>

      <!-- 中间：交易面板 -->
      <el-col :span="8">
        <div class="trading-panel">
          <!-- 交易方向选择 -->
          <div class="direction-tabs">
            <el-radio-group v-model="tradeDirection" size="large">
              <el-radio-button label="buy" class="buy-tab">买入</el-radio-button>
              <el-radio-button label="sell" class="sell-tab">卖出</el-radio-button>
            </el-radio-group>
          </div>

          <!-- 交易表单 -->
          <el-form ref="orderFormRef" :model="orderForm" :rules="orderRules" label-width="80px">
            <!-- 订单类型 -->
            <el-form-item label="订单类型">
              <el-radio-group v-model="orderForm.order_type">
                <el-radio label="limit">限价单</el-radio>
                <el-radio label="market">市价单</el-radio>
              </el-radio-group>
            </el-form-item>

            <!-- 价格 -->
            <el-form-item 
              label="委托价格" 
              prop="price" 
              v-if="orderForm.order_type === 'limit'"
            >
              <el-input-number
                v-model="orderForm.price"
                :precision="2"
                :step="0.01"
                :min="0"
                style="width: 100%"
                controls-position="right"
                @change="calculateAmount"
              />
              <div class="price-buttons">
                <el-button size="small" @click="setPriceFromDepth('buy', 1)">买一</el-button>
                <el-button size="small" @click="setPriceFromDepth('sell', 1)">卖一</el-button>
                <el-button size="small" @click="setPriceFromCurrent">现价</el-button>
              </div>
            </el-form-item>

            <!-- 数量 -->
            <el-form-item label="委托数量" prop="quantity">
              <el-input-number
                v-model="orderForm.quantity"
                :min="100"
                :step="100"
                style="width: 100%"
                controls-position="right"
                @change="calculateAmount"
              />
              <div class="quantity-buttons">
                <el-button size="small" @click="setQuantity(1000)">1000</el-button>
                <el-button size="small" @click="setQuantity(2000)">2000</el-button>
                <el-button size="small" @click="setQuantity(5000)">5000</el-button>
                <el-button size="small" @click="setMaxQuantity">最大</el-button>
              </div>
            </el-form-item>

            <!-- 金额 -->
            <el-form-item label="委托金额">
              <div class="amount-display">
                <span class="amount">{{ formatAmount(orderForm.amount) }}</span>
                <span class="currency">元</span>
              </div>
            </el-form-item>

            <!-- 可用资金/持仓 -->
            <el-form-item :label="tradeDirection === 'buy' ? '可用资金' : '可卖数量'">
              <div class="available-info">
                <span v-if="tradeDirection === 'buy'" class="available-funds">
                  {{ formatAmount(availableFunds) }} 元
                </span>
                <span v-else class="available-shares">
                  {{ availableShares }} 股
                </span>
              </div>
            </el-form-item>

            <!-- 交易密码 -->
            <el-form-item label="交易密码" prop="password">
              <el-input
                v-model="orderForm.password"
                type="password"
                placeholder="请输入交易密码"
                show-password
              />
            </el-form-item>

            <!-- 提交按钮 -->
            <el-form-item>
              <el-button
                type="primary"
                :class="tradeDirection === 'buy' ? 'buy-button' : 'sell-button'"
                size="large"
                style="width: 100%"
                :loading="submitLoading"
                @click="submitOrder"
              >
                {{ tradeDirection === 'buy' ? '买入' : '卖出' }}
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-col>

      <!-- 右侧：订单和资金信息 -->
      <el-col :span="8">
        <div class="order-info-panel">
          <!-- 账户资金信息 -->
          <div class="account-info">
            <h4>账户资金</h4>
            <el-row :gutter="10">
              <el-col :span="12">
                <div class="info-item">
                  <span class="label">总资产:</span>
                  <span class="value">{{ formatAmount(accountInfo.total_assets) }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <span class="label">可用资金:</span>
                  <span class="value">{{ formatAmount(accountInfo.available_funds) }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <span class="label">持仓市值:</span>
                  <span class="value">{{ formatAmount(accountInfo.position_value) }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <span class="label">今日盈亏:</span>
                  <span class="value" :class="accountInfo.today_pnl >= 0 ? 'price-up' : 'price-down'">
                    {{ formatAmount(accountInfo.today_pnl) }}
                  </span>
                </div>
              </el-col>
            </el-row>
          </div>

          <!-- 当日委托 -->
          <div class="today-orders">
            <h4>当日委托</h4>
            <div class="order-list">
              <div 
                v-for="order in todayOrders" 
                :key="order.id"
                class="order-item"
              >
                <div class="order-header">
                  <span class="stock-info">{{ order.stock_code }} {{ order.stock_name }}</span>
                  <span class="order-status" :class="getOrderStatusClass(order.status)">
                    {{ getOrderStatusText(order.status) }}
                  </span>
                </div>
                <div class="order-details">
                  <span class="direction" :class="order.direction === 'buy' ? 'buy-text' : 'sell-text'">
                    {{ order.direction === 'buy' ? '买入' : '卖出' }}
                  </span>
                  <span class="price">{{ order.price?.toFixed(2) }}</span>
                  <span class="quantity">{{ order.quantity }}股</span>
                </div>
                <div class="order-actions" v-if="order.status === 'pending'">
                  <el-button size="small" type="danger" @click="cancelOrder(order.id)">
                    撤单
                  </el-button>
                </div>
              </div>
            </div>
          </div>

          <!-- 成交回报 -->
          <div class="trade-reports">
            <h4>成交回报</h4>
            <div class="report-list">
              <div 
                v-for="report in tradeReports" 
                :key="report.id"
                class="report-item"
              >
                <div class="report-header">
                  <span class="stock-info">{{ report.stock_code }} {{ report.stock_name }}</span>
                  <span class="trade-time">{{ report.trade_time }}</span>
                </div>
                <div class="report-details">
                  <span class="direction" :class="report.direction === 'buy' ? 'buy-text' : 'sell-text'">
                    {{ report.direction === 'buy' ? '买入' : '卖出' }}
                  </span>
                  <span class="price">{{ report.price?.toFixed(2) }}</span>
                  <span class="quantity">{{ report.quantity }}股</span>
                  <span class="amount">{{ formatAmount(report.amount) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
```

#### 3.2.3 数据结构
```javascript
// 订单数据模型
const orderData = {
  id: 'ORD20240115001',      // 订单ID
  stock_code: '000001',      // 股票代码
  stock_name: '平安银行',     // 股票名称
  direction: 'buy',          // 交易方向: buy/sell
  order_type: 'limit',       // 订单类型: limit/market
  price: 12.85,              // 委托价格
  quantity: 1000,            // 委托数量
  amount: 12850,             // 委托金额
  status: 'pending',         // 订单状态: pending/filled/cancelled/partial
  filled_quantity: 0,        // 已成交数量
  filled_amount: 0,          // 已成交金额
  create_time: '2024-01-15 09:30:00',  // 创建时间
  update_time: '2024-01-15 09:30:00',  // 更新时间
  trade_password: '******'   // 交易密码(加密)
}

// 五档行情数据模型
const depthData = {
  stock_code: '000001',
  timestamp: '2024-01-15 09:30:00',
  buy_depth: [
    { level: 1, price: 12.84, volume: 2500 },
    { level: 2, price: 12.83, volume: 1800 },
    { level: 3, price: 12.82, volume: 3200 },
    { level: 4, price: 12.81, volume: 1500 },
    { level: 5, price: 12.80, volume: 2000 }
  ],
  sell_depth: [
    { level: 1, price: 12.85, volume: 1200 },
    { level: 2, price: 12.86, volume: 2800 },
    { level: 3, price: 12.87, volume: 1600 },
    { level: 4, price: 12.88, volume: 2200 },
    { level: 5, price: 12.89, volume: 1800 }
  ]
}
```

### 3.3 持仓管理模块 (Position)

#### 3.3.1 功能描述
- 展示用户当前持仓情况
- 显示持仓盈亏和收益率
- 提供快速交易和止盈止损设置
- 支持持仓数据导出和分析

#### 3.3.2 界面布局
```vue
<template>
  <div class="position-container">
    <!-- 持仓概况 -->
    <div class="position-summary">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="summary-card">
            <div class="summary-item">
              <div class="label">总市值</div>
              <div class="value big-number">{{ formatAmount(positionSummary.total_market_value) }}</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="summary-card">
            <div class="summary-item">
              <div class="label">持仓成本</div>
              <div class="value big-number">{{ formatAmount(positionSummary.total_cost) }}</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="summary-card">
            <div class="summary-item">
              <div class="label">持仓盈亏</div>
              <div class="value big-number" :class="positionSummary.total_pnl >= 0 ? 'profit' : 'loss'">
                {{ formatAmount(positionSummary.total_pnl) }}
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="summary-card">
            <div class="summary-item">
              <div class="label">收益率</div>
              <div class="value big-number" :class="positionSummary.total_return_rate >= 0 ? 'profit' : 'loss'">
                {{ positionSummary.total_return_rate?.toFixed(2) }}%
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 工具栏 -->
    <div class="toolbar">
      <el-button type="primary" @click="refreshPositions">
        <el-icon><Refresh /></el-icon>
        刷新
      </el-button>
      <el-button @click="exportPositions">
        <el-icon><Download /></el-icon>
        导出
      </el-button>
      <el-select v-model="sortBy" placeholder="排序方式" style="width: 150px">
        <el-option label="股票代码" value="code" />
        <el-option label="持仓盈亏" value="pnl" />
        <el-option label="收益率" value="return_rate" />
        <el-option label="持仓市值" value="market_value" />
      </el-select>
      <el-input
        v-model="searchKeyword"
        placeholder="搜索股票"
        style="width: 200px"
        clearable
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
    </div>

    <!-- 持仓列表 -->
    <div class="position-table">
      <el-table
        :data="filteredPositions"
        style="width: 100%"
        height="500"
        @row-click="handleRowClick"
      >
        <el-table-column prop="stock_code" label="代码" width="80" fixed="left">
          <template #default="scope">
            <span class="stock-code">{{ scope.row.stock_code }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="stock_name" label="名称" width="120" fixed="left">
          <template #default="scope">
            <span class="stock-name">{{ scope.row.stock_name }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="quantity" label="持仓股数" width="100" align="right">
          <template #default="scope">
            <span>{{ scope.row.quantity.toLocaleString() }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="available_quantity" label="可用股数" width="100" align="right">
          <template #default="scope">
            <span>{{ scope.row.available_quantity.toLocaleString() }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="avg_cost" label="成本价" width="100" align="right">
          <template #default="scope">
            <span>{{ scope.row.avg_cost?.toFixed(2) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="current_price" label="现价" width="100" align="right">
          <template #default="scope">
            <span :class="getPriceClass(scope.row.change_percent)">
              {{ scope.row.current_price?.toFixed(2) }}
            </span>
          </template>
        </el-table-column>
        
        <el-table-column prop="market_value" label="市值" width="120" align="right">
          <template #default="scope">
            <span>{{ formatAmount(scope.row.market_value) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="cost_value" label="成本" width="120" align="right">
          <template #default="scope">
            <span>{{ formatAmount(scope.row.cost_value) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="pnl_amount" label="盈亏金额" width="120" align="right">
          <template #default="scope">
            <span :class="scope.row.pnl_amount >= 0 ? 'profit' : 'loss'">
              {{ scope.row.pnl_amount >= 0 ? '+' : '' }}{{ formatAmount(scope.row.pnl_amount) }}
            </span>
          </template>
        </el-table-column>
        
        <el-table-column prop="return_rate" label="收益率" width="100" align="right">
          <template #default="scope">
            <span :class="scope.row.return_rate >= 0 ? 'profit' : 'loss'">
              {{ scope.row.return_rate >= 0 ? '+' : '' }}{{ scope.row.return_rate?.toFixed(2) }}%
            </span>
          </template>
        </el-table-column>
        
        <el-table-column prop="change_percent" label="涨跌幅" width="100" align="right">
          <template #default="scope">
            <span :class="getPriceClass(scope.row.change_percent)">
              {{ scope.row.change_percent >= 0 ? '+' : '' }}{{ scope.row.change_percent?.toFixed(2) }}%
            </span>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="scope">
            <el-button size="small" type="danger" @click.stop="handleSell(scope.row)">
              卖出
            </el-button>
            <el-button size="small" @click.stop="handleAddPosition(scope.row)">
              加仓
            </el-button>
            <el-button size="small" @click.stop="handleSetAlert(scope.row)">
              预警
            </el-button>
            <el-dropdown @command="handleDropdownCommand">
              <el-button size="small">
                更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="{action: 'analysis', row: scope.row}">
                    技术分析
                  </el-dropdown-item>
                  <el-dropdown-item :command="{action: 'history', row: scope.row}">
                    交易历史
                  </el-dropdown-item>
                  <el-dropdown-item :command="{action: 'dividend', row: scope.row}">
                    分红记录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 持仓详情对话框 -->
    <position-detail-dialog
      v-model="showDetailDialog"
      :position-data="selectedPosition"
    />

    <!-- 交易对话框 -->
    <quick-trade-dialog
      v-model="showTradeDialog"
      :stock-info="selectedStock"
      :trade-type="tradeType"
    />

    <!-- 预警设置对话框 -->
    <alert-setting-dialog
      v-model="showAlertDialog"
      :stock-info="selectedStock"
    />
  </div>
</template>
```

#### 3.3.3 数据结构
```javascript
// 持仓数据模型
const positionData = {
  id: 'POS20240115001',           // 持仓ID
  stock_code: '000001',           // 股票代码
  stock_name: '平安银行',          // 股票名称
  quantity: 2000,                 // 持仓数量
  available_quantity: 2000,       // 可用数量(扣除冻结)
  avg_cost: 12.90,               // 平均成本价
  cost_value: 25800,             // 成本金额
  current_price: 12.85,          // 当前价格
  market_value: 25700,           // 当前市值
  pnl_amount: -100,              // 盈亏金额
  return_rate: -0.39,            // 收益率(%)
  change_amount: -0.03,          // 涨跌额
  change_percent: -0.23,         // 涨跌幅(%)
  first_buy_date: '2024-01-10',  // 首次买入日期
  last_trade_date: '2024-01-14', // 最后交易日期
  dividend_amount: 0,            // 累计分红
  total_buy_amount: 25800,       // 累计买入金额
  total_sell_amount: 0,          // 累计卖出金额
  frozen_quantity: 0,            // 冻结数量
  today_buy_quantity: 0,         // 今日买入数量
  today_sell_quantity: 0,        // 今日卖出数量
  alert_settings: {              // 预警设置
    upper_price: null,           // 价格上限
    lower_price: null,           // 价格下限
    profit_rate: null,           // 止盈比例
    loss_rate: null              // 止损比例
  }
}

// 持仓汇总数据模型
const positionSummary = {
  total_market_value: 125700,    // 总市值
  total_cost: 126800,           // 总成本
  total_pnl: -1100,             // 总盈亏
  total_return_rate: -0.87,     // 总收益率
  position_count: 5,            // 持仓股票数量
  today_pnl: -200,              // 今日盈亏
  today_return_rate: -0.16      // 今日收益率
}
```

## 4. 组件设计

### 4.1 通用组件

#### 4.1.1 股票搜索组件 (StockSearch)
```vue
<template>
  <el-select
    v-model="selectedValue"
    filterable
    remote
    :placeholder="placeholder"
    :remote-method="handleSearch"
    :loading="loading"
    @change="handleChange"
  >
    <el-option
      v-for="stock in stockOptions"
      :key="stock.code"
      :label="`${stock.code} ${stock.name}`"
      :value="stock.code"
    >
      <div class="stock-option">
        <span class="code">{{ stock.code }}</span>
        <span class="name">{{ stock.name }}</span>
        <span class="price">{{ stock.current_price?.toFixed(2) }}</span>
      </div>
    </el-option>
  </el-select>
</template>
```

#### 4.1.2 价格显示组件 (PriceDisplay)
```vue
<template>
  <span :class="priceClass">
    <span class="price">{{ formattedPrice }}</span>
    <span v-if="showChange" class="change">
      ({{ changePrefix }}{{ formattedChange }})
    </span>
  </span>
</template>
```

#### 4.1.3 交易确认对话框 (TradeConfirmDialog)
```vue
<template>
  <el-dialog
    v-model="visible"
    title="交易确认"
    width="500px"
    :before-close="handleClose"
  >
    <div class="confirm-content">
      <!-- 交易信息展示 -->
      <div class="trade-info">
        <h4>请确认以下交易信息:</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="股票">
            {{ orderData.stock_code }} {{ orderData.stock_name }}
          </el-descriptions-item>
          <el-descriptions-item label="方向">
            <el-tag :type="orderData.direction === 'buy' ? 'success' : 'danger'">
              {{ orderData.direction === 'buy' ? '买入' : '卖出' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="价格">
            {{ orderData.price?.toFixed(2) }} 元
          </el-descriptions-item>
          <el-descriptions-item label="数量">
            {{ orderData.quantity }} 股
          </el-descriptions-item>
          <el-descriptions-item label="金额">
            {{ formatAmount(orderData.amount) }} 元
          </el-descriptions-item>
          <el-descriptions-item label="手续费">
            {{ formatAmount(orderData.commission) }} 元
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 风险提示 -->
      <div class="risk-warning">
        <el-alert
          title="风险提示"
          type="warning"
          :closable="false"
        >
          <p>股市有风险，投资需谨慎。请在充分了解相关风险的前提下进行投资决策。</p>
        </el-alert>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="confirming">
          确认交易
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>
```

### 4.2 业务组件

#### 4.2.1 实时行情组件 (RealtimeQuote)
```vue
<template>
  <div class="realtime-quote">
    <!-- K线图表 -->
    <div class="chart-container">
      <kline-chart
        :data="klineData"
        :height="300"
        :indicators="selectedIndicators"
        @time-range-change="handleTimeRangeChange"
      />
    </div>

    <!-- 指标选择 -->
    <div class="indicator-selector">
      <el-checkbox-group v-model="selectedIndicators">
        <el-checkbox label="ma">均线</el-checkbox>
        <el-checkbox label="macd">MACD</el-checkbox>
        <el-checkbox label="kdj">KDJ</el-checkbox>
        <el-checkbox label="volume">成交量</el-checkbox>
      </el-checkbox-group>
    </div>
  </div>
</template>
```

#### 4.2.2 资金流水组件 (FundFlow)
```vue
<template>
  <div class="fund-flow">
    <div class="flow-summary">
      <!-- 资金概况 -->
    </div>
    <div class="flow-list">
      <!-- 流水明细 -->
    </div>
  </div>
</template>
```

## 5. 状态管理设计

### 5.1 Store结构
```javascript
// stores/index.js
import { createPinia } from 'pinia'

export const pinia = createPinia()

// stores/modules/
// ├── trading.js      - 交易相关状态
// ├── position.js     - 持仓相关状态
// ├── market.js       - 市场数据状态
// ├── user.js         - 用户信息状态
// └── ui.js           - UI界面状态
```

### 5.2 交易状态管理
```javascript
// stores/trading.js
export const useTradingStore = defineStore('trading', {
  state: () => ({
    // 当前选中股票
    currentStock: null,
    
    // 交易表单
    orderForm: {
      stock_code: '',
      direction: 'buy',
      order_type: 'limit',
      price: 0,
      quantity: 0,
      amount: 0
    },
    
    // 实时行情
    realtimeQuotes: new Map(),
    
    // 五档行情
    depthData: null,
    
    // 当日委托
    todayOrders: [],
    
    // 成交回报
    tradeReports: [],
    
    // 账户资金
    accountInfo: {
      total_assets: 0,
      available_funds: 0,
      position_value: 0,
      today_pnl: 0
    }
  }),
  
  actions: {
    async submitOrder(orderData) {
      // 提交订单逻辑
    },
    
    async cancelOrder(orderId) {
      // 撤销订单逻辑
    },
    
    updateRealtimeQuote(stockCode, quote) {
      this.realtimeQuotes.set(stockCode, quote)
    },
    
    subscribeMarketData(stockCodes) {
      // 订阅实时行情
    }
  }
})
```

## 6. API接口设计

### 6.1 交易相关接口
```javascript
// api/trading.js
export const tradingAPI = {
  // 提交订单
  submitOrder(orderData) {
    return api.post('/trading/orders', orderData)
  },
  
  // 撤销订单
  cancelOrder(orderId) {
    return api.delete(`/trading/orders/${orderId}`)
  },
  
  // 获取委托列表
  getOrders(params) {
    return api.get('/trading/orders', { params })
  },
  
  // 获取成交回报
  getTradeReports(params) {
    return api.get('/trading/trades', { params })
  },
  
  // 获取账户资金
  getAccountInfo() {
    return api.get('/trading/account')
  }
}
```

### 6.2 持仓相关接口
```javascript
// api/position.js
export const positionAPI = {
  // 获取持仓列表
  getPositions() {
    return api.get('/trading/positions')
  },
  
  // 获取持仓详情
  getPositionDetail(stockCode) {
    return api.get(`/trading/positions/${stockCode}`)
  },
  
  // 设置预警
  setAlert(stockCode, alertData) {
    return api.post(`/trading/alerts/${stockCode}`, alertData)
  }
}
```

### 6.3 市场数据接口
```javascript
// api/market.js
export const marketAPI = {
  // 获取实时行情
  getRealtimeQuote(stockCode) {
    return api.get(`/market/quote/${stockCode}`)
  },
  
  // 获取五档行情
  getDepthData(stockCode) {
    return api.get(`/market/depth/${stockCode}`)
  },
  
  // 获取成交明细
  getTradeDetails(stockCode) {
    return api.get(`/market/trades/${stockCode}`)
  },
  
  // 搜索股票
  searchStocks(keyword) {
    return api.get('/market/search', { params: { q: keyword } })
  }
}
```

## 7. 样式设计规范

### 7.1 色彩规范
```scss
// styles/colors.scss
:root {
  // 主色调
  --primary-color: #409EFF;
  --primary-light: #66b1ff;
  --primary-dark: #337ecc;
  
  // 涨跌色彩
  --price-up: #f56c6c;      // 上涨红色
  --price-down: #67c23a;    // 下跌绿色
  --price-flat: #909399;    // 平盘灰色
  
  // 交易色彩
  --buy-color: #f56c6c;     // 买入红色
  --sell-color: #67c23a;    // 卖出绿色
  
  // 状态色彩
  --success-color: #67c23a;
  --warning-color: #e6a23c;
  --danger-color: #f56c6c;
  --info-color: #909399;
  
  // 背景色彩
  --bg-primary: #ffffff;
  --bg-secondary: #f5f7fa;
  --bg-dark: #1f2329;
  
  // 文字色彩
  --text-primary: #303133;
  --text-regular: #606266;
  --text-secondary: #909399;
  --text-placeholder: #c0c4cc;
  
  // 边框色彩
  --border-base: #dcdfe6;
  --border-light: #e4e7ed;
  --border-lighter: #ebeef5;
}
```

### 7.2 组件样式
```scss
// styles/components.scss

// 价格显示样式
.price-display {
  &.price-up {
    color: var(--price-up);
  }
  
  &.price-down {
    color: var(--price-down);
  }
  
  &.price-flat {
    color: var(--price-flat);
  }
}

// 交易按钮样式
.buy-button {
  background-color: var(--buy-color);
  border-color: var(--buy-color);
  
  &:hover {
    background-color: lighten(var(--buy-color), 10%);
  }
}

.sell-button {
  background-color: var(--sell-color);
  border-color: var(--sell-color);
  
  &:hover {
    background-color: darken(var(--sell-color), 10%);
  }
}

// 盈亏显示样式
.profit {
  color: var(--price-up);
}

.loss {
  color: var(--price-down);
}

// 股票信息样式
.stock-code {
  font-family: 'Monaco', 'Consolas', monospace;
  font-weight: bold;
}

.stock-name {
  font-weight: 500;
}

// 数值显示样式
.big-number {
  font-size: 24px;
  font-weight: bold;
}

.amount-display {
  font-family: 'Monaco', 'Consolas', monospace;
}
```

## 8. 响应式设计

### 8.1 断点设置
```scss
// styles/responsive.scss
$breakpoints: (
  'mobile': '(max-width: 768px)',
  'tablet': '(min-width: 769px) and (max-width: 1024px)',
  'desktop': '(min-width: 1025px)',
  'large': '(min-width: 1440px)'
);

@mixin mobile {
  @media #{map-get($breakpoints, 'mobile')} {
    @content;
  }
}

@mixin tablet {
  @media #{map-get($breakpoints, 'tablet')} {
    @content;
  }
}

@mixin desktop {
  @media #{map-get($breakpoints, 'desktop')} {
    @content;
  }
}
```

### 8.2 移动端适配
```vue
<!-- 移动端交易界面简化版 -->
<template>
  <div class="mobile-trading">
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="行情" name="quote">
        <!-- 简化的行情信息 -->
      </el-tab-pane>
      <el-tab-pane label="交易" name="trade">
        <!-- 简化的交易表单 -->
      </el-tab-pane>
      <el-tab-pane label="持仓" name="position">
        <!-- 简化的持仓列表 -->
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
```

## 9. 性能优化

### 9.1 数据更新策略
- 使用WebSocket进行实时数据推送
- 实现数据缓存和本地存储
- 采用虚拟滚动处理大量数据
- 使用防抖和节流优化用户输入

### 9.2 组件优化
- 使用动态导入实现代码分割
- 实现组件懒加载
- 优化图表渲染性能
- 使用memo缓存计算结果

### 9.3 网络优化
- 实现请求缓存机制
- 使用请求合并减少网络调用
- 实现离线模式支持
- 优化图片和静态资源加载

## 10. 安全性设计

### 10.1 交易安全
- 交易密码二次确认
- 交易限额和风控检查
- 敏感信息加密传输
- 防重复提交机制

### 10.2 数据安全
- 敏感数据脱敏显示
- 本地数据加密存储
- 防XSS和CSRF攻击
- API接口权限验证

## 11. 测试策略

### 11.1 单元测试
```javascript
// tests/components/StockPool.test.js
import { mount } from '@vue/test-utils'
import StockPool from '@/components/StockPool.vue'

describe('StockPool', () => {
  test('should render stock list correctly', () => {
    const wrapper = mount(StockPool, {
      props: {
        stockList: mockStockData
      }
    })
    
    expect(wrapper.find('.stock-list').exists()).toBe(true)
    expect(wrapper.findAll('.stock-item')).toHaveLength(mockStockData.length)
  })
})
```

### 11.2 集成测试
- API接口测试
- 交易流程端到端测试
- 数据同步测试
- 性能压力测试

## 12. 部署配置

### 12.1 开发环境配置
```javascript
// vite.config.js
export default defineConfig({
  plugins: [vue()],
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true
      },
      '/ws': {
        target: 'ws://localhost:8000',
        ws: true
      }
    }
  }
})
```

### 12.2 生产环境优化
- 代码压缩和混淆
- 资源CDN加速
- Gzip压缩
- 缓存策略配置

---

## 总结

本设计文档基于现有量化分析系统的技术架构，扩展设计了完整的股票交易系统前端界面。主要特点包括：

1. **模块化设计**：股票池、交易下单、持仓管理三大核心模块
2. **专业化界面**：符合专业交易软件的界面标准和用户习惯
3. **实时性支持**：实时行情推送和交易状态更新
4. **安全性保障**：交易确认机制和数据安全保护
5. **响应式设计**：适配桌面端和移动端多种设备
6. **可扩展性**：基于Vue3生态系统，便于功能扩展和维护

该设计方案充分利用了现有系统的技术基础，在保持一致性的同时，提供了完整的交易功能支持。