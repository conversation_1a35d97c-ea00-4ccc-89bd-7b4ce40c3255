"""
初始化管理员用户的脚本

在应用首次启动或迁移后，可以使用此脚本创建管理员用户。
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到系统路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from sqlalchemy.orm import Session
from app.services.user_service import UserService
from app.models.base import BaseModel
from app.core.sync_database import get_db

def init_admin_user():
    """初始化管理员用户"""
    db: Session = next(get_db())
    
    # 检查是否已有管理员用户
    admin = db.query(BaseModel.metadata.tables['users']).filter_by(username="admin").first()
    
    if admin:
        print("管理员用户已存在，跳过创建")
        return
    
    # 创建管理员用户
    try:
        UserService.create_user(
            db=db,
            username="admin",
            password="admin123",  # 实际应用中应使用更复杂的密码
            email="<EMAIL>",
            is_admin=True
        )
        print("成功创建管理员用户")
    except Exception as e:
        print(f"创建管理员用户失败: {e}")

if __name__ == "__main__":
    init_admin_user()
