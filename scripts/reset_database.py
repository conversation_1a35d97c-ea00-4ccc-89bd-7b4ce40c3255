"""
执行最新的迁移脚本

此脚本会执行最新的数据库迁移并初始化必要的数据。
"""

import os
import sys
import subprocess
from pathlib import Path

# 添加项目根目录到系统路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

def execute_latest_migration():
    """执行最新的迁移脚本"""
    
    print("正在执行最新的数据库迁移...")
    try:
        # 首先获取当前迁移版本
        result = subprocess.run(
            ["alembic", "current"], 
            check=True, 
            capture_output=True, 
            text=True
        )
        current_version = result.stdout.strip()
        print(f"当前数据库版本: {current_version}")
        
        # 检查是否有未执行的迁移
        result = subprocess.run(
            ["alembic", "heads"], 
            check=True, 
            capture_output=True, 
            text=True
        )
        head_version = result.stdout.strip()
        print(f"最新迁移版本: {head_version}")
        
        # 如果当前版本不是最新版本，执行迁移
        if current_version != head_version:
            print("正在升级数据库到最新版本...")
            subprocess.run(["alembic", "upgrade", "head"], check=True)
            print("数据库升级完成。")
        else:
            print("数据库已是最新版本。")
            
        # 直接执行最新的迁移脚本中的SQL语句
        from app.core.sync_database import SessionLocal
        # 导入 text 函数用于创建文本 SQL
        from sqlalchemy import text
        
        db = SessionLocal()
        try:
            # 执行迁移脚本中的SQL语句
            db.execute(text("CREATE TABLE IF NOT EXISTS users (\
                id INTEGER PRIMARY KEY AUTOINCREMENT, \
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP, \
                updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP, \
                username VARCHAR(50) NOT NULL UNIQUE, \
                password_hash VARCHAR(128) NOT NULL, \
                email VARCHAR(100) UNIQUE, \
                is_active BOOLEAN DEFAULT TRUE, \
                is_admin BOOLEAN DEFAULT FALSE, \
                last_login DATETIME)"))
                
            db.execute(text("CREATE TABLE IF NOT EXISTS watchlist_items (\
                id INTEGER PRIMARY KEY AUTOINCREMENT, \
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP, \
                updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP, \
                user_id INTEGER NOT NULL, \
                stock_code VARCHAR(10) NOT NULL, \
                added_at DATETIME DEFAULT CURRENT_TIMESTAMP, \
                sort_order INTEGER DEFAULT 0, \
                FOREIGN KEY(user_id) REFERENCES users(id), \
                UNIQUE(user_id, stock_code))"))
                
            # 创建索引
            db.execute(text("CREATE INDEX IF NOT EXISTS ix_users_username ON users (username)"))
            db.execute(text("CREATE INDEX IF NOT EXISTS ix_users_email ON users (email)"))
            db.execute(text("CREATE INDEX IF NOT EXISTS ix_watchlist_items_user_id ON watchlist_items (user_id)"))
            db.execute(text("CREATE INDEX IF NOT EXISTS ix_watchlist_items_stock_code ON watchlist_items (stock_code)"))
            # 检查是否已有管理员用户
            admin_exists = db.execute(text("SELECT COUNT(*) FROM users WHERE username = 'admin'")).scalar()
            if not admin_exists:
                # 初始化管理员用户 (密码: admin123)
                from app.services.user_service import UserService
                
                UserService.create_user(
                    db=db,
                    username="admin",
                    password="admin123",
                    email="<EMAIL>",
                    is_admin=True
                )
                print("管理员用户创建成功。")
            else:
                print("管理员用户已存在。")
                
            db.commit()
            print("数据库表和数据初始化完成。")
        except Exception as e:
            db.rollback()
            print(f"数据库操作失败: {e}")
        finally:
            db.close()
    except subprocess.CalledProcessError as e:
        print(f"执行迁移命令失败: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"执行过程中发生错误: {e}")
        sys.exit(1)
    
    print("迁移脚本执行完成。")

if __name__ == "__main__":
    execute_latest_migration()
