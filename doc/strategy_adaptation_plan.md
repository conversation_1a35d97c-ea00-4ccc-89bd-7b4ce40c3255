# 策略适配计划：成交量压力与动量双驱策略

## 1. 引言

本文档旨在规划如何将《股票策略文档.txt》中描述的“成交量压力与动量双驱策略”（以下简称“新策略”）整合到当前项目的特征计算框架（参考 `new_feature_formatted.md`）中。目标是利用现有计算模块，实现新策略的核心信号生成逻辑。

## 2. 策略组件与现有框架的映射

### 2.1 数据周期

*   **新策略要求**：日线 (`dh`)。
*   **现有框架**：`new_feature_formatted.md` 中的 `period_candidates` 支持 `dh` 等多种周期。
*   **适配**：完全兼容。可根据新策略需求选择日线数据周期。

### 2.2 数据读取

*   **新策略要求**：OHLCV（开盘价, 最高价, 最低价, 收盘价, 成交量），交易日期，以及用于计算内外盘的数据。
*   **现有框架**：`new_feature_formatted.md` 中已加载 `adj_c_price` (复权收盘价), `o_price` (开盘价), `vol` (成交量), `c_price` (收盘价), `h_price` (最高价), `l_price` (最低价)。内外盘计算依赖 `stock_library.cal_diff(data_open, data_close, h_price, l_price, vol)`。
*   **适配**：基本兼容。现有框架已提供计算内外盘差异 (`diff_vol`) 的基础。

### 2.3 技术指标计算

#### a. 价格布林带 (Price Bollinger Bands)

*   **新策略**：周期为20，用于设定动态止损位 (需要下轨 `dis_Lowr`)。
*   **现有框架**：`[dis_Mid, dis_Uppr, dis_Lowr] = bollinger(ok, twindow)`，其中 `twindow = 20`。`ok` 是基于价格的 `fints` 对象。
*   **适配**：可直接复用。`dis_Lowr` 即为所需的价格布林带下轨。

#### b. 成交量压力指标 (Volume Pressure)

*   **新策略**：基于内外盘成交量差值 (`ex_vol - in_vol`)，进行标准化处理，然后应用EMA平滑 (周期 `lag*2`)。输出为 `log_vol`。
*   **现有框架**：
    1.  计算内外盘成交量差值：`[in_vol, ex_vol] = stock_library.cal_diff(data_open, data_close, h_price, l_price, vol);`
    2.  计算差值：`diff_vol = ex_vol - in_vol;`
    3.  标准化处理：`diff_vol = diff_vol - min(diff_vol) + 1;`
    4.  EMA平滑：`diff_vol = tsmovavg(diff_vol, 'e', lag*2, 1);`
*   **适配**：完全一致。现有框架中的 `diff_vol` (经过上述处理后) 即对应新策略中的 `log_vol`。

#### c. 成交量布林带 (Volume Bollinger Bands)

*   **新策略**：对“成交量压力指标” (`log_vol`) 计算布林带，周期20。其中轨 (`average_vol`) 代表近期买卖情绪的均衡线。
*   **现有框架**：
    1.  `log_vol = diff_vol;` (承接上一指标)
    2.  `ok_vol = fints(dates, log_vol, {'CLOSE'});`
    3.  `[dis_Mid_vol, dis_Uppr_vol, dis_Lowr_vol] = bollinger(ok_vol, twindow);`
    4.  `average_vol = fts2mat(dis_Mid_vol.CLOSE);`
*   **适配**：完全一致。现有框架中的 `average_vol` 即对应新策略中的成交量布林带中轨。

#### d. KDJ指标

*   **新策略**：经典KDJ指标，输出 `price_k, price_d, price_j`。
*   **现有框架**：
    1.  `vin = [h_price, l_price, c_price];`
    2.  `vout = stock_library.indicators(vin, 'kdj');`
    3.  `price_k = vout(:,1); price_d = vout(:,2); price_j = vout(:,3);`
*   **适配**：完全一致。可直接复用。

## 3. 交易信号逻辑适配

为便于后续处理，所有信号点位均以索引表示，相对于原始数据序列。

### 3.1 买入信号 (高精度 AND 逻辑)

*   **新策略条件**:
    1.  成交量压力突破: 当日的“成交量压力指标” (`log_vol`) 从下向上穿过其布林带中轨 (`average_vol`)。
        *   `log_vol(t-1) < average_vol(t-1) AND log_vol(t) > average_vol(t)`
    2.  KDJ金叉: 当日的KDJ指标发生金叉，即 `price_k` 从下向上穿过 `price_d`。
        *   `price_k(t-1) < price_d(t-1) AND price_k(t) > price_d(t)`
*   **适配**: 需要组合这两个条件。

### 3.2 卖出（止盈）信号 (OR 逻辑)

*   **新策略条件**:
    1.  成交量压力衰竭: 当日的“成交量压力指标” (`log_vol`) 从上向下穿过其布林带中轨 (`average_vol`)。
        *   `log_vol(t-1) > average_vol(t-1) AND log_vol(t) < average_vol(t)`
    2.  KDJ死叉: 当日的KDJ指标发生死叉，即 `price_k` 从上向下穿过 `price_d`。
        *   `price_k(t-1) > price_d(t-1) AND price_k(t) < price_d(t)`
*   **适配**: 需要组合这两个条件。

### 3.3 止损信号

*   **新策略条件**: 持仓期间，若任一交易日的收盘价 (`c_price`) 跌破了当日的价格布林带下轨 (`dis_Lowr_val`)。
    *   `c_price(t) < dis_Lowr_val(t)`
*   **适配**: 需要从 `dis_Lowr` (fints对象) 中提取数值序列。

## 4. 实施步骤建议

1.  **确认/提取指标数值**:
    *   `log_vol`: 使用 `new_feature_formatted.md` 中计算得到的成交量内外盘差异（`diff_vol`），经过标准化和EMA平滑处理后的结果。
        *   即 `new_feature_formatted.md` 中 `diff_vol = tsmovavg(diff_vol, 'e', lag*2, 1);` 后的 `diff_vol`。
    *   `average_vol`: 使用 `new_feature_formatted.md` 中计算得到的成交量布林带中轨。
        *   即 `average_vol = fts2mat(dis_Mid_vol.CLOSE);`。
    *   `price_k`, `price_d`: 使用 `new_feature_formatted.md` 中计算得到的KDJ指标的K值和D值。
        *   即 `price_k = vout(:,1); price_d = vout(:,2);`。
    *   `price_bollinger_lower`: 使用 `new_feature_formatted.md` 中计算得到的价格布林带下轨。
        *   例如 `price_bollinger_lower = fts2mat(dis_Lowr.CLOSE);` (或 `fts2mat(dis_Lowr)` 若其为单列)。
    *   `c_price`: 使用 `new_feature_formatted.md` 中加载的收盘价。
        *   即 `c_price = flipud(stockdata.c);`。

2.  **实现买入信号逻辑**:
    ```matlab
    % --- 实现买入信号逻辑 ---
    % 变量来源 (参考 new_feature_formatted.md 和本计划2.3节):
    % log_vol: 处理后的成交量内外盘差异 (diff_vol)
    % average_vol: 成交量布林带中轨 (fts2mat(dis_Mid_vol.CLOSE))
    % price_k: KDJ指标的K线 (vout(:,1))
    % price_d: KDJ指标的D线 (vout(:,2))

    % 确保所有参与比较的向量长度一致，通常为 N。交叉信号比较 t 和 t-1。
    % 初始化信号向量为全零，长度与原始数据一致
    buy_signal_points = zeros(size(log_vol)); 

    % 成交量压力突破条件
    vol_pressure_breakout_condition = (log_vol(1:end-1) < average_vol(1:end-1) & log_vol(2:end) > average_vol(2:end));
    
    % KDJ金叉条件
    kdj_golden_cross_condition = (price_k(1:end-1) < price_d(1:end-1) & price_k(2:end) > price_d(2:end));
    
    % 组合买入条件 (AND逻辑)
    % combined_buy_condition 的长度为 N-1
    combined_buy_condition = vol_pressure_breakout_condition & kdj_golden_cross_condition;
    
    % 找到满足条件的索引，并将其映射回原始 N 长度序列的正确位置 (即信号发生在第 t 天)
    % find 返回的是相对于 combined_buy_condition (长度 N-1) 的索引
    % 加 1 后，这些索引对应于原始数据序列中信号发生的当天 (从第2天开始)
    buy_signal_indices = find(combined_buy_condition) + 1; 
    
    % (可选) 如果需要一个布尔向量标记买入点:
    % buy_signal_points(buy_signal_indices) = 1;
    ```

3.  **实现止盈信号逻辑**:
    ```matlab
    % --- 实现止盈信号逻辑 ---
    % 变量来源与买入信号类似

    % 初始化信号向量
    take_profit_signal_points = zeros(size(log_vol));

    % 成交量压力衰竭条件
    vol_pressure_exhaustion_condition = (log_vol(1:end-1) > average_vol(1:end-1) & log_vol(2:end) < average_vol(2:end));
    
    % KDJ死叉条件
    kdj_death_cross_condition = (price_k(1:end-1) > price_d(1:end-1) & price_k(2:end) < price_d(2:end));
    
    % 组合止盈条件 (OR逻辑)
    % combined_take_profit_condition 的长度为 N-1
    combined_take_profit_condition = vol_pressure_exhaustion_condition | kdj_death_cross_condition;
    
    % 找到满足条件的索引，并将其映射回原始 N 长度序列的信号发生当天
    take_profit_signal_indices = find(combined_take_profit_condition) + 1;
    
    % (可选) 如果需要一个布尔向量标记止盈点:
    % take_profit_signal_points(take_profit_signal_indices) = 1;
    ```

4.  **实现止损信号逻辑**:
    ```matlab
    % --- 实现止损信号逻辑 ---
    % 变量来源:
    % c_price: 收盘价 (flipud(stockdata.c))
    % price_bollinger_lower: 价格布林带下轨 (fts2mat(dis_Lowr.CLOSE) 或 fts2mat(dis_Lowr))

    % 初始化信号向量
    stop_loss_signal_points = zeros(size(c_price));

    % 止损条件是即时的: 当日收盘价 < 当日价格布林带下轨
    % 确保 c_price 和 price_bollinger_lower 长度一致 (N)
    stop_loss_condition = c_price < price_bollinger_lower;
    
    % 找到满足条件的索引 (这些索引直接对应原始数据序列)
    stop_loss_signal_indices = find(stop_loss_condition);
    
    % (可选) 如果需要一个布尔向量标记止损点:
    % stop_loss_signal_points(stop_loss_signal_indices) = 1;
    % 注意：实际应用中止损通常在持仓后每日检查。
    % 这里的 find 是一次性找出所有潜在的原始止损触发点。
    % 在回测或实盘中，应结合当前持仓状态判断是否执行止损。
    ```

5.  **整合与输出**:
    *   将新计算得到的信号索引整合到 `new_feature_formatted.md` 的输出结构中，例如 `bollinger_output`。
    *   建议新增以下字段到 `bollinger_output`:
        *   `bollinger_output.buy_indices_VPKM` (Volume Pressure KDJ Momentum Buy) `= buy_signal_indices;`
        *   `bollinger_output.sell_indices_VPKM_tp` (Take Profit) `= take_profit_signal_indices;`
        *   `bollinger_output.sell_indices_VPKM_sl` (Stop Loss) `= stop_loss_signal_indices;`
    *   原有的 `bollinger_output.sel_buy22` 逻辑可以：
        *   **被完全替代**：如果新策略是唯一使用的买入逻辑。
        *   **保留并与新信号结合**：如果需要同时考虑多种买入信号，可能需要进一步的逻辑来合并或选择。根据策略文档，新策略是核心，应优先考虑替代。
    *   确保输出的信号索引与原始数据的时间序列正确对应。

## 5. 注意事项

*   **数据对齐与长度**: 在进行向量化比较（如 `(1:end-1)` vs `(2:end)`）时，务必确保参与运算的各指标序列已经过正确对齐，并且长度匹配。`fints` 对象转换为 `mat` 后，其日期对齐特性会丢失，需要手动保证。
*   **索引转换**: `find` 函数返回的索引是基于输入向量的。当这些向量是通过切片（如 `(2:end)`）产生时，需要将索引转换回原始数据序列的正确位置。上述示例中 `+1` 操作即为此目的。
*   **信号的实际应用**: 本文档主要关注信号点的识别。在实际交易系统中，买入信号通常用于次日开仓，止盈/止损信号用于当日或次日平仓决策，并需结合持仓状态。
*   **风险与资金管理**: 新策略文档中提及的股票池管理、仓位分配、流动性控制等属于更高级的策略执行层面，不在本特征计算适配计划的核心范围内，但应作为后续完整策略系统实现的一部分。

