# 前后端接口修改计划补充 - 自选股功能

## 1. 自选股功能概述

根据需求，Vue.js前端界面已经实现了自选股功能，包括：
- 自选股列表页面
- 添加/删除自选股的交互
- 自选股统计信息展示

目前前端使用localStorage进行本地存储，但需要后端提供持久化存储和用户绑定功能，确保用户在不同设备上都能访问其自选股列表。与HTML原型(`doc/index.html`)相比，Vue.js前端的自选股功能在界面和交互上有所不同，但功能本质相似。

### 1.1 原型与Vue.js实现的差异

HTML原型与Vue.js前端在自选股功能上的主要差异：

| 方面 | HTML原型实现 | Vue.js前端实现 | 差异处理方案 |
|-----|------------|--------------|-----------|
| 添加自选股 | 弹窗模式，需输入股票代码和名称 | 在股票详情页中通过按钮直接添加 | 后端API支持两种添加方式 |
| 数据存储 | 未明确指定，似乎使用本地JavaScript变量 | 使用localStorage临时存储 | 后端提供统一的数据持久化 |
| 列表视图 | 单一表格视图 | 支持表格和卡片两种视图模式 | API返回包含所有卡片视图所需数据 |
| 数据统计 | 简单的计数统计 | 包含更丰富的涨跌分布、行业分布等统计 | API支持更复杂的统计计算 |

尽管存在这些差异，但基本功能需求一致，即维护用户的自选股列表。本文档提供的API方案将兼容Vue.js前端的实际需求，同时保留扩展性以适应未来可能的功能增强。

## 2. 前端当前实现分析

### 2.1 前端现有功能

前端已经实现了完整的自选股UI，包括：

1. **自选股列表页面**:
   - 表格和卡片两种视图模式
   - 排序和搜索功能
   - 自选股数据统计（总数、涨跌分布等）

2. **股票详情页中的自选功能**:
   - 添加/删除自选股的按钮
   - 自选股状态显示

3. **数据管理**:
   - 使用Pinia存储（`stockData.js`）管理自选股列表
   - 通过`watchlist`数组存储自选股代码
   - 本地存储实现（`localStorage`）

### 2.2 前端代码优化建议

1. 将本地存储方式迁移到API调用：
```javascript
// 修改前
const saveWatchlist = () => {
  localStorage.setItem('watchlist', JSON.stringify(watchlist.value))
}

// 修改后
const saveWatchlist = async () => {
  try {
    await stockService.updateWatchlist(watchlist.value)
  } catch (err) {
    console.error('保存自选股失败:', err)
    // 回退到本地存储作为备用
    localStorage.setItem('watchlist', JSON.stringify(watchlist.value))
  }
}
```

2. 初始化时从API获取自选股列表:
```javascript
const loadWatchlist = async () => {
  try {
    // 优先从API获取
    const response = await stockService.getWatchlist()
    watchlist.value = response.data.map(item => item.stock_code)
  } catch (err) {
    console.error('获取自选股列表失败:', err)
    // 回退到本地存储
    const saved = localStorage.getItem('watchlist')
    if (saved) {
      try {
        watchlist.value = JSON.parse(saved)
      } catch (parseErr) {
        console.error('解析本地自选股列表失败:', parseErr)
        watchlist.value = []
      }
    }
  }
}
```

## 3. 后端实现计划

### 3.1 数据库模型设计

需要创建新的数据库表来存储用户自选股数据：

```python
# app/models/watchlist.py
from sqlalchemy import Column, String, Integer, DateTime, ForeignKey, Table, UniqueConstraint
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from app.models.base import Base

class WatchlistItem(Base):
    """用户自选股表"""
    __tablename__ = "watchlist_items"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    user_id = Column(String(50), index=True, nullable=False, comment="用户ID")
    stock_code = Column(String(10), index=True, nullable=False, comment="股票代码")
    added_at = Column(DateTime, default=func.now(), comment="添加时间")
    sort_order = Column(Integer, default=0, comment="排序顺序")
    price_alert = Column(String(50), nullable=True, comment="价格提醒设置(JSON)")
    
    # 联合唯一约束，确保每个用户对每支股票只能有一条记录
    __table_args__ = (
        UniqueConstraint('user_id', 'stock_code', name='uix_user_stock'),
    )
```

### 3.2 API端点设计

#### 3.2.1 获取自选股列表
```python
@router.get("/watchlist", response_model=CommonResponse[List[WatchlistItemResponse]])
async def get_watchlist(
    current_user_id: str = Query(..., description="用户ID"),
    include_details: bool = Query(False, description="是否包含股票详情"),
    db: AsyncSession = Depends(get_db)
):
    """获取用户自选股列表"""
    try:
        items = await watchlist_service.get_user_watchlist(current_user_id, include_details)
        return CommonResponse(data=items)
    except Exception as e:
        return CommonResponse(success=False, error=str(e), message="获取自选股列表失败")
```

#### 3.2.2 添加自选股
```python
@router.post("/watchlist/add", response_model=CommonResponse[Dict])
async def add_to_watchlist(
    item: WatchlistItemCreate,
    db: AsyncSession = Depends(get_db)
):
    """添加股票到自选列表"""
    try:
        result = await watchlist_service.add_to_watchlist(item.user_id, item.stock_code)
        return CommonResponse(
            data={"status": "success"},
            message="添加自选股成功"
        )
    except Exception as e:
        return CommonResponse(success=False, error=str(e), message="添加自选股失败")
```

#### 3.2.3 移除自选股
```python
@router.delete("/watchlist/remove", response_model=CommonResponse[Dict])
async def remove_from_watchlist(
    user_id: str = Query(..., description="用户ID"),
    stock_code: str = Query(..., description="股票代码"),
    db: AsyncSession = Depends(get_db)
):
    """从自选列表移除股票"""
    try:
        result = await watchlist_service.remove_from_watchlist(user_id, stock_code)
        return CommonResponse(
            data={"status": "success"},
            message="移除自选股成功"
        )
    except Exception as e:
        return CommonResponse(success=False, error=str(e), message="移除自选股失败")
```

#### 3.2.4 批量更新自选股
```python
@router.post("/watchlist/update", response_model=CommonResponse[Dict])
async def update_watchlist(
    update: WatchlistUpdate,
    db: AsyncSession = Depends(get_db)
):
    """批量更新自选股列表"""
    try:
        result = await watchlist_service.update_watchlist(update.user_id, update.stock_codes)
        return CommonResponse(
            data={"updated_count": result},
            message="更新自选股列表成功"
        )
    except Exception as e:
        return CommonResponse(success=False, error=str(e), message="更新自选股列表失败")
```

### 3.3 服务层实现

创建新的服务类来处理自选股相关逻辑：

```python
# app/services/watchlist_service.py
from typing import List, Dict, Optional, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete, and_, func
from sqlalchemy.exc import IntegrityError

from app.models.watchlist import WatchlistItem
from app.services.storage.stock_storage import StockStorageService

class WatchlistService:
    def __init__(self, db: AsyncSession):
        self.db = db
        self.stock_storage = StockStorageService(db)
    
    async def get_user_watchlist(self, user_id: str, include_details: bool = False) -> List[Dict[str, Any]]:
        """获取用户自选股列表"""
        query = select(WatchlistItem).where(WatchlistItem.user_id == user_id) \
                .order_by(WatchlistItem.sort_order.asc(), WatchlistItem.added_at.desc())
        
        result = await self.db.execute(query)
        items = result.scalars().all()
        
        if not items:
            return []
        
        if include_details:
            # 获取股票详情
            stock_codes = [item.stock_code for item in items]
            stocks_info = await self.stock_storage.get_stocks_by_codes(stock_codes)
            
            # 将自选股项与股票信息合并
            merged_items = []
            for item in items:
                stock_info = next((s for s in stocks_info if s["stock_code"] == item.stock_code), {})
                merged_items.append({
                    "id": item.id,
                    "stock_code": item.stock_code,
                    "added_at": item.added_at.isoformat() if item.added_at else None,
                    "sort_order": item.sort_order,
                    "price_alert": item.price_alert,
                    "stock_info": stock_info
                })
            return merged_items
        
        return [
            {
                "id": item.id,
                "stock_code": item.stock_code,
                "added_at": item.added_at.isoformat() if item.added_at else None,
                "sort_order": item.sort_order,
                "price_alert": item.price_alert
            }
            for item in items
        ]
    
    async def add_to_watchlist(self, user_id: str, stock_code: str) -> bool:
        """添加股票到自选列表"""
        try:
            # 首先检查股票是否存在
            stock = await self.stock_storage.get_stock_by_code(stock_code)
            if not stock:
                raise ValueError(f"股票代码 {stock_code} 不存在")
            
            # 获取当前最大排序值
            query = select(func.max(WatchlistItem.sort_order)) \
                    .where(WatchlistItem.user_id == user_id)
            result = await self.db.execute(query)
            max_order = result.scalar() or 0
            
            # 创建新的自选股项
            new_item = WatchlistItem(
                user_id=user_id,
                stock_code=stock_code,
                sort_order=max_order + 1
            )
            
            self.db.add(new_item)
            await self.db.commit()
            return True
        except IntegrityError:
            # 如果唯一约束冲突，说明已经添加过
            await self.db.rollback()
            return False
        except Exception as e:
            await self.db.rollback()
            raise e
    
    async def remove_from_watchlist(self, user_id: str, stock_code: str) -> bool:
        """从自选列表移除股票"""
        stmt = delete(WatchlistItem).where(
            and_(
                WatchlistItem.user_id == user_id,
                WatchlistItem.stock_code == stock_code
            )
        )
        
        result = await self.db.execute(stmt)
        await self.db.commit()
        return result.rowcount > 0
    
    async def update_watchlist(self, user_id: str, stock_codes: List[str]) -> int:
        """批量更新用户自选股列表"""
        # 删除所有不在新列表中的项
        query = delete(WatchlistItem) \
                .where(
                    and_(
                        WatchlistItem.user_id == user_id,
                        ~WatchlistItem.stock_code.in_(stock_codes)
                    )
                )
        
        await self.db.execute(query)
        
        # 添加新的项
        added_count = 0
        for i, code in enumerate(stock_codes):
            try:
                # 检查是否已存在
                query = select(WatchlistItem) \
                        .where(
                            and_(
                                WatchlistItem.user_id == user_id,
                                WatchlistItem.stock_code == code
                            )
                        )
                result = await self.db.execute(query)
                exist_item = result.scalar_one_or_none()
                
                if exist_item:
                    # 更新排序
                    exist_item.sort_order = i
                else:
                    # 添加新项
                    new_item = WatchlistItem(
                        user_id=user_id,
                        stock_code=code,
                        sort_order=i
                    )
                    self.db.add(new_item)
                    added_count += 1
            except Exception:
                continue
        
        await self.db.commit()
        return added_count
```

### 3.4 数据模型定义

```python
# app/schemas/watchlist.py
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime

class WatchlistItemBase(BaseModel):
    stock_code: str = Field(..., description="股票代码")

class WatchlistItemCreate(WatchlistItemBase):
    user_id: str = Field(..., description="用户ID")
    sort_order: Optional[int] = Field(0, description="排序顺序")
    price_alert: Optional[str] = Field(None, description="价格提醒设置(JSON)")

class WatchlistItemResponse(WatchlistItemBase):
    id: int = Field(..., description="ID")
    added_at: Optional[str] = Field(None, description="添加时间")
    sort_order: int = Field(0, description="排序顺序")
    price_alert: Optional[str] = Field(None, description="价格提醒设置")
    stock_info: Optional[Dict[str, Any]] = Field(None, description="股票详情")

    class Config:
        orm_mode = True

class WatchlistUpdate(BaseModel):
    user_id: str = Field(..., description="用户ID")
    stock_codes: List[str] = Field(..., description="股票代码列表，按顺序排列")
```

## 4. 前后端接口映射

| 前端方法 | API接口 | HTTP方法 | 说明 |
|---------|---------|---------|-----|
| loadWatchlist | `/api/v1/watchlist?user_id={userId}` | GET | 获取自选股列表 |
| addToWatchlist | `/api/v1/watchlist/add` | POST | 添加自选股 |
| removeFromWatchlist | `/api/v1/watchlist/remove?user_id={userId}&stock_code={code}` | DELETE | 删除自选股 |
| saveWatchlist | `/api/v1/watchlist/update` | POST | 批量更新自选股列表 |

## 5. 前端服务层修改

在 `stockService.js` 中添加自选股相关API调用：

```javascript
// 获取用户自选股列表
getWatchlist(userId, includeDetails = true) {
  return apiClient.get('/v1/watchlist', {
    params: { 
      current_user_id: userId,
      include_details: includeDetails 
    }
  })
},

// 添加股票到自选列表
addToWatchlist(userId, stockCode) {
  return apiClient.post('/v1/watchlist/add', {
    user_id: userId,
    stock_code: stockCode
  })
},

// 从自选列表移除股票
removeFromWatchlist(userId, stockCode) {
  return apiClient.delete('/v1/watchlist/remove', {
    params: {
      user_id: userId,
      stock_code: stockCode
    }
  })
},

// 批量更新自选股列表
updateWatchlist(userId, stockCodes) {
  return apiClient.post('/v1/watchlist/update', {
    user_id: userId,
    stock_codes: stockCodes
  })
}
```

## 6. 实现步骤

1. 后端:
   - 创建 `app/models/watchlist.py` 文件，定义自选股数据表模型
   - 创建 `app/schemas/watchlist.py` 文件，定义请求和响应数据模型
   - 创建 `app/services/watchlist_service.py` 文件，实现自选股相关业务逻辑
   - 创建 `app/api/endpoints/watchlist.py` 文件，实现自选股API接口
   - 在 `app/api/__init__.py` 中注册自选股路由

2. 前端:
   - 修改 `stockService.js`，添加自选股相关API调用方法
   - 更新 Pinia Store 中的自选股操作方法，使用API而不是localStorage
   - 修改前端页面组件，增加处理API错误和加载状态的逻辑

3. 迁移数据:
   - 编写脚本，将用户localStorage中的自选股数据迁移到后端数据库（可选）

## 7. 测试计划

1. API测试:
   - 测试添加自选股功能
   - 测试删除自选股功能
   - 测试批量更新自选股列表
   - 测试获取自选股列表，包含和不包含详情两种情况

2. 前端测试:
   - 测试在不同用户间的自选股隔离
   - 测试前端自选股页面的展示和互动功能
   - 测试在网络不稳定情况下的降级处理

3. 集成测试:
   - 测试完整的添加、删除、查看自选股流程
   - 测试自选股数据的持久化
