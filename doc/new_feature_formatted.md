# 量化交易特征计算文档

## 1. 选择数据周期

```
switch period_index
    case 1
        period_candidates = 'dh';    % 日线
    case 2
        period_candidates = 'wh';    % 周线
    case 3
        period_candidates = 'mh';    % 月线
    case 4
        period_candidates = '15m';   % 15分钟线
    case 5
        period_candidates = '30m';   % 30分钟线
    case 6
        period_candidates = '60m';   % 60分钟线
end
```

## 2. 读取数据

```
% 读取并翻转股票数据
adj_c_price = flipud(stockdata.c);    % 复权收盘价
o_price     = flipud(stockdata.o);    % 开盘价
vol         = flipud(stockdata.v);    % 成交量
c_price     = flipud(stockdata.c);    % 收盘价
h_price     = flipud(stockdata.h);    % 最高价
l_price     = flipud(stockdata.l);    % 最低价
```

## 3. 技术指标计算

### a. Bo<PERSON><PERSON> Bands (布林带)

```
% 设置布林带窗口大小
twindow = 20;

% 构建金融时间序列对象
sername = {'CLOSE', 'OPEN', 'HIGH', 'LOW', 'VOLUME'};
ok = fints(dates, [c_price, o_price, h_price, l_price, vol], sername);
bollinger_output.ok = ok;

% 计算布林带
[dis_Mid, dis_Uppr, dis_Lowr] = bollinger(ok, twindow);
```

### b. 成交量内外盘差异

```
% 计算内外盘
[in_vol, ex_vol] = stock_library.cal_diff(data_open, data_close, h_price, l_price, vol);

% 计算内外盘差异
diff_vol = ex_vol - in_vol;

% 确保log(diff_vol)最小值为1
diff_vol = diff_vol - min(diff_vol) + 1;
diff_vol_alternative = diff_vol_alternative - min(diff_vol_alternative) + 1;

% 保存原始差异值
tem_diff_vol = diff_vol;

% 计算指数移动平均
diff_vol = tsmovavg(diff_vol, 'e', lag*2, 1);
```

### c. 基于成交量的布林带指标

```
% 使用成交量差异作为数据源
log_vol = diff_vol;

% 构建金融时间序列对象
sername = {'CLOSE'};
ok_vol = fints(dates, log_vol, sername);
bollinger_output.ok_vol = ok_vol;

% 计算布林带
[dis_Mid_vol, dis_Uppr_vol, dis_Lowr_vol] = bollinger(ok_vol, twindow);

% 保存布林带数据
bollinger_output.dis_CloseBolling_vol = [
    dis_Mid_vol.CLOSE, 
    dis_Uppr_vol.CLOSE,
    dis_Lowr_vol.CLOSE
];

% 转换为矩阵格式
average_vol = fts2mat(dis_Mid_vol.CLOSE);
uppr_vol = fts2mat(dis_Uppr_vol);
lowr_vol = fts2mat(dis_Lowr_vol);
```

### d. KDJ指标

```
% 计算KDJ指标
vin = [h_price, l_price, c_price];
vout = stock_library.indicators(vin, 'kdj');
price_k = vout(:,1);
price_d = vout(:,2);
price_j = vout(:,3);
```

### e. 计算买卖点

```
% 同时满足成交量突破均线或K线金叉D线的点位
bollinger_output.sel_buy22 = find(
    (log_vol(1:end-1) < average_vol(1:end-1) & log_vol(2:end) > average_vol(2:end)) | 
    (price_k(1:end-1) < price_d(1:end-1) & price_k(2:end) > price_d(2:end))
) + 1;
```
