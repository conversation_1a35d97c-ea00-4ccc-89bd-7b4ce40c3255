# 前端项目开发规划 (Vue3 + Vite + SSR)

本文档旨在规划基于现有 `doc/index.html` 原型，使用 Vue3、Vite 及相关技术栈构建一个新的前端界面。

## 1. 技术栈选型

*   **核心包管理**: npm
*   **核心框架**: Vue 3
*   **构建工具**: Vite
*   **编程语言**: JavaScript
*   **服务端渲染 (SSR)**: Vite SSR
*   **CSS 预处理器**: SCSS
*   **UI 组件库**: Element Plus
*   **状态管理**: Pinia
*   **原子化 CSS**: UnoCSS
*   **Vue 组合式函数库**: VueUse
*   **自动导入插件**:
    *   `unplugin-vue-components/vite` (组件按需自动导入)
    *   `unplugin-auto-import/vite` (API 自动导入)
*   **HTTP 客户端**: Axios (或 Fetch API 配合 VueUse 的 `useFetch`)
*   **路由**: Vue Router

## 2. 项目初始化与设置

1.  **创建 Vite 项目**:
    *   在 `quantization` 工作区根目录下创建一个新的前端项目文件夹，例如 `frontend-app`。
    *   使用 Vite 初始化 Vue3 + JavaScript 项目：
        ```bash
        npm create vite@latest frontend-app -- --template vue
        # 或者 yarn create vite frontend-app --template vue
        # 或者 pnpm create vite frontend-app --template vue
        ```
    *   进入项目目录: `cd frontend-app`

2.  **安装核心依赖**:
    ```bash
    npm install vue-router pinia axios sass element-plus unocss @vueuse/core
    # 或者 yarn add vue-router pinia axios sass element-plus unocss @vueuse/core
    # 或者 pnpm add vue-router pinia axios sass element-plus unocss @vueuse/core
    ```
    *   执行上述命令后，这些依赖项会自动添加到 `frontend-app/package.json` 文件中。
    *   **或者**，您可以先在 `frontend-app/package.json` 文件中手动定义这些依赖，然后在 `frontend-app` 目录下运行 `npm install` (或 `yarn install` / `pnpm install`)。

3.  **安装 Vite 插件 (开发依赖)**:
    ```bash
    npm install -D unplugin-vue-components unplugin-auto-import @vitejs/plugin-vue
    # 或者 yarn add -D unplugin-vue-components unplugin-auto-import @vitejs/plugin-vue
    # 或者 pnpm add -D unplugin-vue-components unplugin-auto-import @vitejs/plugin-vue
    ```
    *   `@vitejs/plugin-vue` 通常已包含在 Vue 模板中，确保其存在并为最新。

## 3. 项目结构规划

```
frontend-app/
├── public/                     # 静态资源 (会被直接复制)
├── src/
│   ├── assets/                 # 模块静态资源 (会被 Vite 处理)
│   │   └── scss/
│   │       ├── _variables.scss # SCSS 变量 (迁移自 index.html)
│   │       ├── _mixins.scss    # SCSS Mixins
│   │       └── main.scss       # 全局 SCSS 入口
│   ├── components/             # 全局/共享 Vue 组件
│   │   ├── common/             # 通用基础组件
│   │   └── layout/             # 布局相关组件 (如 Sidebar, Navbar)
│   ├── layouts/                # 页面布局组件
│   ├── pages/ (或 views/)      # 页面级 Vue 组件
│   ├── router/
│   │   └── index.js            # Vue Router 配置
│   ├── store/
│   │   └── index.js            # Pinia store 主入口
│   │   └── modules/            # Pinia 模块
│   ├── services/ (或 api/)
│   │   └── apiClient.js        # Axios 实例和 API 请求封装
│   ├── App.vue                 # 根 Vue 组件
│   ├── main.js                 # 客户端应用入口 (会调整以支持 SSR)
│   ├── entry-client.js         # SSR 客户端入口
│   └── entry-server.js         # SSR 服务器端入口
├── index.html                  # Vite 应用 HTML 模板
├── vite.config.js              # Vite 配置文件
├── uno.config.js               # UnoCSS 配置文件
├── package.json
└── server.js                   # (可选) 用于 SSR 的 Node.js Express 服务器
```

## 4. Vite 配置 (`vite.config.js`)

*   **基本配置**: 引入 `@vitejs/plugin-vue`。
*   **路径别名**: 配置 `@` 指向 `src/`。
*   **Element Plus 自动导入**:
    *   使用 `unplugin-vue-components/vite`。
    *   配置 `ElementPlusResolver`。
*   **API 自动导入**:
    *   使用 `unplugin-auto-import/vite`。
    *   配置导入 Vue, Vue Router, Pinia, VueUse 的 API。
*   **UnoCSS**:
    *   引入 UnoCSS 插件。
*   **SSR 配置**:
    *   根据 Vite 官方 SSR 文档进行配置。
    *   指定 `build.ssr` 和 `build.outDir`。
*   **SCSS 全局变量**:
    *   通过 `css.preprocessorOptions.scss.additionalData` 自动导入 SCSS 变量和 mixins 文件。

## 5. UnoCSS 配置 (`uno.config.js`)

*   配置预设 (e.g., `@unocss/preset-uno`, `@unocss/preset-attributify`, `@unocss/preset-icons`)。
*   可以考虑将 `doc/index.html` 中的一些原子化样式（如 Tailwind 类名）通过 UnoCSS 实现。

## 6. SSR 实现步骤

1.  **创建 `entry-client.js`**: 客户端激活逻辑。
2.  **创建 `entry-server.js`**: 服务器端渲染逻辑，导出 `render` 函数。
3.  **修改 `main.js`**: 创建一个 `createApp` 函数，供客户端和服务端入口共用。
4.  **修改 `index.html`**:
    *   添加 `<!--ssr-outlet-->` 作为 Vue 应用挂载点。
    *   添加预加载链接和状态注入脚本的占位符。
5.  **配置 `vite.config.js`** 以支持 SSR 构建。
6.  **创建 `server.js` (Node.js/Express)**:
    *   提供静态资源服务。
    *   处理所有路由 (`*`)，调用 `entry-server.js` 的 `render` 函数生成 HTML。
    *   将渲染后的 HTML 返回给客户端。
    *   或者，研究如何将 Vite SSR 中间件集成到现有的 Python 后端 (FastAPI) 中，这可能更复杂。

## 7. 样式迁移与组件化

1.  **CSS 变量迁移**:
    *   将 `doc/index.html` 中 `:root` 和 `.light-theme` 下的 CSS 变量转换为 SCSS 变量，存放在 `src/assets/scss/_variables.scss`。
    *   实现主题切换逻辑 (暗色/亮色)。
2.  **全局样式**:
    *   将 `doc/index.html` 中的通用样式 (如 `body`, `card`, `btn-primary` 等) 迁移到 `src/assets/scss/main.scss` 或拆分到更细化的 SCSS 文件中。
    *   确保这些样式与 Element Plus 和 UnoCSS 协同工作，避免冲突。
3.  **HTML 结构组件化**:
    *   分析 `doc/index.html` 的布局和各个区块。
    *   **侧边栏 (`<div class="sidebar">`)**: 创建 `Sidebar.vue` 组件。
    *   **主内容区域**: 可能包含多个子组件，如仪表盘卡片、图表容器、数据表格等。
    *   **卡片 (`.card`, `.glass-card`)**: 创建可复用的 `Card.vue` 组件。
    *   **图表容器 (`.chart-container`)**: 创建 `ChartContainer.vue` 组件，内部使用 ECharts。
    *   **按钮 (`.btn-primary`, `.btn-secondary`)**: 可以直接使用 Element Plus 的 `el-button` 并通过自定义样式或 UnoCSS 扩展。
    *   **输入框 (`.input-field`)**: 使用 Element Plus 的 `el-input`。
4.  **ECharts 集成**:
    *   在需要图表的组件中引入 ECharts。
    *   封装 ECharts 初始化和更新逻辑。
    *   考虑使用 Vue-ECharts 等封装库。

## 8. 状态管理 (Pinia)

*   **创建 Stores**:
    *   `themeStore`: 管理亮色/暗色主题切换。
    *   `userStore`: (如果需要) 管理用户信息和认证。
    *   `stockDataStore`: 管理股票数据、K线数据等。
    *   `uiStore`: 管理全局 UI 状态，如侧边栏折叠状态、弹窗显示等。
*   在组件中使用 `storeToRefs` 和 action。

## 9. 路由 (Vue Router)

*   **定义路由**:
    *   `/`: 主页/仪表盘。
    *   `/stock/:id`: 股票详情页。
    *   `/settings`: 设置页面。
    *   ... 其他根据 `doc/index.html` 功能推断的页面。
*   使用嵌套路由和布局组件。

## 10. API 对接

*   在 `src/services/apiClient.js` 中配置 Axios 实例 (baseURL, interceptors)。
*   为不同的数据实体创建 API 服务函数 (e.g., `stockService.js`)。
*   在 Pinia store 或组件中调用这些服务函数获取数据。

## 11. 构建与部署

*   **开发模式**: `npm run dev` (Vite 会处理 SSR 的 HMR)。
*   **生产构建**:
    *   `npm run build:client` (构建客户端资源)
    *   `npm run build:server` (构建 SSR bundle)
    *   (需要在 `package.json` 中定义这些脚本，例如: `vite build --ssrManifest --outDir dist/client` 和 `vite build --ssr src/entry-server.js --outDir dist/server`)
*   **运行 SSR 服务器**: `node server.js` (如果使用独立的 Node.js 服务器)。
*   **部署**:
    *   将 `dist/client`, `dist/server` 和 `server.js` (如果使用) 部署到服务器。
    *   或者将 SSR 构建产物集成到 Python 后端服务流程中。

## 12. 现有 `doc/index.html` 内容的利用

*   **CSS 变量和样式**: 作为 SCSS 样式的基础。
*   **HTML 结构**: 作为 Vue 组件模板的参考。
*   **JavaScript 逻辑 (如果有)**: 分析其功能，并用 Vue 的方式重新实现 (例如，ECharts 的初始化和交互)。

## 13. 注意事项

*   **Element Plus 样式覆盖**: 如果需要深度定制 Element Plus 组件样式，可以使用 SCSS 变量覆盖或更具体的选择器。
*   **UnoCSS 与 SCSS 协作**: UnoCSS 用于快速实现原子样式，SCSS 用于全局样式、复杂组件样式和主题管理。
*   **SSR 的复杂性**: SSR 会增加项目的复杂性，特别是在状态管理、路由和第三方库的兼容性方面。确保充分测试。
*   **与后端集成**: 如果选择将 SSR 集成到 Python 后端，需要研究合适的方案，例如通过 Node.js 子进程调用 Vite SSR 服务，或寻找 Python 端的 JavaScript 执行环境来运行 SSR bundle。

这个规划提供了一个全面的起点。在实际开发过程中，可以根据具体需求进行调整。

## 14. 开发环境前后端协同运行

在开发阶段，为了方便联调，我们希望在启动 Python 后端服务的同时，自动启动前端的 Vite 开发服务器。以下是两种常见的方法：

### 方法一：使用 Python 脚本 (`run_dev.py`)

您可以在项目根目录 (`d:\\workspace\\quantization`) 创建一个名为 `run_dev.py` 的 Python 脚本，使用 `subprocess` 模块来并行启动后端和前端服务。

**前提条件:**

*   确保您已经按照本文档第 2 节的步骤创建了 `frontend-app` 目录并初始化了 Vite 项目。
*   Python 后端启动命令（例如 `uvicorn app.main:app --reload --host 0.0.0.0 --port 8000`）。
*   前端启动命令（例如 `pnpm dev`，在 `frontend-app` 目录下执行）。

**示例 `run_dev.py`:**

```python
import subprocess
import os
import signal
import sys
import platform

# --- 配置 ---
# Python 后端项目的根目录 (run_dev.py 所在的目录)
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))

# 后端配置
BACKEND_COMMAND = "uvicorn app.main:app --reload --host 0.0.0.0 --port 8000"
BACKEND_WORKING_DIR = PROJECT_ROOT # FastAPI 应用在项目根目录的 app 文件夹内

# 前端配置
FRONTEND_DIR_NAME = "frontend-app" # 前端项目的文件夹名
FRONTEND_WORKING_DIR = os.path.join(PROJECT_ROOT, FRONTEND_DIR_NAME)
FRONTEND_COMMAND = "pnpm dev" # 或者 npm run dev, yarn dev

# --- 执行 ---
backend_process = None
frontend_process = None

try:
    print(f"启动后端服务于: {BACKEND_WORKING_DIR}")
    print(f"命令: {BACKEND_COMMAND}")
    backend_process = subprocess.Popen(BACKEND_COMMAND, shell=True, cwd=BACKEND_WORKING_DIR)

    if not os.path.isdir(FRONTEND_WORKING_DIR):
        print(f"错误: 前端目录 '{FRONTEND_WORKING_DIR}' 不存在。")
        print(f"请先在 '{PROJECT_ROOT}' 下创建 '{FRONTEND_DIR_NAME}' 目录并初始化前端项目。")
        sys.exit(1)

    print(f"启动前端开发服务器于: {FRONTEND_WORKING_DIR}")
    print(f"命令: {FRONTEND_COMMAND}")
    frontend_process = subprocess.Popen(FRONTEND_COMMAND, shell=True, cwd=FRONTEND_WORKING_DIR)

    # 等待任一进程结束
    while True:
        if backend_process.poll() is not None or frontend_process.poll() is not None:
            break
        # 可以加入短暂休眠以降低 CPU 占用
        try:
            signal.pause() # 在非 Windows 系统上更有效
        except AttributeError: # Windows 没有 signal.pause()
            import time
            time.sleep(0.5)


except KeyboardInterrupt:
    print("\\n检测到 Ctrl+C，正在关闭所有服务...")

finally:
    if frontend_process and frontend_process.poll() is None:
        print("正在终止前端服务...")
        if platform.system() == "Windows":
            subprocess.call(['taskkill', '/F', '/T', '/PID', str(frontend_process.pid)])
        else:
            frontend_process.terminate()
            try:
                frontend_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                frontend_process.kill()
        print("前端服务已终止。")

    if backend_process and backend_process.poll() is None:
        print("正在终止后端服务...")
        if platform.system() == "Windows":
             # Uvicorn 在 Windows 上通常能通过 Ctrl+C 事件优雅关闭，但 Popen 的 terminate 可能不够
             # 如果后端是 uvicorn，它应该能响应 SIGINT/Ctrl+C
            subprocess.call(['taskkill', '/F', '/T', '/PID', str(backend_process.pid)])
        else:
            backend_process.terminate()
            try:
                backend_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                backend_process.kill()
        print("后端服务已终止。")
    print("所有服务已关闭。")

```
**使用方法:**
在项目根目录 (`d:\\workspace\\quantization`) 通过 `python run_dev.py` 命令启动。
