/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ChartContainer: typeof import('./src/components/analysis/ChartContainer.vue')['default']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    HelloWorld: typeof import('./src/components/HelloWorld.vue')['default']
    Icon: typeof import('./src/components/common/Icon.vue')['default']
    IconButton: typeof import('./src/components/common/IconButton.vue')['default']
    IndicatorSettings: typeof import('./src/components/analysis/IndicatorSettings.vue')['default']
    LoadingOverlay: typeof import('./src/components/common/LoadingOverlay.vue')['default']
    Navbar: typeof import('./src/components/layout/Navbar.vue')['default']
    NotificationContainer: typeof import('./src/components/common/NotificationContainer.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Sidebar: typeof import('./src/components/layout/Sidebar.vue')['default']
    StatCard: typeof import('./src/components/common/StatCard.vue')['default']
  }
}
