<template>
  <button 
    :class="[
      'icon-button',
      `icon-button--${variant}`,
      `icon-button--${size}`,
      { 
        'icon-button--loading': loading,
        'icon-only': !$slots.default
      }
    ]"
    :disabled="disabled || loading"
    :title="computedTitle"
    @click="$emit('click', $event)"
  >    <Icon v-if="!loading" :name="icon" :class="iconClass" />
    <div v-if="loading" class="loading-spinner"></div>
    <span v-if="$slots.default" ref="slotRef" class="button-text">
      <slot />
    </span>
  </button>
</template>

<script setup>
import { computed, ref, onMounted } from 'vue'
import Icon from './Icon.vue'

const props = defineProps({
  icon: {
    type: String,
    required: true
  },
  variant: {
    type: String,
    default: 'primary',
    validator: (value) => ['primary', 'secondary', 'success', 'danger', 'warning', 'info', 'ghost'].includes(value)
  },
  size: {
    type: String,
    default: 'md',
    validator: (value) => ['xs', 'sm', 'md', 'lg', 'xl'].includes(value)
  },
  disabled: {
    type: Boolean,
    default: false
  },
  loading: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: ''
  }
})

defineEmits(['click'])

const slotRef = ref(null)

const iconClass = computed(() => {
  const sizeMap = {
    xs: 'text-xs',
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl'
  }
  return sizeMap[props.size] || 'text-base'
})

const computedTitle = computed(() => {
  if (props.title) {
    return props.title
  }
  // 如果 title 为空且有 slot 内容，尝试获取 slot 中的文本
  if (slotRef.value) {
    return slotRef.value.textContent || ''
  }
  return ''
})
</script>

<style scoped lang="scss">
.icon-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
  outline: none;
  border-radius: 0.5rem;
  cursor: pointer;
  
  &:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    box-shadow: 0 0 0 2px var(--bg-primary), 0 0 0 4px var(--accent-primary);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  // 尺寸变体
  &--xs {
    padding: 0.375rem;
    font-size: 0.75rem;
    border-radius: 0.375rem;
    min-width: 1.75rem;
    min-height: 1.75rem;
    
    // 只有图标时的紧凑样式
    &.icon-only {
      padding: 0.25rem;
      min-width: 1.5rem;
      min-height: 1.5rem;
    }
  }
  
  &--sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 0.5rem;
  }
  
  &--md {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    border-radius: 0.5rem;
  }
  
  &--lg {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    border-radius: 0.75rem;
  }
  
  &--xl {
    padding: 1rem 2rem;
    font-size: 1.125rem;
    border-radius: 0.75rem;
  }
  
  // 颜色变体
  &--primary {
    background: var(--accent-primary);
    color: white;
    border: 1px solid var(--accent-primary);
    
    &:hover:not(:disabled) {
      background: #2563eb;
      border-color: #2563eb;
    }
    
    &:active:not(:disabled) {
      background: #1d4ed8;
    }
  }
  
  &--secondary {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    
    &:hover:not(:disabled) {
      background: var(--bg-quaternary);
      border-color: var(--border-light);
    }
  }
  
  &--success {
    background: var(--price-up);
    color: white;
    border: 1px solid var(--price-up);
    
    &:hover:not(:disabled) {
      background: #059669;
    }
  }
  
  &--danger {
    background: var(--price-down);
    color: white;
    border: 1px solid var(--price-down);
    
    &:hover:not(:disabled) {
      background: #dc2626;
    }
  }
  
  &--warning {
    background: var(--volume-color);
    color: white;
    border: 1px solid var(--volume-color);
    
    &:hover:not(:disabled) {
      background: #d97706;
    }
  }
  
  &--info {
    background: var(--accent-tertiary);
    color: white;
    border: 1px solid var(--accent-tertiary);
    
    &:hover:not(:disabled) {
      background: #0891b2;
    }
  }
    &--ghost {
    background: transparent;
    color: var(--text-muted);
    border: 1px solid transparent;
    
    &:hover:not(:disabled) {
      background: var(--bg-tertiary);
      color: var(--text-primary);
      border-color: var(--border-light);
    }
    
    &:active:not(:disabled) {
      background: var(--bg-quaternary);
    }
  }
  
  // 加载状态
  &--loading {
    cursor: wait;
  }
  
  // 图标和文字对齐
  .button-text {
    display: flex;
    align-items: center;
  }
  
  // 加载动画
  .loading-spinner {
    width: 1rem;
    height: 1rem;
    border: 2px solid white;
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// 工具提示样式
.icon-button[title] {
  position: relative;
}

.icon-button[title]:hover::after {
  content: attr(title);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: var(--bg-tertiary);
  color: var(--text-primary);
  padding: 6px 10px;
  border-radius: 6px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 1000;
  margin-bottom: 4px;
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-lg);
  animation: tooltipFadeIn 0.2s ease-out;
}

.icon-button[title]:hover::before {
  content: '';
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid var(--bg-tertiary);
  z-index: 1001;
}

@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(4px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}
</style>
