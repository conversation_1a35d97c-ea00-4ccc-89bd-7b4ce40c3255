<template>
  <teleport to="body">
    <div class="notification-container fixed top-4 right-4 z-50 space-y-2">
      <transition-group name="notification" tag="div">
        <div
          v-for="notification in uiStore.notifications"
          :key="notification.id"
          :class="[
            'notification-item p-4 rounded-lg shadow-xl max-w-sm',
            getNotificationClass(notification.type)
          ]"
        >
          <div class="flex items-start space-x-3">
            <i :class="getNotificationIcon(notification.type)" class="text-lg mt-0.5"></i>
            <div class="flex-1">
              <h4 v-if="notification.title" class="font-semibold mb-1">
                {{ notification.title }}
              </h4>
              <p class="text-sm">{{ notification.message }}</p>
            </div>
            <button
              @click="uiStore.removeNotification(notification.id)"
              class="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <i class="i-carbon-close text-sm"></i>
            </button>
          </div>
        </div>
      </transition-group>
    </div>
  </teleport>
</template>

<script setup>
import { useUiStore } from '@/store'

const uiStore = useUiStore()

// 获取通知样式类
const getNotificationClass = (type) => {
  const classes = {
    success: 'bg-green-500/10 border border-green-500/20 text-green-400',
    error: 'bg-red-500/10 border border-red-500/20 text-red-400',
    warning: 'bg-yellow-500/10 border border-yellow-500/20 text-yellow-400',
    info: 'bg-blue-500/10 border border-blue-500/20 text-blue-400'
  }
  return classes[type] || classes.info
}

// 获取通知图标
const getNotificationIcon = (type) => {
  const icons = {
    success: 'i-carbon-checkmark-filled',
    error: 'i-carbon-error-filled',
    warning: 'i-carbon-warning-filled',
    info: 'i-carbon-information-filled'
  }
  return icons[type] || icons.info
}
</script>

<style scoped lang="scss">
.notification-enter-active,
.notification-leave-active {
  transition: all 0.3s ease;
}

.notification-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.notification-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

.notification-move {
  transition: transform 0.3s ease;
}
</style>
