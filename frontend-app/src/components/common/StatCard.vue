<template>
  <div class="stat-card card p-6">
    <div class="flex items-center justify-between">
      <div class="flex-1">
        <div class="flex items-center space-x-3 mb-2">          <div class="w-10 h-10 bg-gradient-primary rounded-lg flex-center">
            <AppIcon :name="iconName" class="text-white" />
          </div>
          <h3 class="text-sm font-medium text-text-muted">{{ title }}</h3>
        </div>
        
        <div class="mb-2">
          <p class="text-2xl font-bold text-text-primary">{{ value }}</p>
        </div>
        
        <div class="flex items-center space-x-2">          <span
            :class="[
              'text-sm font-medium',
              trend === 'up' ? 'price-up' : trend === 'down' ? 'price-down' : 'price-flat'
            ]"
          >
            <AppIcon
              :name="trend === 'up' ? 'trending-up' : 
                     trend === 'down' ? 'trending-down' : 
                     'trending-flat'"
              class="mr-1"
              :size="16"
            />
            {{ change }}
          </span>
          <span class="text-xs text-text-muted">vs 昨日</span>
        </div>
      </div>      <!-- 右侧装饰性图标 -->
      <div class="decorative-icon">
        <AppIcon :name="iconName" :size="32" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import AppIcon from '@/components/common/Icon.vue'

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  value: {
    type: String,
    required: true
  },
  change: {
    type: String,
    required: true
  },
  trend: {
    type: String,
    default: 'flat',
    validator: (value) => ['up', 'down', 'flat'].includes(value)
  },  icon: {
    type: String,
    required: true
  }
})

// 从 i-carbon-xxx 格式的图标类名中提取实际的图标名
const iconName = computed(() => {
  const match = props.icon.match(/i-carbon-(.+)/)
  return match ? match[1] : 'dashboard'
})
</script>

<style scoped lang="scss">
.stat-card {
  position: relative;
  overflow: hidden;
  
  &:hover {
    .decorative-icon {
      color: var(--text-accent);
      opacity: 0.3;
      transform: scale(1.1);
    }
  }
}

.decorative-icon {
  color: var(--text-muted);
  opacity: 0.2;
  transition: all 0.3s ease;
}
</style>
