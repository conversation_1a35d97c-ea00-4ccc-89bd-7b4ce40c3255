// 全局 SCSS 入口文件
@import 'variables';
@import 'mixins';

// CSS 变量定义
:root {
  // 深色主题变量 - 优化版
  --bg-primary: #{$bg-primary};
  --bg-secondary: #{$bg-secondary};
  --bg-tertiary: #{$bg-tertiary};
  --bg-quaternary: #{$bg-quaternary};
  --text-primary: #{$text-primary};
  --text-secondary: #{$text-secondary};
  --text-muted: #{$text-muted};
  --text-accent: #{$text-accent};
  --border-color: #{$border-color};
  --border-light: #{$border-light};
  --accent-primary: #{$accent-primary};
  --accent-secondary: #{$accent-secondary};
  --accent-tertiary: #{$accent-tertiary};

  // 金融色彩 - 增强版
  --price-up: #{$price-up};
  --price-up-bg: #{$price-up-bg};
  --price-down: #{$price-down};
  --price-down-bg: #{$price-down-bg};
  --price-flat: #{$price-flat};
  --volume-color: #{$volume-color};
  --volume-bg: #{$volume-bg};

  // 现代渐变色
  --gradient-primary: #{$gradient-primary};
  --gradient-secondary: #{$gradient-secondary};
  --gradient-success: #{$gradient-success};
  --gradient-warning: #{$gradient-warning};
  --gradient-danger: #{$gradient-danger};
  --gradient-info: #{$gradient-info};
  --gradient-dark: #{$gradient-dark};

  // 玻璃态效果
  --glass-bg: #{$glass-bg};
  --glass-border: #{$glass-border};
  --glass-shadow: #{$glass-shadow};

  // 阴影系统
  --shadow-sm: #{$shadow-sm};
  --shadow-md: #{$shadow-md};
  --shadow-lg: #{$shadow-lg};
  --shadow-xl: #{$shadow-xl};
  --shadow-glow: #{$shadow-glow};
}

// 重置样式和基础设置
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  background: var(--bg-primary);
  color: var(--text-primary);
  font-family: $font-family;
  transition: $transition-base;
  overflow-x: hidden;
  line-height: 1.6;
  @include scrollbar;
}

// 浅色主题类
.light-theme {
  --bg-primary: #{$light-bg-primary};
  --bg-secondary: #{$light-bg-secondary};
  --bg-tertiary: #{$light-bg-tertiary};
  --bg-quaternary: #{$light-bg-quaternary};
  --text-primary: #{$light-text-primary};
  --text-secondary: #{$light-text-secondary};
  --text-muted: #{$light-text-muted};
  --border-color: #{$light-border-color};
  --border-light: #{$light-border-light};
  --glass-bg: #{$light-glass-bg};
  --glass-border: #{$light-glass-border};
  --shadow-glow: #{$light-shadow-glow};
}

// 动画定义
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 5px $accent-primary; }
  50% { box-shadow: 0 0 20px $accent-primary; }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

// 动画工具类
.fade-in { animation: fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1); }
.slide-in-left { animation: slideInLeft 0.6s cubic-bezier(0.4, 0, 0.2, 1); }
.slide-in-right { animation: slideInRight 0.6s cubic-bezier(0.4, 0, 0.2, 1); }
.scale-in { animation: scaleIn 0.4s cubic-bezier(0.4, 0, 0.2, 1); }
.pulse { animation: pulse 2s infinite; }
.glow-animation { animation: glow 2s infinite; }

// 工具类
.mono-font {
  @include mono-font;
}

.price-up {
  @include price-color(up);
}

.price-down {
  @include price-color(down);
}

.price-flat {
  @include price-color(flat);
}

.price-up-bg {
  @include price-bg(up);
}

.price-down-bg {
  @include price-bg(down);
}

.flex-center {
  @include flex-center;
}

.absolute-center {
  @include absolute-center;
}

// 响应式隐藏类
.mobile-hidden {
  display: block;
  
  @media (max-width: $breakpoint-md) {
    display: none;
  }
}

.mobile-only {
  display: none;
  
  @media (max-width: $breakpoint-md) {
    display: block;
  }
}

// 组件基础样式
.card {
  @include card-base;
}

.glass-card {
  @include glass-card;
}

.btn-primary {
  @include btn-primary;
}

// 卡片样式 - 现代化设计
.card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  box-shadow: var(--shadow-md);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--accent-primary), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  
  &:hover {
    box-shadow: var(--shadow-xl);
    transform: translateY(-4px);
    border-color: var(--border-light);
    
    &::before {
      opacity: 1;
    }
  }
}

// 玻璃态卡片
.glass-card {
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
}

// 渐变背景类
.gradient-bg { background: var(--gradient-primary); }
.gradient-success { background: var(--gradient-success); }
.gradient-warning { background: var(--gradient-warning); }
.gradient-danger { background: var(--gradient-danger); }
.gradient-info { background: var(--gradient-info); }
.gradient-dark { background: var(--gradient-dark); }

// 价格颜色类
.price-up {
  color: var(--price-up);
  font-weight: 600;
}

.price-down {
  color: var(--price-down);
  font-weight: 600;
}

.price-flat {
  color: var(--price-flat);
  font-weight: 500;
}

// 价格背景
.price-up-bg {
  background: var(--price-up-bg);
  color: var(--price-up);
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 600;
}

.price-down-bg {
  background: var(--price-down-bg);
  color: var(--price-down);
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 600;
}

// 数字字体
.mono-font {
  font-family: $font-mono;
  font-variant-numeric: tabular-nums;
  letter-spacing: 0.025em;
}

// 发光效果
.glow {
  box-shadow: var(--shadow-glow);
}

.glow-text {
  text-shadow: 0 0 10px currentColor;
}

// 导航样式
.nav-item {
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 12px;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
  }
  
  &:hover::before {
    left: 100%;
  }
  
  &.active {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    
    &::after {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 3px;
      height: 20px;
      background: var(--accent-primary);
      border-radius: 0 2px 2px 0;
      box-shadow: 0 0 10px var(--accent-primary);
    }
  }
}

// 按钮样式
.btn-primary {
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  color: white;
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  font-weight: 600;
  font-size: 0.875rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
  }
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
    
    &::before {
      left: 100%;
    }
  }
  
  &:active {
    transform: translateY(0);
  }
}

.btn-secondary {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 12px 24px;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
  
  &:hover {
    background: var(--bg-quaternary);
    border-color: var(--accent-primary);
    transform: translateY(-1px);
  }
}

// 输入框样式
.input-field {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 12px 16px;
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  
  &:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    background: var(--bg-secondary);
    transform: translateY(-1px);
  }
}

// 工具类
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.text-gradient {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

// 加载动画
.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.btn-secondary {
  @include btn-secondary;
}

.input-field {
  @include input-field;
}

// 图表容器样式
.chart-container {
  background: $bg-secondary;
  border-radius: 12px;
  padding: 16px;
  height: 400px;
  position: relative;
  overflow: hidden;
  @include card-base;
}

// 状态指示器
.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 8px;
  
  &.online {
    background: $price-up;
    box-shadow: 0 0 8px rgba(16, 185, 129, 0.6);
  }
  
  &.offline {
    background: $price-down;
  }
}
