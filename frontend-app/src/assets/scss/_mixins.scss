// SCSS Mixins - 通用工具函数

// 响应式断点 mixin
@mixin responsive($breakpoint) {
  @if $breakpoint == sm {
    @media (min-width: $breakpoint-sm) { @content; }
  }
  @if $breakpoint == md {
    @media (min-width: $breakpoint-md) { @content; }
  }
  @if $breakpoint == lg {
    @media (min-width: $breakpoint-lg) { @content; }
  }
  @if $breakpoint == xl {
    @media (min-width: $breakpoint-xl) { @content; }
  }
  @if $breakpoint == 2xl {
    @media (min-width: $breakpoint-2xl) { @content; }
  }
}

// 卡片样式 mixin
@mixin card-base {
  background: $bg-secondary;
  border: 1px solid $border-color;
  border-radius: 16px;
  box-shadow: $shadow-md;
  transition: $transition-base;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, $accent-primary, transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  
  &:hover {
    box-shadow: $shadow-xl;
    transform: translateY(-4px);
    border-color: $border-light;
    
    &::before {
      opacity: 1;
    }
  }
}

// 玻璃态卡片 mixin
@mixin glass-card {
  background: $glass-bg;
  backdrop-filter: blur(10px);
  border: 1px solid $glass-border;
  box-shadow: $glass-shadow;
}

// 按钮基础样式 mixin
@mixin btn-base {
  border-radius: 12px;
  padding: 12px 24px;
  font-weight: 600;
  font-size: 0.875rem;
  transition: $transition-base;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  border: none;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
  }
  
  &:hover::before {
    left: 100%;
  }
  
  &:hover {
    transform: translateY(-2px);
  }
  
  &:active {
    transform: translateY(0);
  }
}

// 主要按钮样式 mixin
@mixin btn-primary {
  @include btn-base;
  background: linear-gradient(135deg, $accent-primary, $accent-secondary);
  color: white;
  
  &:hover {
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
  }
}

// 次要按钮样式 mixin
@mixin btn-secondary {
  @include btn-base;
  background: $bg-tertiary;
  color: $text-primary;
  border: 1px solid $border-color;
  
  &:hover {
    background: $bg-quaternary;
    border-color: $accent-primary;
    transform: translateY(-1px);
  }
}

// 输入框样式 mixin
@mixin input-field {
  background: $bg-tertiary;
  border: 1px solid $border-color;
  border-radius: 12px;
  padding: 12px 16px;
  color: $text-primary;
  font-size: 0.875rem;
  transition: $transition-base;
  
  &:focus {
    outline: none;
    border-color: $accent-primary;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    background: $bg-secondary;
    transform: translateY(-1px);
  }
  
  &::placeholder {
    color: $text-muted;
    transition: color 0.3s ease;
  }
  
  &:focus::placeholder {
    color: $text-secondary;
  }
}

// 价格颜色 mixin
@mixin price-color($type) {
  @if $type == up {
    color: $price-up;
    font-weight: 600;
  }
  @if $type == down {
    color: $price-down;
    font-weight: 600;
  }
  @if $type == flat {
    color: $price-flat;
    font-weight: 500;
  }
}

// 价格背景 mixin
@mixin price-bg($type) {
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 600;
  
  @if $type == up {
    background: $price-up-bg;
    color: $price-up;
  }
  @if $type == down {
    background: $price-down-bg;
    color: $price-down;
  }
}

// 滚动条样式 mixin
@mixin scrollbar {
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: $bg-secondary;
  }
  
  &::-webkit-scrollbar-thumb {
    background: $border-color;
    border-radius: 3px;
    
    &:hover {
      background: $text-muted;
    }
  }
}

// 加载动画 mixin
@mixin loading-shimmer {
  position: relative;
  overflow: hidden;
  background: $bg-tertiary;
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: shimmer 1.5s infinite;
  }
}

// 数字字体 mixin
@mixin mono-font {
  font-family: $font-mono;
  font-variant-numeric: tabular-nums;
  letter-spacing: 0.025em;
}

// 发光效果 mixin
@mixin glow($color: $accent-primary) {
  box-shadow: 0 0 20px rgba($color, 0.3);
}

// Flexbox 居中 mixin
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

// 绝对居中 mixin
@mixin absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
