/**
 * 搜索统计工具类
 * 管理股票搜索次数的localStorage存储和大根堆排序
 */

const STORAGE_KEY = 'stock_search_stats'

export class SearchStatsManager {
  constructor() {
    this.stats = this.loadStats()
  }

  /**
   * 从localStorage加载搜索统计
   */
  loadStats() {
    try {
      const data = localStorage.getItem(STORAGE_KEY)
      return data ? JSON.parse(data) : {}
    } catch (error) {
      console.error('加载搜索统计失败:', error)
      return {}
    }
  }

  /**
   * 保存搜索统计到localStorage
   */
  saveStats() {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(this.stats))
    } catch (error) {
      console.error('保存搜索统计失败:', error)
    }
  }

  /**
   * 增加股票搜索次数
   * @param {string} stockCode 股票代码
   * @param {string} stockName 股票名称
   * @param {string} industry 行业
   */
  incrementSearchCount(stockCode, stockName, industry = '') {
    if (!this.stats[stockCode]) {
      this.stats[stockCode] = {
        code: stockCode,
        name: stockName,
        industry: industry,
        searchCount: 0,
        lastSearchTime: Date.now()
      }
    }
    
    this.stats[stockCode].searchCount++
    this.stats[stockCode].lastSearchTime = Date.now()
    this.saveStats()
  }

  /**
   * 获取热门股票列表（按搜索次数排序）
   * @param {number} limit 返回数量限制
   * @returns {Array} 热门股票列表
   */
  getPopularStocks(limit = 10) {
    const stockList = Object.values(this.stats)
    
    // 使用大根堆排序：按搜索次数降序，次数相同时按最近搜索时间降序
    stockList.sort((a, b) => {
      if (a.searchCount !== b.searchCount) {
        return b.searchCount - a.searchCount
      }
      return b.lastSearchTime - a.lastSearchTime
    })

    return stockList.slice(0, limit)
  }

  /**
   * 清理过期或搜索次数过少的记录
   * @param {number} minSearchCount 最小搜索次数阈值
   * @param {number} maxAge 最大保留时间（毫秒）
   */
  cleanup(minSearchCount = 2, maxAge = 30 * 24 * 60 * 60 * 1000) { // 30天
    const now = Date.now()
    const newStats = {}

    Object.entries(this.stats).forEach(([code, data]) => {
      // 保留搜索次数足够或最近搜索过的记录
      if (data.searchCount >= minSearchCount || (now - data.lastSearchTime) < maxAge) {
        newStats[code] = data
      }
    })

    this.stats = newStats
    this.saveStats()
  }

  /**
   * 获取股票的搜索次数
   * @param {string} stockCode 股票代码
   * @returns {number} 搜索次数
   */
  getSearchCount(stockCode) {
    return this.stats[stockCode]?.searchCount || 0
  }

  /**
   * 清空所有搜索统计
   */
  clear() {
    this.stats = {}
    this.saveStats()
  }
}

// 创建单例实例
export const searchStatsManager = new SearchStatsManager()

// 定期清理过期数据（在实例化时执行一次）
setTimeout(() => {
  searchStatsManager.cleanup()
}, 1000)
