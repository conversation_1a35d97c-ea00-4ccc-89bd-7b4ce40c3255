<template>
  <div class="technical-indicators-page">    <!-- 股票信息头部 -->
    <div v-if="selectedStock" class="card p-6 mb-6 bg-gradient-to-r from-blue-600/10 to-purple-600/10 border border-blue-500/20">
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-6">
          <div class="flex items-center gap-4">
            <div class="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
              <Icon name="chart-line" class="text-white text-xl" />
            </div>
            <div>
              <h2 class="text-2xl font-bold text-white">{{ selectedStock.name }}</h2>
              <p class="text-gray-400 font-mono text-sm">{{ selectedStock.code }}</p>
            </div>
          </div>
          <div class="flex items-center gap-8">
            <div class="text-center">
              <div class="text-3xl font-bold mono-font" :class="getPriceColorClass(selectedStock.changePercent)">
                ¥{{ selectedStock.price }}
              </div>
              <div class="text-sm font-medium" :class="getPriceColorClass(selectedStock.changePercent)">
                {{ selectedStock.changePercent >= 0 ? '+' : '' }}{{ selectedStock.changePercent }}%
              </div>
            </div>
            <div class="h-12 w-px bg-gray-600"></div>
            <div class="flex flex-col gap-1">
              <div class="flex items-center gap-2">
                <div class="w-2 h-2 rounded-full bg-green-400"></div>
                <span class="text-sm text-gray-300">实时数据</span>
              </div>
              <div class="text-xs text-gray-400">{{ new Date().toLocaleTimeString() }}</div>
            </div>
          </div>
        </div>        <div class="flex items-center gap-3">
          <IconButton 
            icon="arrow-left" 
            variant="secondary" 
            size="sm"
            @click="goBack"
          >
            返回
          </IconButton>
          <IconButton 
            icon="renew" 
            variant="primary" 
            size="sm"
            @click="refreshData"
          >
            刷新
          </IconButton>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
      <!-- 指标选择 -->
      <div class="card p-6">
        <h3 class="text-lg font-semibold mb-4">指标选择</h3>
        <div class="space-y-3">
          <label class="flex items-center space-x-3">
            <input 
              type="checkbox" 
              v-model="selectedIndicators.macd"
              @change="toggleIndicator('macd')"
              class="form-checkbox"
            >
            <span>MACD</span>
          </label>
          <label class="flex items-center space-x-3">
            <input 
              type="checkbox" 
              v-model="selectedIndicators.kdj"
              @change="toggleIndicator('kdj')"
              class="form-checkbox"
            >
            <span>KDJ</span>
          </label>
          <label class="flex items-center space-x-3">
            <input 
              type="checkbox" 
              v-model="selectedIndicators.rsi"
              @change="toggleIndicator('rsi')"
              class="form-checkbox"
            >
            <span>RSI</span>
          </label>
          <label class="flex items-center space-x-3">
            <input 
              type="checkbox" 
              v-model="selectedIndicators.bollinger"
              @change="toggleIndicator('bollinger')"
              class="form-checkbox"
            >
            <span>Bollinger Bands</span>
          </label>
          <label class="flex items-center space-x-3">
            <input 
              type="checkbox" 
              v-model="selectedIndicators.arbr"
              @change="toggleIndicator('arbr')"
              class="form-checkbox"
            >
            <span>ARBR</span>
          </label>
        </div>
      </div>

      <!-- 参数设置 -->
      <div class="card p-6">
        <h3 class="text-lg font-semibold mb-4">参数设置</h3>
        <div class="space-y-4">
          <div v-if="selectedIndicators.macd">
            <label class="block text-sm text-gray-400 mb-2">MACD参数</label>
            <div class="grid grid-cols-3 gap-2">
              <input 
                type="number" 
                v-model="indicatorParams.macd.fast"
                class="input-field text-sm" 
                placeholder="快线"
              >
              <input 
                type="number" 
                v-model="indicatorParams.macd.slow"
                class="input-field text-sm" 
                placeholder="慢线"
              >
              <input 
                type="number" 
                v-model="indicatorParams.macd.signal"
                class="input-field text-sm" 
                placeholder="信号线"
              >
            </div>
          </div>
          
          <div v-if="selectedIndicators.rsi">
            <label class="block text-sm text-gray-400 mb-2">RSI参数</label>
            <input 
              type="number" 
              v-model="indicatorParams.rsi.period"
              class="input-field text-sm w-full" 
              placeholder="周期"
            >
          </div>
          
          <div v-if="selectedIndicators.kdj">
            <label class="block text-sm text-gray-400 mb-2">KDJ参数</label>
            <div class="grid grid-cols-3 gap-2">
              <input 
                type="number" 
                v-model="indicatorParams.kdj.k"
                class="input-field text-sm" 
                placeholder="K值"
              >
              <input 
                type="number" 
                v-model="indicatorParams.kdj.d"
                class="input-field text-sm" 
                placeholder="D值"
              >
              <input 
                type="number" 
                v-model="indicatorParams.kdj.j"
                class="input-field text-sm" 
                placeholder="J值"
              >
            </div>
          </div>
        </div>
      </div>

      <!-- 信号提示 -->
      <div class="card p-6">
        <h3 class="text-lg font-semibold mb-4">交易信号</h3>
        <div class="space-y-3">
          <div v-for="signal in tradingSignals" :key="signal.id" class="flex items-center space-x-3">
            <div 
              class="w-3 h-3 rounded-full"
              :class="getSignalColorClass(signal.type)"
            ></div>
            <span class="text-sm">{{ signal.message }}</span>
          </div>
            <div v-if="tradingSignals.length === 0" class="text-center py-4">
            <Icon name="chart-line" class="text-2xl text-gray-500 mb-2" />
            <p class="text-gray-400 text-sm">暂无交易信号</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 指标图表网格 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">      <div v-if="selectedIndicators.macd" class="card p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold">MACD指标</h3>
          <div class="flex items-center gap-2">
            <div class="flex items-center gap-1">
              <div class="w-3 h-3 bg-blue-500 rounded"></div>
              <span class="text-xs text-gray-400">MACD</span>
            </div>
            <div class="flex items-center gap-1">
              <div class="w-3 h-3 bg-red-500 rounded"></div>
              <span class="text-xs text-gray-400">信号线</span>
            </div>
          </div>
        </div>
        <div class="chart-container">
          <div class="flex items-center justify-center h-full">
            <div class="text-center">
              <Icon name="chart-line" class="text-4xl text-gray-500 mb-2" />
              <p class="text-gray-400 text-sm">MACD指标图表</p>
              <p class="text-xs text-gray-500">快线: {{ indicatorParams.macd.fast }} | 慢线: {{ indicatorParams.macd.slow }} | 信号线: {{ indicatorParams.macd.signal }}</p>
            </div>
          </div>
        </div>
      </div>

      <div v-if="selectedIndicators.kdj" class="card p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold">KDJ指标</h3>
          <div class="flex items-center gap-2">
            <div class="flex items-center gap-1">
              <div class="w-3 h-3 bg-yellow-500 rounded"></div>
              <span class="text-xs text-gray-400">K线</span>
            </div>
            <div class="flex items-center gap-1">
              <div class="w-3 h-3 bg-green-500 rounded"></div>
              <span class="text-xs text-gray-400">D线</span>
            </div>
            <div class="flex items-center gap-1">
              <div class="w-3 h-3 bg-purple-500 rounded"></div>
              <span class="text-xs text-gray-400">J线</span>
            </div>
          </div>
        </div>
        <div class="chart-container">
          <div class="flex items-center justify-center h-full">
            <div class="text-center">
              <Icon name="chart-line" class="text-4xl text-gray-500 mb-2" />
              <p class="text-gray-400 text-sm">KDJ指标图表</p>
              <p class="text-xs text-gray-500">K: {{ indicatorParams.kdj.k }} | D: {{ indicatorParams.kdj.d }} | J: {{ indicatorParams.kdj.j }}</p>
            </div>
          </div>
        </div>
      </div>

      <div v-if="selectedIndicators.rsi" class="card p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold">RSI指标</h3>
          <div class="flex items-center gap-2">
            <div class="flex items-center gap-1">
              <div class="w-3 h-3 bg-orange-500 rounded"></div>
              <span class="text-xs text-gray-400">RSI</span>
            </div>
            <span class="text-xs text-gray-500">超买线: 70 | 超卖线: 30</span>
          </div>
        </div>
        <div class="chart-container">
          <div class="flex items-center justify-center h-full">
            <div class="text-center">
              <Icon name="chart-line" class="text-4xl text-gray-500 mb-2" />
              <p class="text-gray-400 text-sm">RSI指标图表</p>
              <p class="text-xs text-gray-500">周期: {{ indicatorParams.rsi.period }}</p>
            </div>
          </div>
        </div>
      </div>

      <div v-if="selectedIndicators.bollinger" class="card p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold">Bollinger Bands</h3>
          <div class="flex items-center gap-2">
            <div class="flex items-center gap-1">
              <div class="w-3 h-3 bg-blue-500 rounded"></div>
              <span class="text-xs text-gray-400">上轨</span>
            </div>
            <div class="flex items-center gap-1">
              <div class="w-3 h-3 bg-gray-500 rounded"></div>
              <span class="text-xs text-gray-400">中轨</span>
            </div>
            <div class="flex items-center gap-1">
              <div class="w-3 h-3 bg-red-500 rounded"></div>
              <span class="text-xs text-gray-400">下轨</span>
            </div>
          </div>
        </div>
        <div class="chart-container">
          <div class="flex items-center justify-center h-full">
            <div class="text-center">
              <Icon name="chart-line" class="text-4xl text-gray-500 mb-2" />
              <p class="text-gray-400 text-sm">布林带指标图表</p>
              <p class="text-xs text-gray-500">周期: 20 | 标准差: 2</p>
            </div>
          </div>
        </div>
      </div>

      <div v-if="selectedIndicators.arbr" class="card p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold">ARBR指标</h3>
          <div class="flex items-center gap-2">
            <div class="flex items-center gap-1">
              <div class="w-3 h-3 bg-cyan-500 rounded"></div>
              <span class="text-xs text-gray-400">AR线</span>
            </div>
            <div class="flex items-center gap-1">
              <div class="w-3 h-3 bg-pink-500 rounded"></div>
              <span class="text-xs text-gray-400">BR线</span>
            </div>
          </div>
        </div>
        <div class="chart-container">
          <div class="flex items-center justify-center h-full">
            <div class="text-center">
              <Icon name="chart-line" class="text-4xl text-gray-500 mb-2" />
              <p class="text-gray-400 text-sm">ARBR指标图表</p>
              <p class="text-xs text-gray-500">周期: 26</p>
            </div>
          </div>
        </div>
      </div>
    </div>    <!-- 无选中指标提示 -->
    <div v-if="!hasSelectedIndicators" class="card p-12 text-center">
      <Icon name="chart-line" class="text-6xl text-gray-500 mb-4" />
      <h3 class="text-xl font-semibold mb-2">请选择技术指标</h3>
      <p class="text-gray-400">在左侧选择您要查看的技术指标</p>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import Icon from '@/components/common/Icon.vue'
import IconButton from '@/components/common/IconButton.vue'

const route = useRoute()
const router = useRouter()

// 选中的股票
const selectedStock = ref(null)

// 选中的指标
const selectedIndicators = ref({
  macd: true, // 默认选中MACD
  kdj: false,
  rsi: false,
  bollinger: false,
  arbr: false
})

// 指标参数
const indicatorParams = ref({
  macd: {
    fast: 12,
    slow: 26,
    signal: 9
  },
  rsi: {
    period: 14
  },
  kdj: {
    k: 9,
    d: 3,
    j: 3
  }
})

// 交易信号
const tradingSignals = ref([
  {
    id: 1,
    type: 'buy',
    message: 'MACD金叉买入信号'
  },
  {
    id: 2,
    type: 'warning',
    message: 'RSI超买预警'
  },
  {
    id: 3,
    type: 'sell',
    message: 'KDJ死叉卖出信号'
  }
])

// 计算是否有选中的指标
const hasSelectedIndicators = computed(() => {
  return Object.values(selectedIndicators.value).some(selected => selected)
})

// 页面初始化
onMounted(() => {
  // 获取URL参数中的股票代码
  const stockCode = route.query.stock
  if (stockCode) {
    loadStockData(stockCode)
  } else {
    // 如果没有股票代码，使用默认股票
    loadStockData('600519')
  }
})

// 监听路由变化
watch(() => route.query.stock, (newStock) => {
  if (newStock) {
    loadStockData(newStock)
  }
})

// 加载股票数据
const loadStockData = (stockCode) => {
  // 模拟股票数据
  const mockStocks = {
    '600519': {
      code: '600519',
      name: '贵州茅台',
      price: '1678.90',
      changePercent: 2.15
    },
    '000858': {
      code: '000858',
      name: '五粮液',
      price: '156.78',
      changePercent: 3.42
    },
    '002415': {
      code: '002415',
      name: '海康威视',
      price: '34.56',
      changePercent: -0.82
    },
    '600036': {
      code: '600036',
      name: '招商银行',
      price: '45.67',
      changePercent: 1.85
    },
    '000001': {
      code: '000001',
      name: '平安银行',
      price: '12.34',
      changePercent: -1.20
    }
  }

  selectedStock.value = mockStocks[stockCode] || {
    code: stockCode,
    name: '未知股票',
    price: '0.00',
    changePercent: 0
  }

  // 加载指标数据
  loadIndicatorData()
}

// 加载指标数据
const loadIndicatorData = () => {
  // 这里可以调用API加载真实的指标数据
  console.log('加载指标数据:', selectedStock.value?.code)
}

// 切换指标
const toggleIndicator = (indicator) => {
  console.log('切换指标:', indicator, selectedIndicators.value[indicator])
  // 这里可以重新计算和渲染指标图表
  loadIndicatorData()
}

// 返回上一页
const goBack = () => {
  router.go(-1)
}

// 刷新数据
const refreshData = () => {
  if (selectedStock.value) {
    loadStockData(selectedStock.value.code)
  }
}

// 获取价格颜色类
const getPriceColorClass = (changePercent) => {
  if (changePercent > 0) return 'text-green-400'
  if (changePercent < 0) return 'text-red-400'
  return 'text-gray-400'
}

// 获取信号颜色类
const getSignalColorClass = (type) => {
  switch (type) {
    case 'buy': return 'bg-green-500'
    case 'sell': return 'bg-red-500'
    case 'warning': return 'bg-yellow-500'
    default: return 'bg-gray-500'
  }
}
</script>

<style scoped lang="scss">
.technical-indicators-page {
  max-width: 1400px;
  margin: 0 auto;
}

.chart-container {
  height: 320px;
  position: relative;
  background: linear-gradient(135deg, var(--bg-tertiary) 0%, var(--bg-quaternary) 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.chart-container:hover {
  border-color: var(--accent-primary);
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.15);
}

// 股票信息头部样式
.bg-gradient-to-r {
  position: relative;
  overflow: hidden;
}

.bg-gradient-to-r::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
  z-index: -1;
}

// 卡片悬停效果增强
.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

// 指标图例样式
.flex.items-center.gap-1 {
  padding: 4px 8px;
  background: var(--bg-quaternary);
  border-radius: 6px;
  border: 1px solid var(--border-color);
}

// 实时状态指示器
.w-2.h-2.rounded-full.bg-green-400 {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .lg\\:grid-cols-3 {
    grid-template-columns: 1fr;
  }
  
  .lg\\:grid-cols-2 {
    grid-template-columns: 1fr;
  }
  
  .chart-container {
    height: 280px;
  }
}

// 复选框样式
.form-checkbox {
  width: 16px;
  height: 16px;
  border: 2px solid var(--border-color);
  border-radius: 4px;
  background: transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:checked {
    background: var(--accent-primary);
    border-color: var(--accent-primary);
  }
  
  &:hover {
    border-color: var(--accent-primary);
  }
}

// 工具提示样式
.tooltip {
  position: relative;
}

.tooltip::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: var(--bg-tertiary);
  color: var(--text-primary);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
  z-index: 1000;
}

.tooltip:hover::after {
  opacity: 1;
}
</style>
