<template>
  <div class="watchlist-page">
    <div class="grid grid-cols-1 gap-6">
      <!-- 自选股列表 -->
      <div>
        <div class="card p-6">
          <div class="flex items-center justify-between mb-4">
            <div class="flex items-center gap-3">
              <h3 class="text-lg font-semibold">我的自选股</h3>
              <span class="text-sm text-gray-400">({{ watchlistStocks.length }}只)</span>
            </div>            <div class="flex items-center gap-2">
              <IconButton 
                icon="add" 
                variant="primary" 
                size="sm" 
                @click="showAddStockModal"
              >
                添加股票
              </IconButton>
            </div>
          </div>          <div class="data-table">
            <!-- 加载状态 -->
            <div v-if="loading" class="text-center py-12">
              <div class="loading-spinner mx-auto mb-4"></div>
              <p class="text-gray-400">正在加载自选股...</p>
            </div>

            <!-- 错误状态 -->
            <div v-else-if="error" class="text-center py-12">
              <Icon name="exclamation-triangle" class="text-4xl text-red-500 mb-4" />
              <p class="text-red-400 mb-4">{{ error }}</p>
              <button 
                class="btn btn--primary btn--sm"
                @click="retryLoadData"
              >
                重试
              </button>
            </div>

            <!-- 数据表格 -->
            <table v-else class="w-full"><thead>
                <tr>
                  <th>代码</th>
                  <th>名称</th>
                  <th>现价</th>
                  <th>涨跌幅</th>
                  <th>成交量</th>
                  <th class="text-center">操作</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="stock in watchlistStocks" :key="stock.code" class="watchlist-row">
                  <td>
                    <span class="font-mono text-sm">{{ stock.code }}</span>
                  </td>
                  <td>
                    <span class="font-medium">{{ stock.name }}</span>
                  </td>
                  <td>
                    <span class="font-mono font-bold" :class="getPriceColorClass(stock.changePercent)">
                      ¥{{ stock.price }}
                    </span>
                  </td>
                  <td>
                    <span 
                      class="px-2 py-1 rounded text-xs font-medium"
                      :class="getChangeBackgroundClass(stock.changePercent)"
                    >
                      {{ stock.changePercent >= 0 ? '+' : '' }}{{ stock.changePercent }}%
                    </span>
                  </td>
                  <td>
                    <span class="font-mono text-sm">{{ formatVolume(stock.volume) }}</span>
                  </td>                  <td>
                    <div class="flex items-center gap-1 justify-center">
                      <IconButton 
                        icon="chart-line" 
                        variant="ghost" 
                        size="xs" 
                        title="分析股票"
                        @click="viewStockDetail(stock)"
                      />
                      <IconButton 
                        icon="trash-can" 
                        variant="ghost" 
                        size="xs" 
                        title="移除自选"
                        @click="removeFromWatchlist(stock)"
                      />
                    </div>
                  </td>
                </tr>
              </tbody>            </table>              
            
            <!-- 空状态 -->
            <div v-if="!loading && !error && watchlistStocks.length === 0" class="text-center py-12">
              <Icon name="star" class="text-4xl text-gray-500 mb-4" />
              <p class="text-gray-400 mb-4">您还没有添加任何自选股</p>
              <IconButton 
                icon="add" 
                variant="primary" 
                size="md"
                @click="showAddStockModal"
              >
                添加第一只股票
              </IconButton>
            </div>
          </div>
        </div>
      </div>      <!-- 侧边栏：快速分析和预警 (已隐藏) -->
      <!-- 
      <div class="space-y-6">
        快速分析
        <div class="card p-6">
          <h3 class="text-lg font-semibold mb-4">快速分析</h3>
          <div class="space-y-3">
            <div class="flex justify-between">
              <span class="text-gray-400">强势股票:</span>
              <span class="text-green-400 font-medium">{{ analysisData.strongStocks }}只</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-400">弱势股票:</span>
              <span class="text-red-400 font-medium">{{ analysisData.weakStocks }}只</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-400">震荡股票:</span>
              <span class="text-yellow-400 font-medium">{{ analysisData.sidewaysStocks }}只</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-400">总市值:</span>
              <span class="font-mono">{{ formatMarketCap(analysisData.totalMarketCap) }}</span>
            </div>
          </div>
        </div>

        预警提醒
        <div class="card p-6">
          <h3 class="text-lg font-semibold mb-4">预警提醒</h3>
          <div class="space-y-3">
            <div v-for="alert in alertList" :key="alert.id" 
                 class="flex items-center space-x-3 p-3 rounded-lg"
                 :class="getAlertBackgroundClass(alert.type)">
              <i :class="getAlertIconClass(alert.type)"></i>
              <div class="text-sm flex-1">
                <div class="font-medium">{{ alert.stockCode }} {{ alert.stockName }}</div>
                <div class="text-gray-400">{{ alert.message }}</div>
              </div>
            </div>
            
            <div v-if="alertList.length === 0" class="text-center py-6">
              <i class="i-carbon-notification text-3xl text-gray-500 mb-2"></i>
              <p class="text-gray-400 text-sm">暂无预警信息</p>
            </div>
          </div>
        </div>

        市场概况
        <div class="card p-6">
          <h3 class="text-lg font-semibold mb-4">市场概况</h3>
          <div class="space-y-3">
            <div class="flex justify-between">
              <span class="text-gray-400">上证指数:</span>
              <span class="font-mono" :class="marketData.shIndex.change >= 0 ? 'text-green-400' : 'text-red-400'">
                {{ marketData.shIndex.value }}
              </span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-400">深证成指:</span>
              <span class="font-mono" :class="marketData.szIndex.change >= 0 ? 'text-green-400' : 'text-red-400'">
                {{ marketData.szIndex.value }}
              </span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-400">创业板指:</span>
              <span class="font-mono" :class="marketData.cybIndex.change >= 0 ? 'text-green-400' : 'text-red-400'">
                {{ marketData.cybIndex.value }}
              </span>
            </div>
          </div>
        </div>
      </div>
      -->
    </div>

    <!-- 添加股票模态框 -->
    <div v-if="showAddModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div class="bg-gray-800 rounded-lg p-6 w-full max-w-md">        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold">添加自选股</h3>
          <IconButton 
            icon="close" 
            variant="ghost" 
            size="xs" 
            title="关闭"
            @click="hideAddStockModal"
          />
        </div>
        
        <div class="space-y-4">
          <div>
            <label class="block text-sm text-gray-400 mb-2">搜索股票</label>
            <input 
              v-model="searchKeyword"
              type="text" 
              class="input-field w-full" 
              placeholder="输入股票代码或名称"
              @input="searchStocks"
            >
          </div>          
          <!-- 搜索结果 -->
          <div v-if="searchKeyword.trim() && searchResults.length > 0" class="max-h-60 overflow-y-auto overflow-x-hidden">
            <div class="text-sm text-gray-400 mb-2">搜索结果 ({{ searchResults.length }}只):</div>
            <div class="space-y-2 px-2">              <div 
                v-for="stock in searchResults" 
                :key="stock.code"
                class="flex items-center justify-between p-3 bg-gray-700 rounded cursor-pointer hover:bg-gray-600"
                @click="addStockToWatchlist(stock)"
              >
                <div class="flex-1 min-w-0">
                  <div class="font-medium truncate">{{ stock.code }} {{ stock.name }}</div>
                  <div class="text-sm text-gray-400 flex items-center gap-2">
                    <span class="truncate">{{ stock.industry || '未分类' }}</span>
                    <span v-if="stock.exchange" class="text-xs bg-gray-600 px-1 rounded">
                      {{ stock.exchange }}
                    </span>
                    <span v-if="searchStatsManager.getSearchCount(stock.code) > 0" 
                          class="text-xs bg-orange-500 bg-opacity-20 text-orange-400 px-1 rounded">
                      {{ searchStatsManager.getSearchCount(stock.code) }}次
                    </span>
                  </div>
                </div>
                <Icon name="add" class="text-blue-400 flex-shrink-0" />
              </div>
            </div>
          </div>
            <!-- 搜索无结果 -->
          <div v-else-if="searchKeyword.trim() && searchResults.length === 0 && !isSearching">
            <div class="text-center py-8">
              <Icon name="search" class="text-3xl text-gray-500 mb-2" />
              <p class="text-gray-400 text-sm">未找到匹配的股票</p>
              <p class="text-gray-500 text-xs mt-1">请尝试输入完整的股票代码或名称</p>
            </div>
          </div>
          
          <!-- 搜索加载状态 -->
          <div v-else-if="searchKeyword.trim() && isSearching">
            <div class="text-center py-8">
              <div class="loading-spinner mx-auto mb-4"></div>
              <p class="text-gray-400 text-sm">正在搜索...</p>
            </div>
          </div><!-- 热门股票推荐 -->
          <div v-else-if="combinedPopularStocks.length > 0">
            <div class="text-sm text-gray-400 mb-2">热门推荐 (基于搜索次数):</div>
            <div class="space-y-2">              <div 
                v-for="stock in combinedPopularStocks" 
                :key="stock.code"
                class="flex items-center justify-between p-3 bg-gray-700 rounded cursor-pointer hover:bg-gray-600"
                @click="addStockToWatchlist(stock)"
              >
                <div>
                  <div class="font-medium">{{ stock.code }} {{ stock.name }}</div>
                  <div class="text-sm text-gray-400 flex items-center gap-2">
                    <span>{{ stock.industry || '未分类' }}</span>
                    <span v-if="stock.searchCount > 0" class="text-xs bg-blue-500 bg-opacity-20 text-blue-400 px-1 rounded">
                      {{ stock.searchCount }}次搜索
                    </span>
                    <span v-else class="text-xs bg-green-500 bg-opacity-20 text-green-400 px-1 rounded">
                      热门
                    </span>
                  </div>
                </div>
                <Icon name="add" class="text-blue-400" />
              </div>
            </div>
          </div>
          
          <!-- 无推荐数据时的提示 -->
          <div v-else>
            <div class="text-center py-8">
              <Icon name="search" class="text-3xl text-gray-500 mb-2" />
              <p class="text-gray-400 text-sm">输入股票代码或名称开始搜索</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { storeToRefs } from 'pinia'
import { useStockDataStore } from '@/store/modules/stockData'
import { searchStatsManager } from '@/utils/searchStats'
import Icon from '@/components/common/Icon.vue'
import IconButton from '@/components/common/IconButton.vue'
import api from '@/services/api'

const router = useRouter()
const stockStore = useStockDataStore()
const { watchlistStocks, loading, error } = storeToRefs(stockStore)

// 模态框状态
const showAddModal = ref(false)
const searchKeyword = ref('')
const searchResults = ref([])
const isSearching = ref(false)

// 热门股票推荐（基于搜索统计和后端热门股票）
const popularStocks = computed(() => {
  const searchStats = searchStatsManager.getPopularStocks(5)
  return searchStats
})

// 后端热门股票数据
const hotStocks = ref([])

// 获取后端热门股票
const loadHotStocks = async () => {
  try {
    const response = await api.stockSearch.getPopularStocks({ limit: 5 })
    hotStocks.value = response.data || []
  } catch (error) {
    console.error('获取热门股票失败:', error)
    hotStocks.value = []
  }
}

// 合并热门推荐（优先显示搜索统计，再显示后端热门股票）
const combinedPopularStocks = computed(() => {
  const searchBased = popularStocks.value
  const backendBased = hotStocks.value
  
  // 合并并去重
  const combined = [...searchBased]
  const existingCodes = new Set(searchBased.map(stock => stock.code))
  
  for (const stock of backendBased) {
    if (!existingCodes.has(stock.code)) {
      combined.push({
        code: stock.code,
        name: stock.name,
        industry: stock.industry || '未分类',
        searchCount: 0 // 后端热门股票没有搜索次数
      })
      if (combined.length >= 8) break // 限制总数
    }
  }
  
  return combined
})

// 页面加载时拉取自选股和股票数据
const loadData = async () => {
  try {
    // 并行加载自选股列表、热门股票
    await Promise.all([
      stockStore.loadWatchlist(),
      loadHotStocks()
    ])
  } catch (error) {
    console.error('加载数据失败:', error)
  }
}

onMounted(loadData)

// 组件卸载时清理定时器
onUnmounted(() => {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }
})

// 重试加载数据
const retryLoadData = () => {
  stockStore.error = null
  loadData()
}

const showAddStockModal = () => {
  showAddModal.value = true
  searchKeyword.value = ''
  searchResults.value = []
}
const hideAddStockModal = () => {
  showAddModal.value = false
}

// 搜索防抖定时器
let searchTimeout = null

// 搜索股票（带防抖）
const searchStocks = () => {
  // 清除之前的定时器
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }
  
  // 设置新的防抖定时器
  searchTimeout = setTimeout(async () => {
    if (!searchKeyword.value.trim()) {
      searchResults.value = []
      return
    }
    
    try {
      // 调用后端搜索API
      const response = await api.stockSearch.quickSearchStocks(searchKeyword.value, 20)
      searchResults.value = response.data || []
    } catch (error) {
      console.error('搜索股票失败:', error)
      searchResults.value = []
    }
  }, 300) // 300ms防抖延迟
}

// 添加股票到自选股（API）
const addStockToWatchlist = async (stock) => {
  if (stockStore.watchlist.includes(stock.code)) {
    alert('该股票已在自选股中')
    return
  }
  
  try {
    // 记录搜索统计
    searchStatsManager.incrementSearchCount(stock.code, stock.name, stock.industry)
    
    await stockStore.addToWatchlist(stock.code)
    hideAddStockModal()
  } catch (err) {
    alert(err.message || '添加失败')
  }
}

// 查看股票详情
const viewStockDetail = (stock) => {
  router.push({
    name: 'Analysis',
    query: { stock: stock.code, name: stock.name }
  })
}

// 从自选股中删除（API）
const removeFromWatchlist = async (stock) => {
  if (confirm(`确定要删除 ${stock.name} 吗？`)) {
    try {
      await stockStore.removeFromWatchlist(stock.code)
    } catch (err) {
      alert(err.message || '删除失败')
    }
  }
}

// 价格颜色
const getPriceColorClass = (changePercent) => {
  if (changePercent > 0) return 'text-green-400'
  if (changePercent < 0) return 'text-red-400'
  return 'text-gray-400'
}
// 涨跌幅背景
const getChangeBackgroundClass = (changePercent) => {
  if (changePercent > 0) return 'bg-green-500 bg-opacity-20 text-green-400'
  if (changePercent < 0) return 'bg-red-500 bg-opacity-20 text-red-400'
  return 'bg-gray-500 bg-opacity-20 text-gray-400'
}
// 格式化成交量
const formatVolume = (volume) => {
  if (volume >= 100000000) {
    return (volume / 100000000).toFixed(1) + '亿'
  } else if (volume >= 10000) {
    return (volume / 10000).toFixed(1) + '万'
  } else {
    return volume.toString()
  }
}
</script>

<style scoped lang="scss">
.watchlist-page {
  max-width: 1400px;
  margin: 0 auto;
}

.data-table {
  table {
    border-collapse: collapse;
    
    th {
      background: var(--bg-tertiary);
      color: var(--text-muted);
      font-weight: 500;
      font-size: 0.875rem;
      padding: 0.75rem 1rem;
      text-align: left;
      border-bottom: 1px solid var(--border-color);
      
      &:first-child {
        border-top-left-radius: 8px;
      }
      
      &:last-child {
        border-top-right-radius: 8px;
      }
      
      &.text-center {
        text-align: center;
      }
    }
    
    td {
      padding: 1rem;
      border-bottom: 1px solid var(--border-color);
      transition: background-color 0.3s ease;
      vertical-align: middle;
    }
    
    .watchlist-row {
      &:hover td {
        background: var(--bg-tertiary);
      }
      
      &:last-child td {
        border-bottom: none;
      }
    }
  }
}

// 模态框样式
.fixed {
  backdrop-filter: blur(4px);
}

// 搜索结果动画
.space-y-2 > div {
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateX(4px);
  }
}

// 搜索结果滚动条样式
.max-h-60 {
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: var(--bg-quaternary);
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: var(--text-muted);
    border-radius: 3px;
    
    &:hover {
      background: var(--accent-primary);
    }
  }
}

// 搜索项标签样式
.text-xs.bg-blue-500,
.text-xs.bg-orange-500,
.text-xs.bg-gray-600 {
  transition: all 0.2s ease;
  
  &:hover {
    transform: scale(1.05);
  }
}

// 加载动画
.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid var(--border-color);
  border-top: 2px solid var(--accent-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

// 按钮悬停效果
button {
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-1px);
  }
}

// 价格动画
.font-mono {
  transition: color 0.3s ease;
}

// 空状态样式
.text-center {
  i {
    opacity: 0.5;
    transition: all 0.3s ease;
  }
  
  &:hover i {
    opacity: 0.8;
    transform: scale(1.1);
  }
}

// 卡片悬停效果
.card {
  &:hover {
    transform: translateY(-2px);
  }
}

// 表格行悬停效果
.watchlist-row {
  cursor: default;
  
  .icon-button {
    opacity: 0.8;
    transition: all 0.2s ease;
    
    &:hover {
      opacity: 1;
      transform: scale(1.05);
    }
  }
  
  &:hover .icon-button {
    opacity: 1;
  }
}

// 侧边栏统计样式
.space-y-3 > div {
  padding: 0.25rem 0;
  transition: all 0.3s ease;
  
  &:hover {
    padding-left: 0.5rem;
    border-left: 2px solid var(--accent-primary);
  }
}

// 预警提醒动画
.space-y-3 .flex {
  animation: slideInRight 0.5s ease;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// 自选股页面特有的工具提示样式
.watchlist-page {
  .icon-button[title]:hover::after {
    background: linear-gradient(135deg, var(--bg-tertiary) 0%, var(--bg-quaternary) 100%);
    border: 1px solid var(--border-light);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    font-weight: 500;
  }
  // 操作按钮组的整体样式优化
  .flex.items-center.gap-1 {
    .icon-button {
      &--ghost {
        color: var(--text-muted);
        
        &:hover {
          background: var(--bg-tertiary);
          transform: scale(1.05);
        }
      }
        // 查看图表按钮样式
      &[title="分析股票"] {
        color: var(--accent-tertiary);
        &:hover {
          color: white;
          background: var(--accent-tertiary);
        }
      }
      
      // 删除按钮样式
      &[title="移除自选"] {
        color: var(--price-down);
        &:hover {
          color: white;
          background: var(--price-down);
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .data-table {
    overflow-x: auto;
    
    table {
      min-width: 600px;
    }
  }
  
  .grid.lg\\:grid-cols-3 {
    grid-template-columns: 1fr;
  }
}
</style>
