<template>
  <div class="page-container">
    <h1 class="text-2xl font-bold mb-6">股票列表</h1>
    
    <!-- 搜索和筛选 -->
    <div class="mb-6 flex flex-col md:flex-row gap-3 md:items-center">
      <div class="flex-1">
        <input 
          type="text" 
          v-model="searchQuery"
          class="input w-full" 
          placeholder="搜索股票代码或名称..." 
          @input="debouncedSearch"
        />
      </div>
      <div class="flex gap-2">
        <select v-model="exchange" class="select">
          <option value="">全部交易所</option>
          <option value="SH">上交所</option>
          <option value="SZ">深交所</option>
        </select>
        <select v-model="industry" class="select">
          <option value="">全部行业</option>
          <option v-for="ind in industries" :key="ind" :value="ind">{{ ind }}</option>
        </select>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="card">
      <div class="data-table">
        <table class="w-full">
          <thead>
            <tr>
              <th>代码</th>
              <th>名称</th>
              <th>交易所</th>
              <th>行业</th>
              <th class="text-center">操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-if="loading">
              <td colspan="5" class="text-center py-8">
                <div class="flex-center">
                  <div class="spinner mr-2"></div>
                  <span class="text-text-muted">加载中...</span>
                </div>
              </td>
            </tr>
            <tr v-else-if="stocks.length === 0">
              <td colspan="5" class="text-center py-8">
                <p class="text-text-muted">暂无数据</p>
              </td>
            </tr>
            <tr v-for="stock in stocks" :key="stock.code" class="stock-row">
              <td>
                <span class="font-mono">{{ stock.code }}</span>
              </td>
              <td>
                <span class="font-medium">{{ stock.name }}</span>
              </td>
              <td>{{ getExchangeName(stock.exchange) }}</td>
              <td>{{ stock.industry || '未分类' }}</td>
              <td class="text-center">
                <button class="btn-base btn-secondary text-xs" @click="viewStock(stock)">
                  查看详情
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      
      <!-- 分页 -->
      <div class="flex justify-between items-center p-4 border-t border-border-color">
        <div class="text-sm text-text-muted">
          总共 <span class="font-medium">{{ totalStocks }}</span> 条记录
        </div>
        <div class="flex gap-1">
          <button 
            class="btn-base btn-ghost" 
            :disabled="currentPage <= 1"
            @click="currentPage--"
          >
            上一页
          </button>
          <button class="btn-base btn-ghost font-medium">{{ currentPage }} / {{ totalPages }}</button>
          <button 
            class="btn-base btn-ghost" 
            :disabled="currentPage >= totalPages"
            @click="currentPage++"
          >
            下一页
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import debounce from 'lodash.debounce'
import api from '@/services/api'

const router = useRouter()
const stocks = ref([])
const loading = ref(false)
const searchQuery = ref('')
const exchange = ref('')
const industry = ref('')
const industries = ref([])
const currentPage = ref(1)
const totalStocks = ref(0)
const pageSize = 20

// 计算总页数
const totalPages = computed(() => {
  return Math.max(1, Math.ceil(totalStocks.value / pageSize))
})

// 加载股票列表数据
const loadStocks = async () => {
  try {
    loading.value = true
    
    const params = {
      skip: (currentPage.value - 1) * pageSize,
      limit: pageSize
    }
    
    // 添加筛选条件
    if (searchQuery.value) params.search = searchQuery.value
    if (exchange.value) params.exchange = exchange.value
    if (industry.value) params.industry = industry.value
    
    const response = await api.stockList.getStockList(params)
    stocks.value = response.items || []
    totalStocks.value = response.total || 0
    
    // 从返回数据中提取行业类别
    if (!industries.value.length) {
      collectIndustries()
    }
  } catch (error) {
    console.error('加载股票列表失败', error)
  } finally {
    loading.value = false
  }
}

// 收集行业分类
const collectIndustries = async () => {
  try {
    const response = await api.stockList.getIndustries()
    industries.value = response.industries || []
  } catch (error) {
    console.error('获取行业分类失败', error)
  }
}

// 交易所名称
const getExchangeName = (code) => {
  const exchangeMap = {
    'SH': '上交所',
    'SZ': '深交所'
  }
  return exchangeMap[code] || code
}

// 查看股票详情
const viewStock = (stock) => {
  router.push(`/stock/${stock.code}`)
}

// 防抖搜索
const debouncedSearch = debounce(() => {
  currentPage.value = 1 // 重置页码
  loadStocks()
}, 300)

// 监听筛选条件变化
watch([exchange, industry], () => {
  currentPage.value = 1 // 重置页码
  loadStocks()
})

// 监听页码变化
watch(currentPage, () => {
  loadStocks()
})

// 组件挂载后加载数据
loadStocks()
</script>

<style scoped>
.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-color);
  border-top-color: var(--accent-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {transform: rotate(360deg);}
}

.data-table {
  overflow-x: auto;
}

.data-table table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th {
  text-align: left;
  padding: 1rem;
  font-weight: 500;
  color: var(--text-muted);
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
}

.data-table td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--border-color);
}

.stock-row:hover td {
  background: var(--bg-tertiary);
}

.stock-row:last-child td {
  border-bottom: none;
}
</style>
