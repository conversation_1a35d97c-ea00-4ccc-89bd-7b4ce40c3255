<template>
  <div class="stock-detail page-container">
    <!-- 股票基本信息 -->
    <div class="stock-header card p-6 mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">          <div class="w-16 h-16 bg-gradient-primary rounded-2xl flex-center">
            <AppIcon name="analytics" class="text-white" :size="28" />
          </div>
          <div>
            <h1 class="text-2xl font-bold text-text-primary">{{ stockInfo.name }}</h1>
            <p class="text-text-muted">{{ stockInfo.code }}</p>
            <div class="flex items-center space-x-2 mt-1">
              <span class="text-xs bg-bg-tertiary px-2 py-1 rounded">{{ stockInfo.market }}</span>
              <span class="text-xs bg-bg-tertiary px-2 py-1 rounded">{{ stockInfo.industry }}</span>
            </div>
          </div>
        </div>
        
        <div class="text-right">
          <div class="text-3xl font-bold mono-font">¥{{ stockInfo.currentPrice }}</div>
          <div class="flex items-center space-x-2 mt-1">
            <span :class="stockInfo.changePercent >= 0 ? 'price-up' : 'price-down'" class="text-lg mono-font">
              {{ stockInfo.changePercent >= 0 ? '+' : '' }}{{ stockInfo.changePercent }}%
            </span>
            <span :class="stockInfo.changeAmount >= 0 ? 'price-up' : 'price-down'" class="mono-font">
              {{ stockInfo.changeAmount >= 0 ? '+' : '' }}¥{{ stockInfo.changeAmount }}
            </span>
          </div>
          <div class="text-sm text-text-muted mt-1">
            成交量: {{ formatVolume(stockInfo.volume) }}
          </div>
        </div>
      </div>
    </div>

    <!-- 图表和数据区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
      <!-- K线图 -->
      <div class="lg:col-span-2 card p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold">K线图</h3>
          <div class="flex space-x-2">
            <button
              v-for="period in timePeriods"
              :key="period.value"
              @click="currentPeriod = period.value"
              :class="[
                'btn-base text-sm',
                currentPeriod === period.value ? 'btn-primary' : 'btn-secondary'
              ]"
            >
              {{ period.label }}
            </button>
          </div>
        </div>
        <div class="chart-container h-96">          <div class="flex-center h-full text-text-muted">
            <div class="text-center">
              <AppIcon name="chart-candlestick" :size="40" class="mb-2" />
              <p>K线图加载中...</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 实时数据 -->
      <div class="space-y-4">
        <!-- 今日统计 -->
        <div class="card p-4">
          <h4 class="text-sm font-medium text-text-muted mb-3">今日统计</h4>
          <div class="space-y-3">
            <div class="flex justify-between">
              <span class="text-sm text-text-muted">开盘价</span>
              <span class="mono-font">¥{{ stockInfo.openPrice }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-text-muted">最高价</span>
              <span class="mono-font price-up">¥{{ stockInfo.highPrice }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-text-muted">最低价</span>
              <span class="mono-font price-down">¥{{ stockInfo.lowPrice }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-text-muted">昨收价</span>
              <span class="mono-font">¥{{ stockInfo.prevClose }}</span>
            </div>
          </div>
        </div>

        <!-- 市场数据 -->
        <div class="card p-4">
          <h4 class="text-sm font-medium text-text-muted mb-3">市场数据</h4>
          <div class="space-y-3">
            <div class="flex justify-between">
              <span class="text-sm text-text-muted">市值</span>
              <span class="mono-font">{{ formatMarketCap(stockInfo.marketCap) }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-text-muted">市盈率</span>
              <span class="mono-font">{{ stockInfo.pe }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-text-muted">市净率</span>
              <span class="mono-font">{{ stockInfo.pb }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-text-muted">换手率</span>
              <span class="mono-font">{{ stockInfo.turnoverRate }}%</span>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="card p-4">
          <div class="space-y-3">
            <button
              @click="toggleWatchlist"
              :class="[
                'w-full btn-base',
                isInWatchlist ? 'btn-secondary' : 'btn-primary'
              ]"
            >              <AppIcon :name="isInWatchlist ? 'star-filled' : 'star'" :size="16" class="mr-2" />
              {{ isInWatchlist ? '取消关注' : '加入自选' }}
            </button>
            <button class="w-full btn-secondary">
              <AppIcon name="notification" :size="16" class="mr-2" />
              价格提醒
            </button>
            <button class="w-full btn-secondary">
              <AppIcon name="share" :size="16" class="mr-2" />
              分享股票
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 技术指标和分析 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 技术指标 -->
      <div class="card p-6">
        <h3 class="text-lg font-semibold mb-4">技术指标</h3>
        <div class="chart-container h-64">
          <div class="flex-center h-full text-text-muted">            <div class="text-center">
              <AppIcon name="chart-line" :size="40" class="mb-2" />
              <p>技术指标加载中...</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 相关新闻 -->
      <div class="card p-6">
        <h3 class="text-lg font-semibold mb-4">相关新闻</h3>
        <div class="space-y-4">
          <div v-for="news in relatedNews" :key="news.id" class="border-b border-border-color pb-3 last:border-b-0">
            <h4 class="font-medium mb-1 line-clamp-2 hover:text-accent-primary cursor-pointer">
              {{ news.title }}
            </h4>
            <p class="text-sm text-text-muted mb-1">{{ news.summary }}</p>
            <div class="flex justify-between items-center">
              <span class="text-xs text-text-muted">{{ news.source }}</span>
              <span class="text-xs text-text-muted">{{ news.publishTime }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue'
import { useRoute } from 'vue-router'
import AppIcon from '@/components/common/Icon.vue'
import api from '@/services/api'

const route = useRoute()
const stockCode = computed(() => route.params.code)

// 时间周期选项
const timePeriods = ref([
  { label: '1分', value: '1m' },
  { label: '5分', value: '5m' },
  { label: '15分', value: '15m' },
  { label: '30分', value: '30m' },
  { label: '1小时', value: '1h' },
  { label: '日K', value: '1d' },
  { label: '周K', value: '1w' },
  { label: '月K', value: '1M' }
])

const currentPeriod = ref('1d')

// 股票信息
const stockInfo = ref({
  name: '贵州茅台',
  code: '600519',
  market: '上交所',
  industry: '白酒',
  currentPrice: '1678.90',
  changePercent: 2.15,
  changeAmount: 35.20,
  volume: '125000',
  openPrice: '1645.00',
  highPrice: '1685.50',
  lowPrice: '1642.80',
  prevClose: '1643.70',
  marketCap: '2100000000000',
  pe: '25.6',
  pb: '8.2',
  turnoverRate: '0.68'
})

// 是否在自选股中
const isInWatchlist = ref(true)

// 相关新闻
const relatedNews = ref([
  {
    id: 1,
    title: '茅台Q3业绩超预期，净利润同比增长18%',
    summary: '公司三季度业绩表现强劲，高端白酒需求持续旺盛...',
    source: '财经新闻',
    publishTime: '2小时前'
  },
  {
    id: 2,
    title: '白酒板块集体上涨，机构看好行业前景',
    summary: '受益于消费升级和品牌价值提升，白酒龙头企业...',
    source: '投资快报',
    publishTime: '4小时前'
  },
  {
    id: 3,
    title: '茅台新品发布会将于下月举行',
    summary: '公司计划推出新系列产品，进一步丰富产品矩阵...',
    source: '企业公告',
    publishTime: '6小时前'
  }
])

// 格式化成交量
const formatVolume = (volume) => {
  const num = parseInt(volume)
  if (num >= 100000000) {
    return (num / 100000000).toFixed(1) + '亿'
  } else if (num >= 10000) {
    return (num / 10000).toFixed(1) + '万'
  } else {
    return num.toString()
  }
}

// 格式化市值
const formatMarketCap = (marketCap) => {
  const num = parseInt(marketCap)
  if (num >= 1000000000000) {
    return (num / 1000000000000).toFixed(1) + '万亿'
  } else if (num >= 100000000) {
    return (num / 100000000).toFixed(1) + '亿'
  } else {
    return num.toString()
  }
}

// 加载股票基本信息
const loadStockInfo = async () => {
  try {
    const response = await api.stockList.getStockInfo(stockCode.value)
    
    // 获取实时数据
    const realtimeData = await api.stockData.getRealtimeData(stockCode.value)
    
    // 更新股票信息
    stockInfo.value = {
      name: response.name,
      code: response.code,
      market: response.exchange === 'SH' ? '上交所' : '深交所',
      industry: response.industry || '未分类',
      currentPrice: realtimeData.p || '0.00',
      changePercent: realtimeData.pc || 0,
      changeAmount: realtimeData.ud || 0,
      volume: realtimeData.v || 0,
      openPrice: realtimeData.o || '0.00',
      highPrice: realtimeData.h || '0.00',
      lowPrice: realtimeData.l || '0.00',
      prevClose: realtimeData.yc || '0.00',
      marketCap: realtimeData.sz || 0,
      pe: realtimeData.pe || 0,
      pb: realtimeData.sjl || 0,
      turnoverRate: realtimeData.hs || 0
    }
  } catch (error) {
    console.error('加载股票信息失败:', error)
  }
}

// 加载股票K线数据
const loadStockData = async () => {
  try {
    // 根据当前选择的周期加载K线数据
    const response = await api.stockData.getKlineData(
      stockCode.value, 
      mapPeriodToApiValue(currentPeriod.value)
    )
    
    // 格式化K线数据供图表使用
    klineData.value = formatKlineData(response)
    
    // TODO: 初始化或更新K线图
    initializeKlineChart()
  } catch (error) {
    console.error('加载K线数据失败:', error)
  }
}

// 将前端周期字符串映射为API所需的值
const mapPeriodToApiValue = (period) => {
  const periodMap = {
    '1m': '1m',
    '5m': '5m',
    '15m': '15m',
    '30m': '30m',
    '1h': '60m',
    '1d': 'dq', // 日K前复权
    '1w': 'wq', // 周K前复权
    '1M': 'mq'  // 月K前复权
  }
  return periodMap[period] || 'dq'
}

// 格式化K线数据
const formatKlineData = (data = []) => {
  return data.map(item => ({
    time: item.d,
    open: item.o,
    high: item.h,
    low: item.l,
    close: item.c,
    volume: item.v,
    amount: item.e,
    changePercent: item.zd,
    turnoverRate: item.hs
  }))
}

// 初始化K线图
const initializeKlineChart = () => {
  // TODO: 使用图表库(如ECharts)初始化K线图
  console.log('初始化图表:', klineData.value.length, '条数据')
}

// 检查股票是否在用户自选股中
const checkWatchlistStatus = async () => {
  try {
    const response = await api.watchlist.getWatchlist()
    const watchlistCodes = response.map(item => item.stock_code)
    isInWatchlist.value = watchlistCodes.includes(stockCode.value)
  } catch (error) {
    console.error('检查自选股状态失败:', error)
  }
}

// 切换自选股状态
const toggleWatchlist = async () => {
  try {
    if (isInWatchlist.value) {
      await api.watchlist.removeFromWatchlist(stockCode.value)
    } else {
      await api.watchlist.addToWatchlist(stockCode.value)
    }
    isInWatchlist.value = !isInWatchlist.value
  } catch (error) {
    console.error('更新自选股状态失败:', error)
  }
}

onMounted(() => {
  // 加载股票详情数据
  console.log('加载股票详情:', stockCode.value)
  loadStockInfo()
  loadStockData()
  checkWatchlistStatus()
})

// 定义K线数据
const klineData = ref([])
</script>

<style scoped lang="scss">
.page-container {
  max-width: 1400px;
  margin: 0 auto;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.chart-container {
  position: relative;
  background: var(--bg-tertiary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}
</style>
