<template>
  <div class="analysis-page">
    <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
      <!-- 左侧：股票选择和基本信息 -->
      <div class="xl:col-span-1">
        <!-- 股票选择面板 -->
        <div class="card p-6 mb-6">
          <h3 class="text-lg font-semibold mb-4">股票选择</h3>
          <div class="space-y-4">
            <div>
              <label class="block text-sm text-gray-400 mb-2">股票代码</label>
              <select
                v-model="selectedStock"
                class="input-field w-full"
                @change="onStockChange"
              >
                <option value="">选择股票</option>
                <optgroup
                  v-if="combinedPopularStocks.length > 0"
                  label="热门推荐"
                >
                  <option
                    v-for="stock in combinedPopularStocks"
                    :key="stock.code"
                    :value="stock.code"
                  >
                    {{ stock.code }} {{ stock.name }}
                    <span v-if="stock.searchCount > 0"
                      >({{ stock.searchCount }}次搜索)</span
                    >
                  </option>
                </optgroup>
              </select>
            </div>
            <!-- 时间周期已隐藏，默认使用日线 -->
            <!--
            <div>
              <label class="block text-sm text-gray-400 mb-2">时间周期</label>
              <select v-model="selectedPeriod" class="input-field w-full" @change="onPeriodChange">
                <option value="D">日线</option>
                <option value="W">周线</option>
                <option value="M">月线</option>
              </select>
            </div>
            -->

            <div>
              <label class="block text-sm text-gray-400 mb-2">时间范围</label>
              <select
                v-model="selectedRange"
                class="input-field w-full"
                @change="onRangeChange"
              >
                <option value="30">最近30天</option>
                <option value="60">最近60天</option>
                <option value="120">最近120天</option>
                <option value="250">最近250天</option>
              </select>
            </div>
            <button
              class="btn-primary w-full flex items-center justify-center"
              @click="loadStockAnalysis"
              :disabled="!selectedStock || loading"
            >
              <AppIcon name="chart-line" class="mr-2" />
              {{ loading ? "分析中..." : "开始分析" }}
            </button>
          </div>
        </div>

        <!-- 股票基本信息 -->
        <div v-if="stockInfo" class="card p-6">
          <h3 class="text-lg font-semibold mb-4">基本信息</h3>
          <div class="space-y-3">
            <div class="flex justify-between">
              <span class="text-gray-400">股票名称</span>
              <span>{{ stockInfo.name }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-400">股票代码</span>
              <span>{{ stockInfo.code }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-400">当前价格</span>
              <span
                :class="
                  stockInfo.changePercent >= 0
                    ? 'text-green-400'
                    : 'text-red-400'
                "
              >
                ¥{{ stockInfo.price }}
              </span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-400">涨跌幅</span>
              <span
                :class="
                  stockInfo.changePercent >= 0
                    ? 'text-green-400'
                    : 'text-red-400'
                "
              >
                {{ stockInfo.changePercent >= 0 ? "+" : ""
                }}{{ stockInfo.changePercent }}%
              </span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-400">成交量</span>
              <span>{{ stockInfo.volume }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-400">市值</span>
              <span>{{ stockInfo.marketCap }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：图表区域 -->
      <div class="xl:col-span-3">
        <!-- K线图表 -->
        <div class="card p-6 mb-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold">K线图表</h3>
            <div class="flex space-x-2">
              <button
                class="px-3 py-1 text-sm rounded"
                :class="
                  chartOverlay === 'none'
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-400 hover:text-white'
                "
                @click="setChartOverlay('none')"
              >
                主图
              </button>
              <button
                class="px-3 py-1 text-sm rounded"
                :class="
                  chartOverlay === 'ma'
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-400 hover:text-white'
                "
                @click="setChartOverlay('ma')"
              >
                均线
              </button>
              <button
                class="px-3 py-1 text-sm rounded"
                :class="
                  chartOverlay === 'bollinger'
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-400 hover:text-white'
                "
                @click="setChartOverlay('bollinger')"
              >
                布林带
              </button>
            </div>
          </div>
          <ChartContainer
            ref="klineChartContainer"
            height="500px"
            :loading="loading"
            :error="error"
            :has-data="!!chartData?.kline || !selectedStock"
            empty-text="请选择股票开始分析"
            loading-text="K线图表加载中..."
            empty-icon="i-carbon-chart-candlestick text-blue-400"
            @retry="loadStockAnalysis"
          />
        </div>

        <!-- 技术指标图表 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div class="card p-6">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-semibold">MACD指标</h3>
              <button
                class="text-sm text-blue-400 hover:text-blue-300"
                @click="showIndicatorSettings('macd')"
              >
                参数设置
              </button>
            </div>
            <ChartContainer
              ref="macdChartContainer"
              height="300px"
              :loading="loading"
              :error="error"
              :has-data="!!chartData?.macd || !selectedStock"
              empty-text="请选择股票开始分析"
              loading-text="MACD指标加载中..."
              empty-icon="i-carbon-chart-line text-purple-400"
              @retry="loadStockAnalysis"
            />
          </div>

          <div class="card p-6">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-semibold">成交量</h3>
              <button class="text-sm text-blue-400 hover:text-blue-300">
                详细分析
              </button>
            </div>
            <ChartContainer
              ref="volumeChartContainer"
              height="300px"
              :loading="loading"
              :error="error"
              :has-data="!!chartData?.volume || !selectedStock"
              empty-text="请选择股票开始分析"
              loading-text="成交量图表加载中..."
              empty-icon="i-carbon-chart-column text-green-400"
              @retry="loadStockAnalysis"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 指标参数设置对话框 -->
    <IndicatorSettings
      v-model="showSettingsDialog"
      :indicator-type="currentIndicatorType"
      :current-settings="currentIndicatorSettings"
      @confirm="handleIndicatorSettingsConfirm"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, computed } from "vue";
import { useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import * as echarts from "echarts";
import AppIcon from "@/components/common/Icon.vue";
import ChartContainer from "@/components/analysis/ChartContainer.vue";
import IndicatorSettings from "@/components/analysis/IndicatorSettings.vue";
import { api } from "@/services/api";
import { searchStatsManager } from "@/utils/searchStats";

const route = useRoute();

// 选择状态
const selectedStock = ref("");
const selectedPeriod = ref("D");
const selectedRange = ref("30");
const chartOverlay = ref("none");

// 数据状态
const loading = ref(false);
const chartData = ref(null);
const stockInfo = ref(null);
const error = ref(null);

// ECharts实例
const klineChart = ref(null);
const macdChart = ref(null);
const volumeChart = ref(null);

// 热门股票列表 - 基于搜索统计和后端数据
const hotStocks = ref([]);

// 基于搜索统计的热门股票
const popularStocks = computed(() => {
  const searchStats = searchStatsManager.getPopularStocks(5);
  return searchStats;
});

// 合并热门推荐（优先显示搜索统计，再显示后端热门股票）
const combinedPopularStocks = computed(() => {
  const searchBased = popularStocks.value;
  const backendBased = hotStocks.value;

  // 合并并去重
  const combined = [...searchBased];
  const existingCodes = new Set(searchBased.map((stock) => stock.code));

  for (const stock of backendBased) {
    if (!existingCodes.has(stock.code)) {
      combined.push({
        code: stock.code,
        name: stock.name,
        industry: stock.industry || "未分类",
        searchCount: 0, // 后端热门股票没有搜索次数
      });
      if (combined.length >= 10) break; // 限制总数
    }
  }

  return combined;
});

// 获取后端热门股票
const loadHotStocks = async () => {
  try {
    const response = await api.stockSearch.getPopularStocks({ limit: 8 });
    hotStocks.value = response.data || [];
  } catch (error) {
    console.error("获取热门股票失败:", error);
    // 如果API失败，使用默认热门股票
    hotStocks.value = [
      { code: "000001", name: "平安银行", industry: "银行" },
      { code: "000002", name: "万科A", industry: "房地产" },
      { code: "000858", name: "五粮液", industry: "白酒" },
      { code: "002230", name: "科大讯飞", industry: "人工智能" },
      { code: "002415", name: "海康威视", industry: "安防" },
      { code: "600036", name: "招商银行", industry: "银行" },
      { code: "600519", name: "贵州茅台", industry: "白酒" },
      { code: "600887", name: "伊利股份", industry: "乳业" },
    ];
  }
};

// 图表容器引用
const klineChartContainer = ref(null);
const macdChartContainer = ref(null);
const volumeChartContainer = ref(null);

// 指标设置相关
const showSettingsDialog = ref(false);
const currentIndicatorType = ref("macd");
const currentIndicatorSettings = ref({});

// 事件处理函数
const onStockChange = () => {
  console.log("Stock changed:", selectedStock.value);
  stockInfo.value = null;
  chartData.value = null;

  // 记录搜索统计
  if (selectedStock.value) {
    const selectedStockData = combinedPopularStocks.value.find(
      (stock) => stock.code === selectedStock.value
    );
    if (selectedStockData) {
      searchStatsManager.incrementSearchCount(
        selectedStockData.code,
        selectedStockData.name,
        selectedStockData.industry
      );
    }
  }
};

const onPeriodChange = () => {
  console.log("Period changed:", selectedPeriod.value);
  if (selectedStock.value) {
    loadStockAnalysis();
  }
};

const onRangeChange = () => {
  console.log("Range changed:", selectedRange.value);
  if (selectedStock.value) {
    loadStockAnalysis();
  }
};

const setChartOverlay = (overlay) => {
  chartOverlay.value = overlay;
  console.log("Chart overlay changed:", overlay);
  // 重新渲染K线图表以应用叠加层
  if (chartData.value && klineChart.value) {
    renderKlineChart();
  }
};

// 显示指标参数设置
const showIndicatorSettings = (indicatorType) => {
  currentIndicatorType.value = indicatorType;
  currentIndicatorSettings.value = getIndicatorSettings(indicatorType);
  showSettingsDialog.value = true;
};

// 获取指标当前设置
const getIndicatorSettings = (indicatorType) => {
  // 这里可以从localStorage或其他地方获取用户保存的设置
  const savedSettings = localStorage.getItem(
    `indicator_settings_${indicatorType}`
  );
  return savedSettings ? JSON.parse(savedSettings) : {};
};

// 处理指标设置确认
const handleIndicatorSettingsConfirm = (data) => {
  console.log("Indicator settings confirmed:", data);
  // 保存设置到localStorage
  localStorage.setItem(
    `indicator_settings_${data.type}`,
    JSON.stringify(data.settings)
  );

  // 重新加载对应的指标数据
  if (selectedStock.value) {
    loadStockAnalysis();
  }
};

// 工具函数
const calculateMA = (data, period) => {
  const result = [];
  for (let i = 0; i < data.length; i++) {
    if (i < period - 1) {
      result.push(null);
    } else {
      const sum = data.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0);
      result.push((sum / period).toFixed(2));
    }
  }
  return result;
};

// 转换前端周期格式到后端格式
const convertPeriodToBackend = (period) => {
  const periodMap = {
    D: "D",
    D1: "D",
    W: "W",
    W1: "W",
    M: "M",
    M1: "M",
  };
  return periodMap[period] || "D";
};

// 计算布林带
const calculateBollingerBands = (data, period = 20, stdDev = 2) => {
  const result = {
    middle: [],
    upper: [],
    lower: [],
  };

  for (let i = 0; i < data.length; i++) {
    if (i < period - 1) {
      result.middle.push(null);
      result.upper.push(null);
      result.lower.push(null);
    } else {
      const slice = data.slice(i - period + 1, i + 1);
      const avg = slice.reduce((a, b) => a + b, 0) / period;
      const variance =
        slice.reduce((a, b) => a + Math.pow(b - avg, 2), 0) / period;
      const std = Math.sqrt(variance);

      result.middle.push(avg.toFixed(2));
      result.upper.push((avg + stdDev * std).toFixed(2));
      result.lower.push((avg - stdDev * std).toFixed(2));
    }
  }

  return result;
};

const formatVolume = (volume) => {
  if (!volume) return "0";
  if (volume >= 100000000) {
    return (volume / 100000000).toFixed(2) + "亿";
  } else if (volume >= 10000) {
    return (volume / 10000).toFixed(2) + "万";
  }
  return volume.toString();
};

const formatMarketCap = (marketCap) => {
  if (!marketCap) return "0";
  if (marketCap >= 100000000) {
    return (marketCap / 100000000).toFixed(2) + "亿";
  }
  return marketCap.toString();
};

// 处理窗口大小变化
const handleResize = () => {
  if (klineChart.value) klineChart.value.resize();
  if (macdChart.value) macdChart.value.resize();
  if (volumeChart.value) volumeChart.value.resize();
};

const loadStockAnalysis = async () => {
  if (!selectedStock.value) return;

  console.log("开始加载股票分析数据:", selectedStock.value);
  loading.value = true;
  error.value = null;

  try {
    // 获取股票基本信息
    await loadStockInfo();

    // 计算日期范围
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - parseInt(selectedRange.value));
    const startDateStr = startDate.toISOString().split("T")[0];
    const endDateStr = endDate.toISOString().split("T")[0];

    console.log("请求参数:", {
      stock: selectedStock.value,
      period: selectedPeriod.value,
      startDate: startDateStr,
      endDate: endDateStr,
    });

    // 转换周期格式
    const backendPeriod = convertPeriodToBackend(selectedPeriod.value);

    // 并行获取数据
    const [klineData, macdData] = await Promise.all([
      // 获取K线数据（包含指标）
      api.stockData
        .getKlineData(selectedStock.value, backendPeriod, {
          start_date: startDateStr,
          end_date: endDateStr,
          with_indicators: true,
          indicators: ["macd"],
        })
        .then((ret) => {
          console.log("K线数据API响应:", ret);
          return ret.data;
        })
        .catch((err) => {
          console.warn("K线数据获取失败，使用备用方案:", err);
          // 备用方案：尝试获取基础K线数据
          return api.stockData.getKlineData(
            selectedStock.value,
            backendPeriod,
            {
              start_date: startDateStr,
              end_date: endDateStr,
            }
          );
        }),
      // 单独获取MACD指标数据
      api.indicator
        .getIndicator(selectedStock.value, "macd", {
          start_date: startDateStr,
          end_date: endDateStr,
          freq: backendPeriod,
        })
        .then((ret) => {
          console.log("MACD数据API响应:", ret);
          return ret.data;
        })
        .catch((err) => {
          console.warn("MACD指标获取失败:", err);
          return null;
        }),
    ]);

    console.log("处理后的数据:", {
      klineData,
      macdData,
    });

    // 处理K线数据
    if (!klineData || !klineData.kline_data) {
      throw new Error("无法获取K线数据");
    }

    chartData.value = {
      kline: klineData,
      macd: macdData || klineData.indicators?.macd, // 优先使用单独获取的MACD数据
      volume: klineData.kline_data, // 成交量数据包含在K线数据中
    };

    console.log("设置 chartData:", chartData.value);

    // 等待DOM更新后渲染图表
    console.log("等待DOM更新...");
    await nextTick();
    console.log("DOM更新完成，开始渲染图表");
    await renderCharts();

    console.log("股票分析数据加载成功");
  } catch (err) {
    console.error("加载股票分析数据失败:", err);
    error.value =
      err.response?.data?.message || err.message || "加载股票分析数据失败";
    ElMessage.error(error.value);
  } finally {
    loading.value = false;
  }
};

// 加载股票基本信息
const loadStockInfo = async () => {
  try {
    const stockDetail = await api.stockList.getStockInfo(selectedStock.value);
    stockInfo.value = {
      code: selectedStock.value,
      name: stockDetail.name || "未知股票",
      price: stockDetail.current_price?.toFixed(2) || "0.00",
      changePercent: stockDetail.change_percent?.toFixed(2) || "0.00",
      volume: formatVolume(stockDetail.volume),
      marketCap: formatMarketCap(stockDetail.market_cap),
    };
  } catch (err) {
    console.warn("获取股票详细信息失败，使用基本信息:", err);
    // 如果获取详细信息失败，使用基本信息
    const stockDetails = combinedPopularStocks.value.find(
      (stock) => stock.code === selectedStock.value
    );
    stockInfo.value = {
      code: selectedStock.value,
      name: stockDetails?.name || "未知股票",
      price: "0.00",
      changePercent: "0.00",
      volume: "0万手",
      marketCap: "0亿",
    };
  }
};

// 渲染所有图表
const renderCharts = async () => {
  if (!chartData.value) {
    console.warn("No chart data available");
    return;
  }

  console.log("开始渲染图表, chartData:", chartData.value);
  console.log("容器引用状态:", {
    kline: !!klineChartContainer.value,
    macd: !!macdChartContainer.value,
    volume: !!volumeChartContainer.value,
  });

  try {
    console.log("开始渲染图表");
    await Promise.all([
      renderKlineChart(),
      renderMacdChart(),
      renderVolumeChart(),
    ]);
    console.log("所有图表渲染完成");
  } catch (err) {
    console.error("Failed to render charts:", err);
    ElMessage.error("图表渲染失败");
  }
};

// 渲染K线图表
const renderKlineChart = async () => {
  if (!klineChartContainer.value?.chartRef || !chartData.value.kline) {
    console.warn("K线图表渲染条件不满足:", {
      hasContainer: !!klineChartContainer.value?.chartRef,
      hasData: !!chartData.value?.kline,
      chartData: chartData.value,
    });
    return;
  }

  console.log("开始渲染K线图表");

  // 销毁旧图表
  if (klineChart.value) {
    klineChart.value.dispose();
  }

  // 确保容器存在
  const chartDom = klineChartContainer.value.chartRef;
  if (!chartDom) {
    console.error("图表容器 DOM 不存在");
    return;
  }

  console.log("初始化 ECharts 实例, 容器:", chartDom);
  klineChart.value = echarts.init(chartDom);

  const klineData = chartData.value.kline.kline_data || [];
  console.log("K线数据:", klineData);

  if (klineData.length === 0) {
    console.warn("K线数据为空");
    return;
  }

  const dates = klineData.map((item) => item.date);
  const values = klineData.map((item) => [
    item.open,
    item.close,
    item.low,
    item.high,
  ]);
  // 计算移动平均线
  const ma5 = calculateMA(
    klineData.map((item) => item.close),
    5
  );
  const ma10 = calculateMA(
    klineData.map((item) => item.close),
    10
  );
  const ma20 = calculateMA(
    klineData.map((item) => item.close),
    20
  );

  // 计算布林带
  const bollinger = calculateBollingerBands(
    klineData.map((item) => item.close),
    20,
    2
  );

  const option = {
    animation: true,
    backgroundColor: "transparent",
    grid: {
      left: "50px",
      right: "50px",
      top: "60px",
      bottom: "60px",
    },
    xAxis: {
      type: "category",
      data: dates,
      axisLine: { lineStyle: { color: "#666" } },
      axisLabel: { color: "#999" },
    },
    yAxis: {
      type: "value",
      scale: true,
      axisLine: { lineStyle: { color: "#666" } },
      axisLabel: { color: "#999" },
      splitLine: { lineStyle: { color: "#333" } },
    },
    tooltip: {
      trigger: "axis",
      backgroundColor: "rgba(0, 0, 0, 0.8)",
      borderColor: "#333",
      textStyle: { color: "#fff" },
      formatter: function (params) {
        const data = params[0];
        const kline = values[data.dataIndex];
        if (!kline) return "";

        return `
          <div>
            <div style="margin-bottom: 5px; font-weight: bold;">${data.axisValue}</div>
            <div>开盘: ${kline[0]}</div>
            <div>收盘: ${kline[1]}</div>
            <div>最低: ${kline[2]}</div>
            <div>最高: ${kline[3]}</div>
          </div>
        `;
      },
    },
    series: [
      {
        name: "K线",
        type: "candlestick",
        data: values,
        itemStyle: {
          color: "#ef4444",
          color0: "#22c55e",
          borderColor: "#ef4444",
          borderColor0: "#22c55e",
        },
      },
      ...(chartOverlay.value === "ma"
        ? [
            {
              name: "MA5",
              type: "line",
              data: ma5,
              smooth: true,
              lineStyle: { color: "#ff6b6b", width: 1 },
              showSymbol: false,
            },
            {
              name: "MA10",
              type: "line",
              data: ma10,
              smooth: true,
              lineStyle: { color: "#4ecdc4", width: 1 },
              showSymbol: false,
            },
            {
              name: "MA20",
              type: "line",
              data: ma20,
              smooth: true,
              lineStyle: { color: "#45b7d1", width: 1 },
              showSymbol: false,
            },
          ]
        : []),
      ...(chartOverlay.value === "bollinger"
        ? [
            {
              name: "布林上轨",
              type: "line",
              data: bollinger.upper,
              smooth: true,
              lineStyle: { color: "#ff6b6b", width: 1, type: "dashed" },
              showSymbol: false,
            },
            {
              name: "布林中轨",
              type: "line",
              data: bollinger.middle,
              smooth: true,
              lineStyle: { color: "#4ecdc4", width: 1 },
              showSymbol: false,
            },
            {
              name: "布林下轨",
              type: "line",
              data: bollinger.lower,
              smooth: true,
              lineStyle: { color: "#22c55e", width: 1, type: "dashed" },
              showSymbol: false,
            },
          ]
        : []),
    ],
  };

  console.log("设置图表配置");
  klineChart.value.setOption(option);
  console.log("K线图表渲染完成");
};

// 渲染MACD图表
const renderMacdChart = async () => {
  if (!macdChartContainer.value?.chartRef || !chartData.value.macd) {
    console.warn("MACD图表渲染条件不满足:", {
      hasContainer: !!macdChartContainer.value?.chartRef,
      hasData: !!chartData.value?.macd,
    });
    return;
  }

  console.log("开始渲染MACD图表");

  // 销毁旧图表
  if (macdChart.value) {
    macdChart.value.dispose();
  }

  const chartDom = macdChartContainer.value.chartRef;
  if (!chartDom) {
    console.error("MACD图表容器 DOM 不存在");
    return;
  }

  console.log("初始化 MACD ECharts 实例");
  macdChart.value = echarts.init(chartDom);

  const macdData = chartData.value.macd;
  console.log("MACD Data:", macdData); // 调试用

  let dates = [];
  let dif = [];
  let dea = [];
  let histogram = [];

  // 处理MACD数据格式 - 支持多种API返回格式
  if (macdData.data && Array.isArray(macdData.data)) {
    // 格式1: {data: [{date, MACD_12_26_9, MACDs_12_26_9, MACDh_12_26_9}, ...]}
    dates = macdData.data.map((item) => item.date);
    dif = macdData.data.map((item) =>
      parseFloat(item.MACD_12_26_9 || item.dif || item.diff || 0)
    );
    dea = macdData.data.map((item) =>
      parseFloat(item.MACDs_12_26_9 || item.dea || item.signal || 0)
    );
    histogram = macdData.data.map((item) =>
      parseFloat(item.MACDh_12_26_9 || item.histogram || item.hist || 0)
    );
  } else if (macdData.date && Array.isArray(macdData.date)) {
    // 格式2: {date: [], dif: [], dea: [], histogram: []}
    dates = macdData.date;
    dif = macdData.dif || macdData.diff || [];
    dea = macdData.dea || macdData.signal || [];
    histogram = macdData.histogram || macdData.hist || [];
  } else if (Array.isArray(macdData)) {
    // 格式3: 直接是数组
    dates = macdData.map((item) => item.date);
    dif = macdData.map((item) =>
      parseFloat(item.MACD_12_26_9 || item.dif || item.diff || 0)
    );
    dea = macdData.map((item) =>
      parseFloat(item.MACDs_12_26_9 || item.dea || item.signal || 0)
    );
    histogram = macdData.map((item) =>
      parseFloat(item.MACDh_12_26_9 || item.histogram || item.hist || 0)
    );
  }

  const option = {
    animation: true,
    backgroundColor: "transparent",
    grid: {
      left: "50px",
      right: "50px",
      top: "30px",
      bottom: "50px",
    },
    xAxis: {
      type: "category",
      data: dates,
      axisLine: { lineStyle: { color: "#666" } },
      axisLabel: { color: "#999", fontSize: 11 },
    },
    yAxis: {
      type: "value",
      axisLine: { lineStyle: { color: "#666" } },
      axisLabel: { color: "#999", fontSize: 11 },
      splitLine: { lineStyle: { color: "#333" } },
    },
    tooltip: {
      trigger: "axis",
      backgroundColor: "rgba(0, 0, 0, 0.8)",
      borderColor: "#333",
      textStyle: { color: "#fff" },
      formatter: function (params) {
        let content = `<div style="margin-bottom: 5px; font-weight: bold;">${params[0].axisValue}</div>`;
        params.forEach((param) => {
          content += `<div>${param.seriesName}: ${
            param.value?.toFixed(4) || "0.0000"
          }</div>`;
        });
        return content;
      },
    },
    legend: {
      textStyle: { color: "#999" },
      top: 5,
    },
    series: [
      {
        name: "DIF",
        type: "line",
        data: dif,
        lineStyle: { color: "#ff6b6b", width: 1 },
        showSymbol: false,
      },
      {
        name: "DEA",
        type: "line",
        data: dea,
        lineStyle: { color: "#4ecdc4", width: 1 },
        showSymbol: false,
      },
      {
        name: "HISTOGRAM",
        type: "bar",
        data: histogram,
        itemStyle: {
          color: function (params) {
            return params.value >= 0 ? "#ef4444" : "#22c55e";
          },
        },
      },
    ],
  };

  console.log("设置MACD图表配置");
  macdChart.value.setOption(option);
  console.log("MACD图表渲染完成");
};

// 渲染成交量图表
const renderVolumeChart = async () => {
  if (!volumeChartContainer.value?.chartRef || !chartData.value.volume) {
    console.warn("成交量图表渲染条件不满足:", {
      hasContainer: !!volumeChartContainer.value?.chartRef,
      hasData: !!chartData.value?.volume,
    });
    return;
  }

  console.log("开始渲染成交量图表");

  if (volumeChart.value) {
    volumeChart.value.dispose();
  }

  const chartDom = volumeChartContainer.value.chartRef;
  if (!chartDom) {
    console.error("成交量图表容器 DOM 不存在");
    return;
  }

  console.log("初始化成交量 ECharts 实例");
  volumeChart.value = echarts.init(chartDom);
  const volumeData = chartData.value.volume || [];
  console.log("成交量数据:", volumeData);

  // 提取成交量图表所需的数据
  const dates = volumeData.map((item) => item.date);
  const volumes = volumeData.map((item) => item.volume);

  if (dates.length === 0 || volumes.length === 0) {
    console.warn("成交量数据为空");
    return;
  }

  const option = {
    animation: true,
    backgroundColor: "transparent",
    grid: {
      left: "50px",
      right: "50px",
      top: "30px",
      bottom: "50px",
    },
    xAxis: {
      type: "category",
      data: dates,
      axisLine: { lineStyle: { color: "#666" } },
      axisLabel: { color: "#999", fontSize: 11 },
    },
    yAxis: {
      type: "value",
      axisLine: { lineStyle: { color: "#666" } },
      axisLabel: {
        color: "#999",
        fontSize: 11,
        formatter: function (value) {
          return formatVolume(value);
        },
      },
      splitLine: { lineStyle: { color: "#333" } },
    },
    tooltip: {
      trigger: "axis",
      backgroundColor: "rgba(0, 0, 0, 0.8)",
      borderColor: "#333",
      textStyle: { color: "#fff" },
      formatter: function (params) {
        const data = params[0];
        return `
          <div>
            <div style="margin-bottom: 5px; font-weight: bold;">${
              data.axisValue
            }</div>
            <div>成交量: ${formatVolume(data.value)}</div>
          </div>
        `;
      },
    },
    series: [
      {
        name: "成交量",
        type: "bar",
        data: volumes,
        itemStyle: {
          color: function (params) {
            // 根据涨跌情况设置颜色
            const index = params.dataIndex;
            if (index === 0) return "#4ecdc4";
            const current = volumeData[index];
            const previous = volumeData[index - 1];
            return current.close >= previous.close ? "#ef4444" : "#22c55e";
          },
        },
      },
    ],
  };

  console.log("设置成交量图表配置");
  volumeChart.value.setOption(option);
  console.log("成交量图表渲染完成");
};

// 模拟数据生成函数（备用）
const generateMockKlineData = () => {
  return [];
};

const generateMockMacdData = () => {
  return [];
};

const generateMockVolumeData = () => {
  return [];
};

// 初始化处理路由参数
onMounted(async () => {
  // 加载热门股票数据
  await loadHotStocks();

  // 检查是否从自选股页面跳转过来
  if (route.query.stock) {
    selectedStock.value = route.query.stock;
    console.log("从自选股跳转，预选股票:", route.query.stock, route.query.name);

    // 如果传入了股票代码，自动加载分析数据
    if (selectedStock.value) {
      await loadStockAnalysis();
    }
  }

  // 添加窗口大小变化监听
  window.addEventListener("resize", handleResize);
});

// 组件卸载时清理
onUnmounted(() => {
  window.removeEventListener("resize", handleResize);
  if (klineChart.value) klineChart.value.dispose();
  if (macdChart.value) macdChart.value.dispose();
  if (volumeChart.value) volumeChart.value.dispose();
});
</script>

<style scoped lang="scss">
.analysis-page {
  max-width: 1400px;
  margin: 0 auto;
}

.chart-container {
  position: relative;
  background: linear-gradient(
    135deg,
    var(--bg-tertiary) 0%,
    var(--bg-secondary) 100%
  );
  border: 1px solid var(--border-color);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;

  &:hover {
    border-color: var(--accent-primary);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
  }

  // 添加微妙的纹理效果
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: radial-gradient(
        circle at 20% 20%,
        rgba(59, 130, 246, 0.05) 0%,
        transparent 50%
      ),
      radial-gradient(
        circle at 80% 80%,
        rgba(139, 92, 246, 0.05) 0%,
        transparent 50%
      );
    pointer-events: none;
    z-index: 1;
  }

  // 确保内容在纹理上方
  > * {
    position: relative;
    z-index: 2;
  }
}

// 添加渐变边框效果
.card {
  &:hover .chart-container {
    background: linear-gradient(
      135deg,
      var(--bg-tertiary) 0%,
      rgba(59, 130, 246, 0.05) 50%,
      var(--bg-secondary) 100%
    );
  }
}

// 加载动画优化
@keyframes pulse-glow {
  0%,
  100% {
    opacity: 0.4;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 图标悬停效果
i[class*="i-carbon-"] {
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.1);
    animation: pulse-glow 2s infinite;
  }
}
</style>
