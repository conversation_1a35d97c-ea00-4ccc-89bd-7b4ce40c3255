import axios from 'axios'

// 创建 axios 实例
const apiClient = axios.create({
  baseURL: '/api', // 通过 Vite 代理到后端
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    // 在发送请求之前做些什么
    // 可以在这里添加 token
    const token = localStorage.getItem('access_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    // 添加时间戳防止缓存
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      }
    }
    
    return config
  },
  (error) => {
    // 对请求错误做些什么
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => {
    // 统一处理后端格式 {data, status, message}
    const res = response.data
    if (res && typeof res === 'object' && 'data' in res && 'status' in res && 'message' in res) {
      return res.data
    }
    return res
  },
  (error) => {
    // 对响应错误做点什么
    const { response } = error
    if (response) {
      switch (response.status) {
        case 401:
          localStorage.removeItem('access_token')
          break
        case 403:
          console.error('权限不足')
          break
        case 404:
          console.error('资源不存在')
          break
        case 500:
          console.error('服务器内部错误')
          break
        default:
          console.error(`请求失败: ${response.status}`)
      }
      return Promise.reject(new Error(response.data?.detail || response.data?.message || '请求失败'))
    } else {
      return Promise.reject(new Error('网络连接失败，请检查网络设置'))
    }
  }
)

export default apiClient
