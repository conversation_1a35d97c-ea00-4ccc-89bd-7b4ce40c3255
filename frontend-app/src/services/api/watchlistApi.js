import apiClient from '../apiClient'

/**
 * 自选股API服务
 * 提供自选股相关的API调用
 */
export const watchlistApi = {
  /**
   * 获取用户自选股列表
   * @param {boolean} includeDetails - 是否包含股票详情
   * @returns {Promise}
   */
  getWatchlist(includeDetails = true) {
    return apiClient.get('/v1/watchlist/', {
      params: {
        // user_id 将从后端通过 token 获取
        include_details: includeDetails
      }
    })
  },

  /**
   * 添加股票到自选股
   * @param {string} stockCode - 股票代码
   * @returns {Promise}
   */
  addToWatchlist(stockCode) {
    // user_id 将从后端通过 token 获取
    return apiClient.post('/v1/watchlist/', {
      stock_code: stockCode
      // user_id 会在后端从token解析，或者如果你的后端设计是创建时必须传，则需要调整
    })
  },

  /**
   * 从自选股中删除
   * @param {string} stockCode - 股票代码
   * @returns {Promise}
   */
  removeFromWatchlist(stockCode) {
    // user_id 将从后端通过 token 获取，并通过后端逻辑在删除时校验权限
    return apiClient.delete(`/v1/watchlist/${stockCode}`)
  },

  /**
   * 批量更新自选股列表 (例如，用于排序或完全替换)
   * @param {Array<string>} stockCodes - 股票代码数组，按顺序排列
   * @returns {Promise}
   */
  bulkUpdateWatchlist(stockCodes) {
    // user_id 将从后端通过 token 获取
    return apiClient.put('/v1/watchlist/', {
      stock_codes: stockCodes
      // user_id 会在后端从token解析
    })
  }
  
  // 暂时注释掉未优先实现的接口
  // /**
  //  * 批量添加股票到自选股 (如果后端支持此接口)
  //  * @param {Array<string>} stockCodes - 股票代码数组
  //  * @returns {Promise}
  //  */
  // batchAddToWatchlist(stockCodes) {
  //   return apiClient.post('/v1/watchlist/batch', { stock_codes: stockCodes });
  // },

  // /**
  //  * 批量获取自选股实时数据 (如果后端支持此接口)
  //  * @returns {Promise}
  //  */
  // getWatchlistRealtimeData() {
  //   return apiClient.get('/v1/watchlist/realtime');
  // }
}

export default watchlistApi
