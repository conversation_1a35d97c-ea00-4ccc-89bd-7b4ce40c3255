import apiClient from '../apiClient'

/**
 * 股票列表API服务
 * 提供股票列表相关的API调用
 */
export const stockListApi = {
  /**
   * 获取股票列表
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  getStockList(params = {}) {
    return apiClient.get('/v1/stocks/list/', { params })
  },

  /**
   * 获取单个股票信息
   * @param {string} stockCode - 股票代码
   * @returns {Promise}
   */
  getStockInfo(stockCode) {
    return apiClient.get(`/v1/stocks/list/${stockCode}`)
  },

  /**
   * 搜索股票
   * @param {string} query - 搜索关键词
   * @param {number} limit - 限制返回数量
   * @returns {Promise}
   */
  searchStocks(query, limit = 20) {
    return apiClient.get('/v1/stocks/list/', {
      params: {
        search: query,
        limit
      }
    })
  },

  /**
   * 补充股票数据
   * @returns {Promise}
   */
  enrichStockData() {
    return apiClient.post('/v1/stocks/list/enrich')
  }
}

export default stockListApi
