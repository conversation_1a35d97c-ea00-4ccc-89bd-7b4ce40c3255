import apiClient from '../apiClient'

/**
 * 技术指标API服务
 * 提供技术分析指标相关的API调用
 */
export const indicatorApi = {
  /**
   * 获取股票基础技术指标
   * @param {string} stockCode - 股票代码
   * @param {string} indicator - 指标名称
   * @param {Object} params - 指标参数
   * @returns {Promise}
   */
  getIndicator(stockCode, indicator, params = {}) {
    return apiClient.get(`/v1/indicators/${indicator}/${stockCode}`, { params })
  },
  
  /**
   * 获取高级技术指标
   * @param {string} stockCode - 股票代码
   * @param {string} indicator - 指标名称
   * @param {Object} params - 指标参数
   * @returns {Promise}
   */
  getAdvancedIndicator(stockCode, indicator, params = {}) {
    return apiClient.get(`/v1/indicators/advanced/${indicator}/${stockCode}`, { params })
  },
  
  /**
   * 批量获取多个股票的技术指标
   * @param {Array} stockCodes - 股票代码数组
   * @param {string} indicator - 指标名称
   * @param {Object} params - 指标参数
   * @returns {Promise}
   */
  getBatchIndicator(stockCodes, indicator, params = {}) {
    return apiClient.post(`/v1/indicators/${indicator}/batch`, {
      stock_codes: stockCodes,
      ...params
    })
  }
}

export default indicatorApi
