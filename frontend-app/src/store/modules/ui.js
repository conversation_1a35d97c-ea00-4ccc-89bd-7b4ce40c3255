import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useUiStore = defineStore('ui', () => {
  // 状态
  const sidebarCollapsed = ref(false)
  const loading = ref(false)
  const activeTab = ref('dashboard')
  const selectedStocks = ref([])
  
  // 弹窗状态
  const modals = ref({
    stockDetail: false,
    settings: false,
    addStock: false
  })
  
  // 通知状态
  const notifications = ref([])
  
  // 动作
  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value
  }
  
  const setSidebarCollapsed = (collapsed) => {
    sidebarCollapsed.value = collapsed
  }
  
  const setLoading = (isLoading) => {
    loading.value = isLoading
  }
  
  const setActiveTab = (tab) => {
    activeTab.value = tab
  }
  
  const openModal = (modalName) => {
    if (modals.value.hasOwnProperty(modalName)) {
      modals.value[modalName] = true
    }
  }
  
  const closeModal = (modalName) => {
    if (modals.value.hasOwnProperty(modalName)) {
      modals.value[modalName] = false
    }
  }
  
  const closeAllModals = () => {
    Object.keys(modals.value).forEach(key => {
      modals.value[key] = false
    })
  }
  
  const addNotification = (notification) => {
    const id = Date.now().toString()
    notifications.value.push({
      id,
      type: 'info',
      duration: 3000,
      ...notification
    })
    
    // 自动移除通知
    if (notification.duration !== 0) {
      setTimeout(() => {
        removeNotification(id)
      }, notification.duration || 3000)
    }
  }
  
  const removeNotification = (id) => {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index > -1) {
      notifications.value.splice(index, 1)
    }
  }
  
  const clearNotifications = () => {
    notifications.value = []
  }
  
  const toggleStockSelection = (stockCode) => {
    const index = selectedStocks.value.indexOf(stockCode)
    if (index > -1) {
      selectedStocks.value.splice(index, 1)
    } else {
      selectedStocks.value.push(stockCode)
    }
  }
  
  const clearStockSelection = () => {
    selectedStocks.value = []
  }
  
  const selectAllStocks = (stocks) => {
    selectedStocks.value = [...stocks]
  }
  
  return {
    // 状态
    sidebarCollapsed,
    loading,
    activeTab,
    selectedStocks,
    modals,
    notifications,
    
    // 动作
    toggleSidebar,
    setSidebarCollapsed,
    setLoading,
    setActiveTab,
    openModal,
    closeModal,
    closeAllModals,
    addNotification,
    removeNotification,
    clearNotifications,
    toggleStockSelection,
    clearStockSelection,
    selectAllStocks
  }
})
