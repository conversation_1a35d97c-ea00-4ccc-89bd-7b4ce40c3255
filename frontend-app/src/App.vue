<template>
  <div id="app" :class="themeStore.themeClass">
    <!-- 主布局容器 -->
    <div class="min-h-screen flex bg-bg-primary">
      <!-- 侧边栏 -->
      <Sidebar v-if="!uiStore.sidebarCollapsed || isDesktop" />
      
      <!-- 主内容区域 -->
      <div class="flex-1 flex flex-col">
        <!-- 顶部导航栏 -->
        <Navbar />
        
        <!-- 页面内容 -->
        <main class="flex-1 p-6 overflow-auto bg-bg-primary">
          <router-view v-slot="{ Component, route }">
            <transition name="fade" mode="out-in">
              <component :is="Component" :key="route.path" />
            </transition>
          </router-view>
        </main>
      </div>
    </div>
    
    <!-- 全局通知组件 -->
    <NotificationContainer />
    
    <!-- 全局加载遮罩 -->
    <LoadingOverlay v-if="uiStore.loading" />
  </div>
</template>

<script setup>
import { onMounted, computed } from 'vue'
import { useBreakpoints, breakpointsTailwind } from '@vueuse/core'
import { useThemeStore, useUiStore } from '@/store'
import Sidebar from '@/components/layout/Sidebar.vue'
import Navbar from '@/components/layout/Navbar.vue'
import NotificationContainer from '@/components/common/NotificationContainer.vue'
import LoadingOverlay from '@/components/common/LoadingOverlay.vue'

const themeStore = useThemeStore()
const uiStore = useUiStore()

// 响应式断点
const breakpoints = useBreakpoints(breakpointsTailwind)
const isDesktop = computed(() => breakpoints.greater('md').value)

// 组件挂载时初始化
onMounted(() => {
  // 初始化主题
  themeStore.initTheme()
  
  // 加载收藏列表
  // stockDataStore.loadWatchlist()
  
  // 根据屏幕大小设置侧边栏初始状态
  if (!isDesktop.value) {
    uiStore.setSidebarCollapsed(true)
  }
})
</script>

<style scoped>
/* 路由切换动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
