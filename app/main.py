"""
应用主入口

提供应用启动和关闭钩子，配置中间件和路由。
"""

from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import time
import uvicorn
from app.core import logging
from app.core.config import settings, SettingsManager
from app.core.logging import setup_logging
from app.core.database import engine
from app.core.scheduler import scheduler
from app.models import *  # 导入所有模型以便创建表
from app.core.json_utils import CustomJSONResponse, install_custom_encoder
import json

from sqlalchemy.ext.declarative import declarative_base

# 重新读取配置
SettingsManager.reload()

# 设置日志
setup_logging()
logger = logging.getLogger(__name__)

# 安装自定义JSON编码器
original_jsonable_encoder = install_custom_encoder()

# 扫描并注册定时器任务
scheduler.auto_discover_and_register_tasks(["app.services"])
scheduler.start()

# 定义一个动态生成带时间戳的OpenAPI URL的函数
def get_openapi_url():
    return f"/api/openapi.json?t={int(time.time())}"

# 创建FastAPI应用，配置默认响应类为自定义JSONResponse
app = FastAPI(
    title=settings.APP_NAME,
    description="股票量化分析系统API",
    version=settings.APP_VERSION,
    docs_url="/api/docs" if settings.DEBUG else None,
    redoc_url="/api/redoc" if settings.DEBUG else None,
    default_response_class=CustomJSONResponse,  # 使用自定义响应类
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 请求处理时间中间件
@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response

# 防止文档缓存的中间件
@app.middleware("http")
async def no_cache_openapi_docs(request: Request, call_next):
    response = await call_next(request)
    if request.url.path in ["/api/docs", "/api/redoc", "/openapi.json"] or request.url.path.startswith("/api/docs/"):
        response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
        response.headers["Pragma"] = "no-cache"
        response.headers["Expires"] = "0"
    return response

# 统一API响应格式中间件
# @app.middleware("http")
# async def wrap_api_response(request: Request, call_next):
#     # 跳过非API路径
#     if not request.url.path.startswith("/api/"):
#         return await call_next(request)
#     
#     try:
#         response = await call_next(request)
#     except Exception as e:
#         # 异常情况下也要包装响应
#         logger.exception(f"API请求异常: {e}")
#         wrapped = {
#             "data": None,
#             "status": 500,
#             "message": "服务器内部错误",
#             "success": False,
#             "error": str(e)
#         }
#         from starlette.responses import JSONResponse
#         return JSONResponse(wrapped, status_code=500)
#     
#     # 只处理JSON响应且状态码2xx
#     if (response.headers.get("content-type", "").startswith("application/json") 
#         and 200 <= response.status_code < 300):
#         
#         body = b""
#         async for chunk in response.body_iterator:
#             body += chunk
#         
#         try:
#             data = json.loads(body)
#         except Exception:
#             return response  # 不是有效JSON，直接返回
#         
#         # 使用jsonable_encoder安全序列化
#         from fastapi.encoders import jsonable_encoder
#         try:
#             encoded_data = jsonable_encoder(data)
#         except Exception as e:
#             logger.error(f"序列化失败: {e}")
#             # 序列化失败，返回错误响应
#             wrapped = {
#                 "data": None,
#                 "status": 500,
#                 "message": "数据序列化失败",
#                 "success": False,
#                 "error": str(e)
#             }
#             from starlette.responses import JSONResponse
#             return JSONResponse(wrapped, status_code=500)
#         
#         # 检查是否已经是CommonResponse格式
#         if isinstance(encoded_data, dict) and all(k in encoded_data for k in ["success", "data"]):
#             # 已经是CommonResponse格式，补全status字段
#             wrapped = {
#                 "data": encoded_data.get("data"),
#                 "status": response.status_code,
#                 "message": encoded_data.get("message", ""),
#                 "success": encoded_data.get("success", True),
#                 "error": encoded_data.get("error", None)
#             }
#         else:
#             # 不是CommonResponse格式，进行包装
#             wrapped = {
#                 "data": encoded_data,
#                 "status": response.status_code,
#                 "message": "操作成功",
#                 "success": True,
#                 "error": None
#             }
#         
#         from starlette.responses import Response
#         return Response(
#             json.dumps(wrapped, ensure_ascii=False), 
#             status_code=response.status_code, 
#             headers=dict(response.headers), 
#             media_type="application/json"
#         )
#     
#     return response

# 全局异常处理
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    logger.exception(f"全局异常: {exc}")
    return JSONResponse(
        status_code=500,
        content={"detail": "服务器内部错误", "message": str(exc)},
    )

# 应用启动时执行
@app.on_event("startup")
async def startup_event():
    logger.info("应用启动中...")
    # 创建异步数据库表（如果不存在）
    from app.core.database import Base
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    logger.info("异步数据库表创建完成")
    
    # 创建同步数据库表（如果不存在）
    from app.core.sync_database import sync_engine
    from app.models.base import BaseModel
    BaseModel.metadata.create_all(bind=sync_engine)
    logger.info("同步数据库表创建完成")

    # 注册分区模型
    from app.models.partition import PartitionManager
    from app.models.stock import StockDailyBase
    PartitionManager.register_partition("StockDailyBase", StockDailyBase)
    logger.info("分区模型注册完成")

    # TODO: 添加任务调度器
    # TODO: 初始化缓存
    
    logger.info(f"{settings.APP_NAME} {settings.APP_VERSION} 启动成功!")

# 应用关闭时执行
@app.on_event("shutdown")
async def shutdown_event():
    logger.info("应用关闭中...")
    # TODO: 关闭调度器
    # TODO: 关闭缓存连接
    logger.info("应用已成功关闭!")

# 根路由
@app.get("/")
async def root():
    return {"message": f"欢迎使用{settings.APP_NAME} API"}

# 健康检查
@app.get("/health")
async def health_check():
    return {"status": "healthy", "version": settings.APP_VERSION}

@app.get("/reload-config")
async def reload_config():
    """重新加载配置
    
    当.env文件发生变更时,可以调用此接口重新加载配置,无需重启服务
    """
    new_settings = SettingsManager.reload()
    return {
        "message": "配置已重新加载",
        "config": new_settings.model_dump(exclude_none=True)
    }

# 导入并注册API路由
from app.api import api_router
app.include_router(api_router, prefix=settings.API_PREFIX) # Use setting for prefix

# 直接运行文件时启动服务器
if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host=settings.API_HOST,
        port=int(settings.API_PORT),
        reload=settings.DEBUG
    )
