from sqlalchemy import Column, String, Integer, DateTime, ForeignKey, UniqueConstraint
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from app.models.base import BaseModel as Base

class WatchlistItem(Base):
    __tablename__ = "watchlist_items"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.id"), index=True, nullable=False, comment="用户ID")
    stock_code = Column(String(10), index=True, nullable=False, comment="股票代码")
    added_at = Column(DateTime, default=func.now(), comment="添加时间")
    sort_order = Column(Integer, default=0, comment="排序顺序")
    # price_alert 字段在 markdown 中提及，但具体实现可能复杂，暂时注释，后续可添加
    # price_alert = Column(String(255), nullable=True, comment="价格提醒设置(JSON)") 
    
    # 关系: 多个自选股项目属于一个用户
    user = relationship("User", back_populates="watchlist_items")

    __table_args__ = (
        UniqueConstraint('user_id', 'stock_code', name='uix_user_stock'),
    )

    def __repr__(self):
        return f"<WatchlistItem(user_id='{self.user_id}', stock_code='{self.stock_code}')>"
