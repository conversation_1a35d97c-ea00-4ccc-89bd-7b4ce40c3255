"""
高级技术指标计算服务

基于 new_feature_formatted.md 文档实现的新功能：
1. 多周期数据选择
2. Bollinger Bands (布林带) 计算
3. 成交量内外盘差异计算
4. 基于成交量的Bollinger指标
5. 买卖点信号计算
"""
import pandas as pd
import numpy as np
import pandas_ta as ta
from typing import List, Dict, Any, Optional, Union, Tuple
from datetime import datetime, timedelta
from enum import Enum

from app.services.storage.stock_storage import StockStorageService
from app.core.exceptions import ValidationException
from app.core.logging import logging
from app.utils.data_fetcher import StockDataFetcher

logger = logging.getLogger(__name__)


class PeriodType(str, Enum):
    """数据周期类型"""
    DAILY = "dh"      # 日线
    WEEKLY = "wh"     # 周线  
    MONTHLY = "mh"    # 月线
    MIN_15 = "15"     # 15分钟
    MIN_30 = "30"     # 30分钟
    MIN_60 = "60"     # 60分钟


class AdvancedIndicatorService:
    """高级技术指标计算服务"""
    
    def __init__(self, storage_service: StockStorageService):
        """初始化高级指标计算服务
        
        Args:
            storage_service: 股票数据存储服务
        """
        self.storage = storage_service
        self.data_fetcher = StockDataFetcher(storage_service)
    
    def select_period(self, period_index: int) -> str:
        """选择数据周期
        
        Args:
            period_index: 周期索引 (1-6)
            
        Returns:
            str: 周期标识符
        """
        period_mapping = {
            1: PeriodType.DAILY,
            2: PeriodType.WEEKLY,
            3: PeriodType.MONTHLY,
            4: PeriodType.MIN_15,
            5: PeriodType.MIN_30,
            6: PeriodType.MIN_60
        }
        
        if period_index not in period_mapping:
            raise ValidationException(f"无效的周期索引: {period_index}, 支持的范围: 1-6")
        
        return period_mapping[period_index]
    
    async def get_stock_data(
        self,
        stock_code: str,
        start_date: Union[str, datetime],
        end_date: Union[str, datetime],
        freq: str = 'D'
    ) -> pd.DataFrame:
        """获取并预处理股票数据
        
        Args:
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            freq: 数据频率
              Returns:
            pd.DataFrame: 包含OHLCV数据的DataFrame
        """
        try:
            # 获取股票数据
            df = await self.data_fetcher.fetch_stock_data(
                stock_code=stock_code,
                start_date=start_date,
                end_date=end_date
            )
            
            # 如果需要频率转换，使用 resample_kline 方法
            if freq != 'D' and not df.empty:
                # 将我们的频率格式转换为 resample_kline 支持的格式
                freq_mapping = {
                    PeriodType.DAILY: 'D',
                    PeriodType.WEEKLY: 'W', 
                    PeriodType.MONTHLY: 'M',
                    # 分钟级数据暂时不支持重采样，需要更复杂的处理
                    PeriodType.MIN_15: 'D',  # 暂时返回日线数据
                    PeriodType.MIN_30: 'D',  # 暂时返回日线数据
                    PeriodType.MIN_60: 'D'   # 暂时返回日线数据
                }
                
                resample_freq = freq_mapping.get(freq, 'D')
                if resample_freq != 'D':
                    df = self.data_fetcher.resample_kline(df, resample_freq)
            
            if df is None or df.empty:
                raise ValidationException(f"无法获取股票 {stock_code} 的数据")
            
            # 确保必要的列存在
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                raise ValidationException(f"数据缺少必要列: {missing_columns}")
            
            # 数据清洗
            df = df.dropna()
            
            # 确保数据按日期排序
            if 'date' in df.columns:
                df = df.sort_values('date')
            elif df.index.name == 'date' or isinstance(df.index, pd.DatetimeIndex):
                df = df.sort_index()
            
            return df
            
        except Exception as e:
            logger.error(f"获取股票数据失败: {e}")
            raise ValidationException(f"获取股票数据失败: {str(e)}")
    
    def calculate_bollinger_bands(
        self,
        df: pd.DataFrame,
        window: int = 20,
        std_dev: float = 2.0,
        price_column: str = 'close'
    ) -> Dict[str, pd.Series]:
        """计算Bollinger Bands (布林带)
        
        Args:
            df: 包含价格数据的DataFrame
            window: 移动平均窗口大小
            std_dev: 标准差倍数
            price_column: 用于计算的价格列
            
        Returns:
            Dict[str, pd.Series]: 包含中线、上轨、下轨的字典
        """
        try:
            price_series = df[price_column]
            
            # 计算移动平均线（中线）
            middle_line = price_series.rolling(window=window).mean()
            
            # 计算标准差
            rolling_std = price_series.rolling(window=window).std()
            
            # 计算上轨和下轨
            upper_band = middle_line + (rolling_std * std_dev)
            lower_band = middle_line - (rolling_std * std_dev)
            
            return {
                'middle': middle_line,
                'upper': upper_band,
                'lower': lower_band,
                'bandwidth': (upper_band - lower_band) / middle_line,
                'percent_b': (price_series - lower_band) / (upper_band - lower_band)
            }
            
        except Exception as e:
            logger.error(f"计算Bollinger Bands失败: {e}")
            raise ValidationException(f"计算Bollinger Bands失败: {str(e)}")
    
    def calculate_volume_inout_difference(self, df: pd.DataFrame) -> Dict[str, pd.Series]:
        """计算成交量内外盘差异
        
        基于价格变动和成交量估算内外盘
        
        Args:
            df: 包含OHLCV数据的DataFrame
            
        Returns:
            Dict[str, pd.Series]: 包含内盘、外盘、差异的字典
        """
        try:
            # 计算价格变化率
            close_change = df['close'].pct_change()
            
            # 计算成交量加权平均价格 (VWAP)
            typical_price = (df['high'] + df['low'] + df['close']) / 3
            vwap = (typical_price * df['volume']).rolling(window=20).sum() / df['volume'].rolling(window=20).sum()
            
            # 基于价格相对于VWAP的位置估算内外盘
            price_ratio = df['close'] / vwap
            
            # 外盘估算：价格上涨时的成交量
            out_volume = df['volume'] * np.where(close_change > 0, 1, 0) * price_ratio
            
            # 内盘估算：价格下跌时的成交量
            in_volume = df['volume'] * np.where(close_change < 0, 1, 0) * (2 - price_ratio)
            
            # 计算差异
            volume_diff = out_volume - in_volume
            
            return {
                'in_volume': in_volume,
                'out_volume': out_volume,
                'volume_diff': volume_diff,
                'volume_ratio': out_volume / (in_volume + 1e-8)  # 避免除零
            }
            
        except Exception as e:
            logger.error(f"计算成交量内外盘差异失败: {e}")
            raise ValidationException(f"计算成交量内外盘差异失败: {str(e)}")
    
    def calculate_volume_bollinger(
        self,
        diff_vol: pd.Series,
        window: int = 20,
        lag: int = 6
    ) -> Dict[str, pd.Series]:
        """计算基于成交量的Bollinger指标
        
        Args:
            diff_vol: 成交量差异序列
            window: Bollinger窗口大小
            lag: 移动平均滞后期
            
        Returns:
            Dict[str, pd.Series]: 包含成交量Bollinger指标的字典
        """
        try:
            # 计算移动平均
            ma = diff_vol.rolling(window=window).mean()
            
            # 计算滞后移动平均
            ma_lag = ma.shift(lag)
            
            # 计算标准差
            std = diff_vol.rolling(window=window).std()
            
            # 计算上下轨
            upper = ma + 2 * std
            lower = ma - 2 * std
            
            # 计算指标值
            volume_bollinger = (diff_vol - ma_lag) / (std + 1e-8)
            
            return {
                'volume_bollinger': volume_bollinger,
                'volume_ma': ma,
                'volume_ma_lag': ma_lag,
                'volume_upper': upper,
                'volume_lower': lower,
                'volume_std': std
            }
            
        except Exception as e:
            logger.error(f"计算成交量Bollinger指标失败: {e}")
            raise ValidationException(f"计算成交量Bollinger指标失败: {str(e)}")
    
    def calculate_kdj(
        self,
        df: pd.DataFrame,
        k_period: int = 9,
        d_period: int = 3,
        j_period: int = 3
    ) -> Dict[str, pd.Series]:
        """计算KDJ指标
        
        Args:
            df: 包含OHLC数据的DataFrame
            k_period: K值计算周期
            d_period: D值平滑周期
            j_period: J值计算周期
            
        Returns:
            Dict[str, pd.Series]: 包含K、D、J值的字典
        """
        try:
            # 计算最高价和最低价的滚动窗口
            low_min = df['low'].rolling(window=k_period).min()
            high_max = df['high'].rolling(window=k_period).max()
            
            # 计算RSV (Raw Stochastic Value)
            rsv = (df['close'] - low_min) / (high_max - low_min + 1e-8) * 100
            
            # 计算K值 (使用指数移动平均)
            k_values = rsv.ewm(span=d_period).mean()
            
            # 计算D值 (K值的指数移动平均)
            d_values = k_values.ewm(span=j_period).mean()
            
            # 计算J值
            j_values = 3 * k_values - 2 * d_values
            
            return {
                'K': k_values,
                'D': d_values,
                'J': j_values,
                'RSV': rsv
            }
            
        except Exception as e:
            logger.error(f"计算KDJ指标失败: {e}")
            raise ValidationException(f"计算KDJ指标失败: {str(e)}")
    
    def calculate_buy_sell_signals(
        self,
        df: pd.DataFrame,
        volume_indicators: Dict[str, pd.Series],
        kdj_indicators: Dict[str, pd.Series]
    ) -> Dict[str, List[int]]:
        """计算买卖点信号
        
        基于成交量突破和KDJ金叉死叉
        
        Args:
            df: 原始数据DataFrame
            volume_indicators: 成交量指标字典
            kdj_indicators: KDJ指标字典
            
        Returns:
            Dict[str, List[int]]: 包含买入和卖出信号点位的字典
        """
        try:
            buy_signals = []
            sell_signals = []
            
            k_values = kdj_indicators['K']
            d_values = kdj_indicators['D']
            volume_bollinger = volume_indicators['volume_bollinger']
            
            for i in range(1, len(df)):
                # KDJ金叉买入信号
                if (k_values.iloc[i] > d_values.iloc[i] and 
                    k_values.iloc[i-1] <= d_values.iloc[i-1] and
                    k_values.iloc[i] < 80):  # 避免高位买入
                    
                    # 成交量确认
                    if volume_bollinger.iloc[i] > 0.5:
                        buy_signals.append(i)
                
                # KDJ死叉卖出信号
                if (k_values.iloc[i] < d_values.iloc[i] and 
                    k_values.iloc[i-1] >= d_values.iloc[i-1] and
                    k_values.iloc[i] > 20):  # 避免低位卖出
                    
                    # 成交量确认
                    if volume_bollinger.iloc[i] < -0.5:
                        sell_signals.append(i)
                
                # 额外的成交量突破信号
                if volume_bollinger.iloc[i] > 2.0 and df['close'].iloc[i] > df['close'].iloc[i-1]:
                    buy_signals.append(i)
                
                if volume_bollinger.iloc[i] < -2.0 and df['close'].iloc[i] < df['close'].iloc[i-1]:
                    sell_signals.append(i)
            
            return {
                'buy_signals': sorted(list(set(buy_signals))),
                'sell_signals': sorted(list(set(sell_signals)))
            }
            
        except Exception as e:
            logger.error(f"计算买卖点信号失败: {e}")
            raise ValidationException(f"计算买卖点信号失败: {str(e)}")
    
    async def calculate_complete_indicators(
        self,
        stock_code: str,
        start_date: Union[str, datetime] = None,
        end_date: Union[str, datetime] = None,
        period_index: int = 1,
        bollinger_window: int = 20,
        lag: int = 6
    ) -> Dict[str, Any]:
        """计算完整的技术指标组合
        
        这是主要的接口方法，整合了所有新功能
        
        Args:
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            period_index: 周期索引 (1-6)
            bollinger_window: Bollinger窗口大小
            lag: 移动平均滞后期
            
        Returns:
            Dict[str, Any]: 包含所有计算结果的字典
        """
        try:
            # 设置默认日期范围
            if end_date is None:
                end_date = datetime.now()
            if start_date is None:
                start_date = end_date - timedelta(days=365)
            
            # 选择数据周期
            freq = self.select_period(period_index)
            
            # 获取股票数据
            df = await self.get_stock_data(stock_code, start_date, end_date, freq)
            
            # 计算Bollinger Bands
            bollinger_bands = self.calculate_bollinger_bands(df, window=bollinger_window)
            
            # 计算成交量内外盘差异
            volume_analysis = self.calculate_volume_inout_difference(df)
            
            # 计算基于成交量的Bollinger指标
            volume_bollinger = self.calculate_volume_bollinger(
                volume_analysis['volume_diff'],
                window=bollinger_window,
                lag=lag
            )
            
            # 计算KDJ指标
            kdj_indicators = self.calculate_kdj(df)
            
            # 计算买卖点信号
            trading_signals = self.calculate_buy_sell_signals(df, volume_bollinger, kdj_indicators)
              # 整合所有结果，符合API响应模型格式
            result = {
                'period_type': str(freq),
                'data_points': len(df),
                'date_range': {
                    'start': str(start_date),
                    'end': str(end_date)
                },
                'price_bollinger': {
                    'middle': bollinger_bands['middle'].tolist(),
                    'upper': bollinger_bands['upper'].tolist(),
                    'lower': bollinger_bands['lower'].tolist()
                },
                'volume_analysis': {
                    'in_volume': volume_analysis['in_volume'].tolist(),
                    'ex_volume': volume_analysis['out_volume'].tolist(),
                    'volume_difference': volume_analysis['volume_diff'].tolist()
                },
                'volume_bollinger': {
                    'middle': volume_bollinger['volume_ma'].tolist(),
                    'upper': volume_bollinger['volume_upper'].tolist(),
                    'lower': volume_bollinger['volume_lower'].tolist(),
                    'smoothed_volume': volume_bollinger['volume_bollinger'].tolist()
                },
                'kdj_indicators': {
                    'K': kdj_indicators['K'].tolist(),
                    'D': kdj_indicators['D'].tolist(),
                    'J': kdj_indicators['J'].tolist()
                },
                'trading_signals': trading_signals,
                'parameters': {
                    'bollinger_window': bollinger_window,
                    'lag': lag,
                    'period_index': period_index
                }
            }
            
            logger.info(f"成功计算股票 {stock_code} 的完整技术指标")
            return result
            
        except Exception as e:
            logger.error(f"计算完整技术指标失败: {e}")
            raise ValidationException(f"计算完整技术指标失败: {str(e)}")
