"""
高级技术指标计算服务

基于 new_feature_formatted.md 文档实现的新功能：
1. 多周期数据选择
2. Bollinger Bands (布林带) 计算
3. 成交量内外盘差异计算
4. 基于成交量的Bollinger指标
5. 买卖点信号计算
"""
import numpy as np
import pandas as pd
from typing import Dict, List, Union, Any
from datetime import datetime, timedelta
import logging

# Fixed imports based on actual project structure
from app.utils.data_fetcher import StockDataFetcher
from app.services.storage.stock_storage import StockStorageService
from app.core.exceptions import ValidationException
from app.services.indicators.indicator_service import IndicatorService

logger = logging.getLogger(__name__)

# Define PeriodType enum locally since it doesn't exist in the project
class PeriodType:
    DAILY = "daily"
    WEEKLY = "weekly" 
    MONTHLY = "monthly"
    MIN_15 = "15min"
    MIN_30 = "30min"
    MIN_60 = "60min"

class AdvancedIndicatorService:
    """高级技术指标计算服务"""

    def __init__(self, storage_service: StockStorageService, indicator_service: IndicatorService = None):
        self.data_fetcher = StockDataFetcher(storage_service)
        self.indicator_service = indicator_service

    def _calculate_bollinger_on_series(self, series: pd.Series, window: int = 20, nbdev: float = 2.0) -> Dict[str, pd.Series]:
        """在给定的 pandas Series 上计算布林带。"""
        series = series.astype(float)
        if series.empty or len(series) < window:
            nan_series = pd.Series(np.nan, index=series.index, dtype=float)
            return {'upper': nan_series, 'middle': nan_series, 'lower': nan_series}
        
        middle = series.rolling(window=window, min_periods=window).mean()
        std_dev = series.rolling(window=window, min_periods=window).std()
        upper = middle + nbdev * std_dev
        lower = middle - nbdev * std_dev
        return {'upper': upper, 'middle': middle, 'lower': lower}

    def _calculate_vpkm_signals(
        self,
        df: pd.DataFrame,
        log_vol: pd.Series,
        average_vol: pd.Series,
        kdj_k: pd.Series,
        kdj_d: pd.Series,
        price_bollinger_lower: pd.Series,
        c_price: pd.Series
    ) -> Dict[str, List[int]]:
        """根据成交量压力与动量双驱策略计算信号。"""
        buy_indices_iloc = []
        sell_tp_indices_iloc = []
        sell_sl_indices_iloc = []

        # Ensure all series have the same index as df to allow proper alignment for shift and conditions
        # This is crucial if any series were generated with a different length or index.
        # For safety, reindex if necessary, though ideally they are already aligned.
        # log_vol = log_vol.reindex(df.index)
        # average_vol = average_vol.reindex(df.index)
        # kdj_k = kdj_k.reindex(df.index)
        # kdj_d = kdj_d.reindex(df.index)
        # price_bollinger_lower = price_bollinger_lower.reindex(df.index)
        # c_price = c_price.reindex(df.index)

        if not all(s.empty for s in [log_vol, average_vol, kdj_k, kdj_d, price_bollinger_lower, c_price]):
            # 买入信号
            # Ensure comparison series are aligned with the primary series (log_vol, kdj_k)
            vol_pressure_breakout = (log_vol.shift(1) < average_vol.shift(1)) & (log_vol > average_vol)
            kdj_golden_cross = (kdj_k.shift(1) < kdj_d.shift(1)) & (kdj_k > kdj_d)
            combined_buy_condition = vol_pressure_breakout & kdj_golden_cross
            # Use .to_numpy().nonzero()[0] for faster index retrieval if df.index is default RangeIndex
            buy_indices_iloc = df.index[combined_buy_condition.fillna(False)].tolist()

            # 止盈信号
            vol_pressure_exhaustion = (log_vol.shift(1) > average_vol.shift(1)) & (log_vol < average_vol)
            kdj_death_cross = (kdj_k.shift(1) > kdj_d.shift(1)) & (kdj_k < kdj_d)
            combined_take_profit_condition = vol_pressure_exhaustion | kdj_death_cross
            sell_tp_indices_iloc = df.index[combined_take_profit_condition.fillna(False)].tolist()

            # 止损信号
            stop_loss_condition = c_price < price_bollinger_lower
            sell_sl_indices_iloc = df.index[stop_loss_condition.fillna(False)].tolist()
        
        # Convert actual index values to integer positions (iloc)
        # This is what the original calculate_buy_sell_signals seemed to return.
        # If actual date/time indices are preferred, this conversion is not needed.
        # Assuming the output needs to be integer indices relative to the df passed to this function.
        final_buy_indices = [df.index.get_loc(i) for i in buy_indices_iloc if i in df.index]
        final_sell_tp_indices = [df.index.get_loc(i) for i in sell_tp_indices_iloc if i in df.index]
        final_sell_sl_indices = [df.index.get_loc(i) for i in sell_sl_indices_iloc if i in df.index]

        return {
            "buy_indices_VPKM": final_buy_indices,
            "sell_indices_VPKM_tp": final_sell_tp_indices,
            "sell_indices_VPKM_sl": final_sell_sl_indices,
        }

    def select_period(self, period_index: int) -> str:
        """选择数据周期
        
        Args:
            period_index: 周期索引 (1-6)
            
        Returns:
            str: 周期标识符
        """
        period_mapping = {
            1: PeriodType.DAILY,
            2: PeriodType.WEEKLY,
            3: PeriodType.MONTHLY,
            4: PeriodType.MIN_15,
            5: PeriodType.MIN_30,
            6: PeriodType.MIN_60
        }
        
        if period_index not in period_mapping:
            raise ValidationException(f"无效的周期索引: {period_index}, 支持的范围: 1-6")
        
        return period_mapping[period_index]
    
    async def get_stock_data(
        self,
        stock_code: str,
        start_date: Union[str, datetime],
        end_date: Union[str, datetime],
        freq: str = 'D'
    ) -> pd.DataFrame:
        """获取并预处理股票数据
        
        Args:
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            freq: 数据频率
              Returns:
            pd.DataFrame: 包含OHLCV数据的DataFrame
        """
        try:
            # 获取股票数据
            df = await self.data_fetcher.fetch_stock_data(
                stock_code=stock_code,
                start_date=start_date,
                end_date=end_date
            )
            
            # 如果需要频率转换，使用 resample_kline 方法
            if freq != 'D' and not df.empty:
                # 将我们的频率格式转换为 resample_kline 支持的格式
                freq_mapping = {
                    PeriodType.DAILY: 'D',
                    PeriodType.WEEKLY: 'W', 
                    PeriodType.MONTHLY: 'M',
                    # 分钟级数据暂时不支持重采样，需要更复杂的处理
                    PeriodType.MIN_15: 'D',  # 暂时返回日线数据
                    PeriodType.MIN_30: 'D',  # 暂时返回日线数据
                    PeriodType.MIN_60: 'D'   # 暂时返回日线数据
                }
                
                resample_freq = freq_mapping.get(freq, 'D')
                if resample_freq != 'D':
                    df = self.data_fetcher.resample_kline(df, resample_freq)
            
            if df is None or df.empty:
                raise ValidationException(f"无法获取股票 {stock_code} 的数据")
            
            # 确保必要的列存在
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                raise ValidationException(f"数据缺少必要列: {missing_columns}")
            
            # 数据清洗
            df = df.dropna()
            
            # 确保数据按日期排序
            if 'date' in df.columns:
                df = df.sort_values('date')
            elif df.index.name == 'date' or isinstance(df.index, pd.DatetimeIndex):
                df = df.sort_index()
            
            return df
            
        except Exception as e:
            logger.error(f"获取股票数据失败: {e}")
            raise ValidationException(f"获取股票数据失败: {str(e)}")
    
    def calculate_bollinger_bands(
        self,
        df: pd.DataFrame,
        window: int = 20,
        std_dev: float = 2.0,
        price_column: str = 'close'
    ) -> Dict[str, pd.Series]:
        """计算Bollinger Bands (布林带)
        
        Args:
            df: 包含价格数据的DataFrame
            window: 移动平均窗口大小
            std_dev: 标准差倍数
            price_column: 用于计算的价格列
            
        Returns:
            Dict[str, pd.Series]: 包含中线、上轨、下轨的字典
        """
        try:
            price_series = df[price_column]
            
            # 计算移动平均线（中线）
            middle_line = price_series.rolling(window=window).mean()
            
            # 计算标准差
            rolling_std = price_series.rolling(window=window).std()
            
            # 计算上轨和下轨
            upper_band = middle_line + (rolling_std * std_dev)
            lower_band = middle_line - (rolling_std * std_dev)
            
            return {
                'middle': middle_line,
                'upper': upper_band,
                'lower': lower_band,
                'bandwidth': (upper_band - lower_band) / middle_line,
                'percent_b': (price_series - lower_band) / (upper_band - lower_band)
            }
            
        except Exception as e:
            logger.error(f"计算Bollinger Bands失败: {e}")
            raise ValidationException(f"计算Bollinger Bands失败: {str(e)}")
    
    def calculate_volume_inout_difference(self, df: pd.DataFrame) -> Dict[str, pd.Series]:
        """计算成交量内外盘差异
        
        基于价格变动和成交量估算内外盘
        
        Args:
            df: 包含OHLCV数据的DataFrame
            
        Returns:
            Dict[str, pd.Series]: 包含内盘、外盘、差异的字典
        """
        try:
            # 计算价格变化率
            close_change = df['close'].pct_change()
            
            # 计算成交量加权平均价格 (VWAP)
            typical_price = (df['high'] + df['low'] + df['close']) / 3
            vwap = (typical_price * df['volume']).rolling(window=20).sum() / df['volume'].rolling(window=20).sum()
            
            # 基于价格相对于VWAP的位置估算内外盘
            price_ratio = df['close'] / vwap
            
            # 外盘估算：价格上涨时的成交量
            out_volume = df['volume'] * np.where(close_change > 0, 1, 0) * price_ratio
            
            # 内盘估算：价格下跌时的成交量
            in_volume = df['volume'] * np.where(close_change < 0, 1, 0) * (2 - price_ratio)
            
            # 计算差异
            volume_diff = out_volume - in_volume
            
            return {
                'in_volume': in_volume,
                'out_volume': out_volume,
                'volume_diff': volume_diff,
                'volume_ratio': out_volume / (in_volume + 1e-8)  # 避免除零
            }
            
        except Exception as e:
            logger.error(f"计算成交量内外盘差异失败: {e}")
            raise ValidationException(f"计算成交量内外盘差异失败: {str(e)}")
    
    def calculate_volume_bollinger(
        self,
        diff_vol: pd.Series,
        window: int = 20,
        lag: int = 6
    ) -> Dict[str, pd.Series]:
        """计算基于成交量的Bollinger指标
        
        Args:
            diff_vol: 成交量差异序列
            window: Bollinger窗口大小
            lag: 移动平均滞后期
            
        Returns:
            Dict[str, pd.Series]: 包含成交量Bollinger指标的字典
        """
        try:
            # 计算移动平均
            ma = diff_vol.rolling(window=window).mean()
            
            # 计算滞后移动平均
            ma_lag = ma.shift(lag)
            
            # 计算标准差
            std = diff_vol.rolling(window=window).std()
            
            # 计算上下轨
            upper = ma + 2 * std
            lower = ma - 2 * std
            
            # 计算指标值
            volume_bollinger = (diff_vol - ma_lag) / (std + 1e-8)
            
            return {
                'volume_bollinger': volume_bollinger,
                'volume_ma': ma,
                'volume_ma_lag': ma_lag,
                'volume_upper': upper,
                'volume_lower': lower,
                'volume_std': std
            }
            
        except Exception as e:
            logger.error(f"计算成交量Bollinger指标失败: {e}")
            raise ValidationException(f"计算成交量Bollinger指标失败: {str(e)}")
    
    def calculate_kdj(
        self,
        df: pd.DataFrame,
        k_period: int = 9,
        d_period: int = 3,
        j_period: int = 3
    ) -> Dict[str, pd.Series]:
        """计算KDJ指标
        
        Args:
            df: 包含OHLC数据的DataFrame
            k_period: K值计算周期
            d_period: D值平滑周期
            j_period: J值计算周期
            
        Returns:
            Dict[str, pd.Series]: 包含K、D、J值的字典
        """
        try:
            # 计算最高价和最低价的滚动窗口
            low_min = df['low'].rolling(window=k_period).min()
            high_max = df['high'].rolling(window=k_period).max()
            
            # 计算RSV (Raw Stochastic Value)
            rsv = (df['close'] - low_min) / (high_max - low_min + 1e-8) * 100
            
            # 计算K值 (使用指数移动平均)
            k_values = rsv.ewm(span=d_period).mean()
            
            # 计算D值 (K值的指数移动平均)
            d_values = k_values.ewm(span=j_period).mean()
            
            # 计算J值
            j_values = 3 * k_values - 2 * d_values
            
            return {
                'K': k_values,
                'D': d_values,
                'J': j_values,
                'RSV': rsv
            }
            
        except Exception as e:
            logger.error(f"计算KDJ指标失败: {e}")
            raise ValidationException(f"计算KDJ指标失败: {str(e)}")
    
    def calculate_buy_sell_signals(
        self,
        df: pd.DataFrame,
        volume_indicators: Dict[str, pd.Series],
        kdj_indicators: Dict[str, pd.Series]
    ) -> Dict[str, List[int]]:
        """计算买卖点信号
        
        基于成交量突破和KDJ金叉死叉
        
        Args:
            df: 原始数据DataFrame
            volume_indicators: 成交量指标字典
            kdj_indicators: KDJ指标字典
            
        Returns:
            Dict[str, List[int]]: 包含买入和卖出信号点位的字典
        """
        try:
            buy_signals = []
            sell_signals = []
            
            k_values = kdj_indicators['K']
            d_values = kdj_indicators['D']
            volume_bollinger = volume_indicators['volume_bollinger']
            
            for i in range(1, len(df)):
                # KDJ金叉买入信号
                if (k_values.iloc[i] > d_values.iloc[i] and 
                    k_values.iloc[i-1] <= d_values.iloc[i-1] and
                    k_values.iloc[i] < 80):  # 避免高位买入
                    
                    # 成交量确认
                    if volume_bollinger.iloc[i] > 0.5:
                        buy_signals.append(i)
                
                # KDJ死叉卖出信号
                if (k_values.iloc[i] < d_values.iloc[i] and 
                    k_values.iloc[i-1] >= d_values.iloc[i-1] and
                    k_values.iloc[i] > 20):  # 避免低位卖出
                    
                    # 成交量确认
                    if volume_bollinger.iloc[i] < -0.5:
                        sell_signals.append(i)
                
                # 额外的成交量突破信号
                if volume_bollinger.iloc[i] > 2.0 and df['close'].iloc[i] > df['close'].iloc[i-1]:
                    buy_signals.append(i)
                
                if volume_bollinger.iloc[i] < -2.0 and df['close'].iloc[i] < df['close'].iloc[i-1]:
                    sell_signals.append(i)
            
            return {
                'buy_signals': sorted(list(set(buy_signals))),
                'sell_signals': sorted(list(set(sell_signals)))
            }
            
        except Exception as e:
            logger.error(f"计算买卖点信号失败: {e}")
            raise ValidationException(f"计算买卖点信号失败: {str(e)}")
    
    async def calculate_complete_indicators(
        self,
        stock_code: str,
        start_date: Union[str, datetime] = None,
        end_date: Union[str, datetime] = None,
        period_index: int = 1, # This might map to a frequency string 'dh', 'wh' etc.
        bollinger_window: int = 20,
        lag: int = 6 # Used for EMA span = lag * 2
    ) -> Dict[str, Any]:
        """
        计算所有高级技术指标和新的VPKM交易信号。
        """
        
        period_map = {
            1: PeriodType.DAILY,
            2: PeriodType.WEEKLY,
            3: PeriodType.MONTHLY,
            4: PeriodType.MIN_15,
            5: PeriodType.MIN_30,
            6: PeriodType.MIN_60
        }
        selected_period_type = period_map.get(period_index, PeriodType.DAILY)

        # Map PeriodType to frequency string for data fetching if needed by DataFetcher
        # This mapping might be internal to DataFetcher or defined elsewhere.
        # For example:
        freq_map = {
            PeriodType.DAILY: 'D',
            PeriodType.WEEKLY: 'W',
            PeriodType.MONTHLY: 'M',
            # Assuming minute data might still fetch daily and then be downsampled or handled differently
            PeriodType.MIN_15: 'D',  # Placeholder, adjust if DataFetcher handles minute data
            PeriodType.MIN_30: 'D',  # Placeholder
            PeriodType.MIN_60: 'D'   # Placeholder
        }
        fetch_freq = freq_map.get(selected_period_type, 'D')

        if start_date is None or end_date is None:
            end_date_dt = datetime.now()
            # Ensure enough data for all calculations, e.g., KDJ(9), BB(20), EMA(lag*2 = 12)
            # Max window needed is roughly max(bollinger_window, kdj_period, lag*2) + some buffer
            min_data_days = max(bollinger_window, 9, lag*2) + 50 # Adding buffer
            start_date_dt = end_date_dt - timedelta(days=min_data_days) 
            start_date = start_date_dt.strftime('%Y-%m-%d')
            end_date = end_date_dt.strftime('%Y-%m-%d')
          # Adjust min_periods for fetch_stock_data based on the largest window
        df = await self.data_fetcher.fetch_stock_data(
            stock_code, start_date, end_date, 
            min_periods=max(bollinger_window, 9, lag*2) +1 # +1 for diff/shift operations
        )
        
        if df.empty:
            logger.warning(f"No data fetched for {stock_code} between {start_date} and {end_date} for period {selected_period_type}")
            return {
                "error": "No data available",
                'period_type': selected_period_type,
                'data_points': 0,
                'date_range': {'start': str(start_date), 'end': str(end_date)},
                'price_bollinger': {'middle': [], 'upper': [], 'lower': []},
                'volume_analysis': {'in_volume': [], 'ex_volume': [], 'volume_difference': []},
                'volume_bollinger': {'middle': [], 'upper': [], 'lower': [], 'smoothed_volume': []},
                'kdj_indicators': {'K': [], 'D': [], 'J': []},
                'trading_signals': {'buy_signals': [], 'sell_signals': []},
                'parameters': {'bollinger_window': bollinger_window, 'lag': lag, 'period_index': period_index}
            }

        # 1. 计算价格布林带 (Price Bollinger Bands)
        price_bollinger_bands = self.calculate_bollinger_bands(df, window=bollinger_window)
        price_bollinger_lower = price_bollinger_bands.get('lower', pd.Series(index=df.index, dtype=float))

        # 2. 计算KDJ指标 (Using default periods from strategy_adaptation_plan.md which implies standard KDJ)
        kdj_indicators = self.calculate_kdj(df) # Default periods: k_period=9, d_period=3, j_period=3
        kdj_k = kdj_indicators.get('K', pd.Series(index=df.index, dtype=float))
        kdj_d = kdj_indicators.get('D', pd.Series(index=df.index, dtype=float))

        # 3. 计算 raw_diff_vol (ex_vol - in_vol)
        volume_analysis_data = self.calculate_volume_inout_difference(df)
        raw_diff_vol = volume_analysis_data.get('volume_diff', pd.Series(index=df.index, dtype=float))

        # 4. 计算 log_vol (标准化和EMA平滑后的成交量差异)
        log_vol = pd.Series(np.nan, index=df.index, dtype=float)
        if raw_diff_vol.notna().any():
            min_val = raw_diff_vol.min() # Min of the whole series for consistent standardization
            standardized_diff_vol = raw_diff_vol - min_val + 1.0
            # Ensure lag*2 is a valid span for ewm, requires at least that many points for min_periods
            if standardized_diff_vol.notna().any() and len(standardized_diff_vol.dropna()) >= lag * 2 and lag*2 > 0:
                log_vol = standardized_diff_vol.ewm(span=lag*2, adjust=False, min_periods=lag*2).mean()
        
        # 5. 计算 average_vol (log_vol 的布林带中轨)
        average_vol = pd.Series(np.nan, index=df.index, dtype=float)
        vol_bb_on_log_vol = {'upper': pd.Series(np.nan, index=df.index, dtype=float), 'lower': pd.Series(np.nan, index=df.index, dtype=float)}
        if log_vol.notna().any() and len(log_vol.dropna()) >= bollinger_window:
            vol_bb_on_log_vol = self._calculate_bollinger_on_series(log_vol, window=bollinger_window)
            average_vol = vol_bb_on_log_vol.get('middle', pd.Series(index=df.index, dtype=float))

        # 6. 获取收盘价
        c_price = df.get('close', pd.Series(index=df.index, dtype=float))

        # 7. 计算VPKM交易信号
        vpkm_trading_signals = self._calculate_vpkm_signals(
            df, log_vol, average_vol, kdj_k, kdj_d, price_bollinger_lower, c_price
        )        
        
        # Original volume bollinger calculation (on raw_diff_vol)
        # The 'lag' parameter for this specific function was different from the EMA lag.
        # From strategy_adaptation_plan, this was not explicitly part of VPKM, but was in old signal calc.
        # Assuming a default lag for this if not specified, e.g., lag=2 as per previous semantic search context for this func.
        original_volume_bollinger_calc = self.calculate_volume_bollinger(
             raw_diff_vol, 
             window=bollinger_window,
             lag=2 # Defaulting to a common lag for this type of score, adjust if known
        )          # 转换VPKM交易信号到API期望的格式
        buy_signals = vpkm_trading_signals.get("buy_indices_VPKM", [])
        sell_tp_signals = vpkm_trading_signals.get("sell_indices_VPKM_tp", [])
        sell_sl_signals = vpkm_trading_signals.get("sell_indices_VPKM_sl", [])
        # 合并止盈和止损信号
        sell_signals = sorted(list(set(sell_tp_signals + sell_sl_signals)))

        result = {
            'period_type': selected_period_type,
            'data_points': len(df),
            'date_range': {
                'start': str(df.index.min().date()) if not df.empty else start_date,
                'end': str(df.index.max().date()) if not df.empty else end_date
            },
            'price_bollinger': {
                'middle': price_bollinger_bands.get('middle', pd.Series(dtype=float)).fillna(0).tolist(),
                'upper': price_bollinger_bands.get('upper', pd.Series(dtype=float)).fillna(0).tolist(),
                'lower': price_bollinger_lower.fillna(0).tolist()
            },
            'volume_analysis': { 
                'in_volume': volume_analysis_data.get('in_volume', pd.Series(dtype=float)).fillna(0).tolist(),
                'ex_volume': volume_analysis_data.get('ex_volume', pd.Series(dtype=float)).fillna(0).tolist(),
                'volume_difference': raw_diff_vol.fillna(0).tolist()
            },
            'volume_bollinger': {
                'middle': original_volume_bollinger_calc.get('volume_ma', pd.Series(dtype=float)).fillna(0).tolist(),
                'upper': original_volume_bollinger_calc.get('volume_upper', pd.Series(dtype=float)).fillna(0).tolist(),
                'lower': original_volume_bollinger_calc.get('volume_lower', pd.Series(dtype=float)).fillna(0).tolist(),
                'smoothed_volume': original_volume_bollinger_calc.get('volume_bollinger', pd.Series(dtype=float)).fillna(0).tolist()
            },
            'kdj_indicators': {
                'K': kdj_k.fillna(0).tolist(),
                'D': kdj_d.fillna(0).tolist(),
                'J': kdj_indicators.get('J', pd.Series(dtype=float)).fillna(0).tolist()
            },
            'trading_signals': {
                'buy_signals': buy_signals,
                'sell_signals': sell_signals
            },
            'parameters': {
                'bollinger_window': bollinger_window,
                'lag': lag,
                'period_index': period_index
            }
        }
        return result

    def calculate_bollinger_bands(self, df: pd.DataFrame, window: int = 20) -> Dict[str, pd.Series]:
        if 'close' not in df.columns or df['close'].empty or len(df['close'].dropna()) < window:
            nan_series = pd.Series(np.nan, index=df.index, dtype=float)
            return {'upper': nan_series, 'middle': nan_series, 'lower': nan_series}
        return self._calculate_bollinger_on_series(df['close'], window=window)


    def calculate_kdj(self, df: pd.DataFrame, k_period: int = 9, d_period: int = 3, j_period: int = 3) -> Dict[str, pd.Series]:
        required_cols = ['high', 'low', 'close']
        if any(col not in df.columns for col in required_cols) or len(df) < k_period:
            nan_series = pd.Series(np.nan, index=df.index, dtype=float)
            return {'K': nan_series, 'D': nan_series, 'J': nan_series}
        
        # Ensure enough non-NaN data points for rolling operations
        if df['low'].dropna().shape[0] < k_period or \
           df['high'].dropna().shape[0] < k_period or \
           df['close'].dropna().shape[0] < k_period:
            nan_series = pd.Series(np.nan, index=df.index, dtype=float)
            return {'K': nan_series, 'D': nan_series, 'J': nan_series}

        low_min = df['low'].rolling(window=k_period, min_periods=k_period).min()
        high_max = df['high'].rolling(window=k_period, min_periods=k_period).max()
        
        rsv = (df['close'] - low_min) / (high_max - low_min + 1e-12) * 100 # Increased epsilon slightly
        rsv = rsv.fillna(50) # Fill NaNs in RSV (e.g. at the beginning or if high_max == low_min)
        
        # KDJ calculation often uses EMA, span = (2/alpha) - 1. For typical KDJ, alpha = 1/N (N=d_period or j_period)
        # So span = 2*N -1. Or, if d_period is the smoothing factor for EMA: span = d_period (if it's like Wilder's MA)
        # The strategy doc implies standard KDJ, which often uses SMA or a specific EMA variant.
        # Using simple EWM with span for this placeholder, as per previous code structure.
        # Adjust=False is common for financial EMAs.
        k_values = rsv.ewm(span=d_period, adjust=False, min_periods=d_period).mean()
        d_values = k_values.ewm(span=j_period, adjust=False, min_periods=j_period).mean() 
        j_values = 3 * k_values - 2 * d_values
        return {'K': k_values, 'D': d_values, 'J': j_values}

    def calculate_volume_inout_difference(self, df: pd.DataFrame) -> Dict[str, pd.Series]:
        # This is a placeholder. The actual `stock_library.cal_diff` logic is needed.
        # `stock_library.cal_diff(data_open, data_close, h_price, l_price, vol)`
        # For now, returning NaNs or a very basic estimation.
        volume = df.get('volume', pd.Series(index=df.index, dtype=float))
        close = df.get('close', pd.Series(index=df.index, dtype=float))
        open_price = df.get('open', pd.Series(index=df.index, dtype=float))
        
        ex_volume = pd.Series(np.nan, index=df.index, dtype=float)
        in_volume = pd.Series(np.nan, index=df.index, dtype=float)

        if not volume.empty and not close.empty and not open_price.empty:
            # Simplified logic: if close > open, consider it ex_volume dominant, else in_volume dominant
            # This is a gross simplification and not the `cal_diff` logic.
            price_increase_cond = close > open_price
            price_decrease_cond = close < open_price
            price_same_cond = close == open_price

            ex_volume[price_increase_cond] = volume[price_increase_cond]
            in_volume[price_decrease_cond] = volume[price_decrease_cond]
            
            # For days where close == open, or for a more nuanced split:
            ex_volume[price_decrease_cond] = volume[price_decrease_cond] * 0.3 # Small portion if price fell
            in_volume[price_increase_cond] = volume[price_increase_cond] * 0.3 # Small portion if price rose

            ex_volume[price_same_cond] = volume[price_same_cond] * 0.5
            in_volume[price_same_cond] = volume[price_same_cond] * 0.5
            
        volume_diff = (ex_volume - in_volume).fillna(0) # Fill NaN diffs with 0
        return {
            'in_volume': in_volume.fillna(0),
            'ex_volume': ex_volume.fillna(0),
            'volume_diff': volume_diff
        }

    def calculate_volume_bollinger(self, diff_vol: pd.Series, window: int = 20, lag: int = 2) -> Dict[str, pd.Series]:
        # This is the original function that produced a normalized score.
        # The `lag` here is for ma.shift(lag), not EMA span.
        if diff_vol.empty or len(diff_vol.dropna()) < window or len(diff_vol.dropna()) < lag:
            nan_s = pd.Series(np.nan, index=diff_vol.index, dtype=float)
            return {
                'volume_bollinger': nan_s, 'volume_ma': nan_s, 'volume_ma_lag': nan_s,
                'volume_upper': nan_s, 'volume_lower': nan_s, 'volume_std': nan_s
            }

        ma = diff_vol.rolling(window=window, min_periods=window).mean()
        ma_lag = ma.shift(lag) 
        std = diff_vol.rolling(window=window, min_periods=window).std()
        upper = ma + 2 * std
        lower = ma - 2 * std
        volume_bollinger_score = (diff_vol - ma_lag) / (std + 1e-12) 
        return {
            'volume_bollinger': volume_bollinger_score, 
            'volume_ma': ma, 
            'volume_ma_lag': ma_lag,
            'volume_upper': upper, 
            'volume_lower': lower, 
            'volume_std': std
        }
