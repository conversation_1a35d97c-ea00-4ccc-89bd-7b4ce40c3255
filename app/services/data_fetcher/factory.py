"""数据提供者工厂模块"""
from typing import Dict, Optional, Type, Tuple, Any
from app.core import logging
from functools import lru_cache
import asyncio

from .base import DataFetcher
from .adapter import DataAdapter, BaseDataAdapter, AdaptedDataFetcher
from .provider import (
    MairuiDataFetcher,
    MairuiAdapter,
    AkshareDataFetcher,
    AkshareAdapter,
    TushareDataFetcher,
    TushareAdapter,
)

logger = logging.getLogger(__name__)

class DataFetcherFactory:
    """
    数据提供者工厂，负责创建和管理数据提供者实例。
    采用单例模式确保全局只有一个工厂实例。
    """
    
    _instance = None
    _fetchers: Dict[str, DataFetcher] = {}
    
    # 支持的数据提供者映射
    _PROVIDER_MAP: Dict[str, Tuple[Type[Any], Type[Any]]] = {
        "mairui": (MairuiDataFetcher, MairuiAdapter),
        "akshare": (AkshareDataFetcher, AkshareAdapter),
        "tushare": (<PERSON><PERSON><PERSON><PERSON>ataFet<PERSON>, <PERSON><PERSON>reAdapter),
    }
    
    def __new__(cls):
        """实现单例模式"""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    @classmethod
    @lru_cache()  # 缓存创建的实例
    def create_fetcher(
        cls,
        provider: str,
        **config
    ) -> DataFetcher:
        """
        创建数据提供者实例。
        
        Args:
            provider: 数据提供者名称
            **config: 提供者特定的配置参数
        
        Returns:
            DataFetcher: 数据提供者实例
            
        Raises:
            ValueError: 如果提供者名称无效
        """
        if provider not in cls._PROVIDER_MAP:
            raise ValueError(f"不支持的数据提供者: {provider}")
        
        try:
            # 获取提供者类和适配器类
            fetcher_cls, adapter_cls = cls._PROVIDER_MAP[provider]
            
            # 创建原始数据提供者实例
            fetcher = fetcher_cls(**config)
            
            # 创建适配器实例
            adapter = adapter_cls()
            
            # 创建适配后的数据提供者
            adapted_fetcher = AdaptedDataFetcher(fetcher, adapter)
            
            # 保存实例
            cls._fetchers[provider] = adapted_fetcher
            
            logger.info(f"成功创建数据提供者: {provider}")
            return adapted_fetcher
            
        except Exception as e:
            logger.error(f"创建数据提供者失败 {provider}: {str(e)}")
            raise
    
    @classmethod
    def get_fetcher(
        cls,
        provider: str,
        **config
    ) -> DataFetcher:
        """
        获取数据提供者实例，如果不存在则创建。
        
        Args:
            provider: 数据提供者名称
            **config: 提供者特定的配置参数
        
        Returns:
            DataFetcher: 数据提供者实例
        """
        if provider not in cls._fetchers:
            return cls.create_fetcher(provider, **config)
        return cls._fetchers[provider]
    
    @classmethod
    def register_provider(
        cls,
        name: str,
        fetcher_cls: Type[DataFetcher],
        adapter_cls: Type[DataAdapter]
    ) -> None:
        """
        注册新的数据提供者。
        
        Args:
            name: 提供者名称
            fetcher_cls: 数据提供者类
            adapter_cls: 数据适配器类
        """
        if name in cls._PROVIDER_MAP:
            logger.warning(f"数据提供者 {name} 已存在，将被覆盖")
        cls._PROVIDER_MAP[name] = (fetcher_cls, adapter_cls)
        logger.info(f"成功注册数据提供者: {name}")
    
    @classmethod
    def remove_provider(cls, name: str) -> None:
        """
        移除数据提供者。
        
        Args:
            name: 提供者名称
        """
        if name in cls._PROVIDER_MAP:
            del cls._PROVIDER_MAP[name]
            if name in cls._fetchers:
                del cls._fetchers[name]
            logger.info(f"成功移除数据提供者: {name}")
    
    @classmethod
    def get_supported_providers(cls) -> list:
        """
        获取所有支持的数据提供者列表。
        
        Returns:
            list: 提供者名称列表
        """
        return list(cls._PROVIDER_MAP.keys())
    
    @classmethod
    async def reset(cls) -> None:
        """
        重置工厂状态，清除所有缓存的实例。
        主要用于测试目的。
        """
        # 清理异步资源
        for fetcher in cls._fetchers.values():
            if hasattr(fetcher, 'close') and callable(fetcher.close):
                try:
                    await fetcher.close()
                except Exception as e:
                    logger.error(f"关闭数据提供者失败: {str(e)}")
        
        cls._fetchers.clear()
        cls.create_fetcher.cache_clear()
