from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from datetime import date, datetime

class DataFetcher(ABC):
    """
    数据获取服务的基础接口。
    定义了从不同数据源获取股票数据所需的标准方法。
    """
    
    @abstractmethod
    async def get_stock_list(self) -> List[Dict[str, Any]]:
        """
        获取股票列表及其基本信息。

        Returns:
            List[Dict[str, Any]]: 股票信息列表，每个字典包含以下字段:
                - code: str, 股票代码
                - name: str, 股票名称
                - industry: str, 所属行业
                - market: str, 交易市场
                - list_date: str, 上市日期
        """
        pass

    @abstractmethod
    async def get_daily_data(
        self,
        code: str,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> List[Dict[str, Any]]:
        """
        获取指定股票的日线数据。

        Args:
            code: 股票代码
            start_date: 开始日期，如果为None则获取最早的数据
            end_date: 结束日期，如果为None则获取到最新的数据

        Returns:
            List[Dict[str, Any]]: 日线数据列表，每个字典包含以下字段:
                - date: datetime, 交易日期
                - open: float, 开盘价
                - high: float, 最高价
                - low: float, 最低价
                - close: float, 收盘价
                - volume: float, 成交量
                - amount: float, 成交额
                - turnover: float, 换手率
                - change_pct: float, 涨跌幅
        """
        pass

    @abstractmethod
    async def get_realtime_quotes(self, codes: List[str]) -> List[Dict[str, Any]]:
        """
        获取指定股票列表的实时行情数据。

        Args:
            codes: 股票代码列表

        Returns:
            List[Dict[str, Any]]: 实时行情数据列表，每个字典包含以下字段:
                - code: str, 股票代码
                - price: float, 当前价格
                - change: float, 涨跌额
                - change_pct: float, 涨跌幅
                - volume: float, 成交量
                - amount: float, 成交额
                - time: datetime, 数据时间
        """
        pass

    @abstractmethod
    async def get_index_components(self, index_code: str) -> List[str]:
        """
        获取指数成分股列表。

        Args:
            index_code: 指数代码，如'000300'(沪深300)

        Returns:
            List[str]: 成分股代码列表
        """
        pass

    @abstractmethod
    async def get_financial_data(
        self,
        code: str,
        report_type: str = 'annual'
    ) -> List[Dict[str, Any]]:
        """
        获取财务数据。

        Args:
            code: 股票代码
            report_type: 报表类型，可选值：'annual'(年报),'quarterly'(季报)

        Returns:
            List[Dict[str, Any]]: 财务数据列表，具体字段根据报表类型可能不同
        """
        pass

    async def close(self) -> None:
        """
        关闭数据提供者，清理资源。
        默认实现为空操作，子类可以根据需要重写此方法。
        """
        pass
