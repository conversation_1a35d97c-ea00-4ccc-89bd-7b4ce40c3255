"""
数据获取服务模块

提供统一的接口来从不同的数据源获取股票数据，支持:
- Tushare
- Akshare
- 自定义API

使用示例:
```python
from app.services.data_fetcher import DataFetcherFactory

# 创建Tushare数据提供者
fetcher = DataFetcherFactory.get_fetcher(
    "tushare",
    api_token="your_tushare_token"
)

# 获取股票列表
stocks = await fetcher.get_stock_list()

# 获取日线数据
daily_data = await fetcher.get_daily_data("000001")
```
"""

from .base import DataFetcher
from .adapter import (
    DataAdapter,
    BaseDataAdapter,
    AdaptedDataFetcher
)
from .factory import DataFetcherFactory
from .provider.tushare_provider import (
    TushareDataFetcher,
    TushareAdapter
)
from .provider.akshare_provider import (
    AkshareDataFetcher,
    AkshareAdapter
)

__all__ = [
    # 基础接口
    "DataFetcher",
    # 适配器
    "DataAdapter",
    "BaseDataAdapter",
    "AdaptedDataFetcher",
    # 工厂
    "DataFetcherFactory",
    # Tushare实现
    "TushareDataFetcher",
    "TushareAdapter",
    # Akshare实现
    "AkshareDataFetcher",
    "AkshareAdapter",
]
