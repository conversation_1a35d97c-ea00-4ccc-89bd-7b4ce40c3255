# 数据获取服务

## 简介

数据获取服务模块提供了一套统一的接口，用于从多种数据源获取股票市场数据。该模块采用了适配器模式和工厂模式，可以轻松地支持不同的数据提供商，并将各种格式的数据标准化为统一格式。

## 特性

- **统一接口**: 提供标准化的数据获取接口，无论使用哪个数据提供商
- **多数据源支持**: 目前支持Tushare和Akshare，易于扩展其他数据源
- **数据适配**: 自动将不同来源的数据转换为统一格式
- **错误重试**: 内置自动重试机制，提高数据获取的可靠性
- **异步设计**: 所有数据获取操作都是异步的，提高性能

## 模块结构

- `__init__.py`: 模块入口，导出公共API
- `base.py`: 定义数据获取的基础接口
- `adapter.py`: 实现数据适配器模式，将不同格式的数据转换为统一格式
- `factory.py`: 实现数据提供者工厂，负责创建和管理数据提供者实例
- `tushare_provider.py`: Tushare数据提供者的实现
- `akshare_provider.py`: Akshare数据提供者的实现

## 使用示例

### 基本使用

```python
import asyncio
from app.services.data_fetcher import DataFetcherFactory

async def main():
    # 创建Tushare数据提供者
    tushare_fetcher = DataFetcherFactory.get_fetcher(
        "tushare",
        api_token="your_tushare_token"
    )
    
    # 获取股票列表
    stocks = await tushare_fetcher.get_stock_list()
    print(f"获取到 {len(stocks)} 只股票")
    
    # 获取特定股票的日线数据
    daily_data = await tushare_fetcher.get_daily_data("000001")
    print(f"获取到 {len(daily_data)} 条日线数据")
    
    # 获取实时行情
    quotes = await tushare_fetcher.get_realtime_quotes(["000001", "600000"])
    print(quotes)
    
    # 使用Akshare数据提供者
    akshare_fetcher = DataFetcherFactory.get_fetcher("akshare")
    
    # 获取指数成分股
    components = await akshare_fetcher.get_index_components("000300")
    print(f"沪深300共有 {len(components)} 只成分股")

if __name__ == "__main__":
    asyncio.run(main())
```

### 获取标准化数据

所有数据提供者返回的数据都被标准化为统一格式：

```python
# 股票基本信息
{
    "code": "000001",       # 股票代码
    "name": "平安银行",     # 股票名称
    "industry": "银行",     # 所属行业
    "market": "SZ",         # 交易市场
    "list_date": "19910403" # 上市日期
}

# 日线数据
{
    "date": datetime(2023, 1, 1),  # 日期
    "open": 10.0,                  # 开盘价
    "high": 10.5,                  # 最高价
    "low": 9.8,                    # 最低价
    "close": 10.2,                 # 收盘价
    "volume": 100000,              # 成交量
    "amount": 1020000,             # 成交额
    "turnover": 2.5,               # 换手率
    "change_pct": 1.2              # 涨跌幅
}

# 实时行情
{
    "code": "000001",              # 股票代码
    "price": 10.2,                 # 当前价格
    "change": 0.2,                 # 涨跌额
    "change_pct": 2.0,             # 涨跌幅(%)
    "volume": 50000,               # 成交量
    "amount": 510000,              # 成交额
    "time": datetime.now()         # 时间
}
```

## 扩展新的数据提供者

要添加新的数据提供者，需要执行以下步骤：

1. 创建一个新的数据提供者类，继承`DataFetcher`：

```python
from app.services.data_fetcher.base import DataFetcher

class MyNewDataFetcher(DataFetcher):
    # 实现所有必需的方法
    ...
```

2. 创建一个适配器类，继承`BaseDataAdapter`：

```python
from app.services.data_fetcher.adapter import BaseDataAdapter

class MyNewAdapter(BaseDataAdapter):
    # 实现数据转换方法
    ...
```

3. 注册新的数据提供者：

```python
from app.services.data_fetcher import DataFetcherFactory
from .my_provider import MyNewDataFetcher, MyNewAdapter

# 注册新提供者
DataFetcherFactory.register_provider(
    "my_provider",
    MyNewDataFetcher,
    MyNewAdapter
)

# 使用新提供者
fetcher = DataFetcherFactory.get_fetcher(
    "my_provider",
    # 自定义配置参数
    api_key="my_key"
)
```

## 错误处理

所有数据获取方法都内置了错误重试机制，默认最多重试3次。如果仍然失败，会抛出异常。建议在调用时使用try-except捕获可能的异常：

```python
try:
    data = await fetcher.get_daily_data("000001")
except Exception as e:
    print(f"获取数据失败: {e}")
```
