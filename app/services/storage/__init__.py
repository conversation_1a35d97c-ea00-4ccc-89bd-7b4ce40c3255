"""
存储服务模块
"""
from typing import Type

from sqlalchemy.ext.asyncio import AsyncSession

from app.services.storage.base import BaseStorageService
from app.services.storage.stock_storage import StockStorageService

def get_storage_service(
    session: AsyncSession,
    service_cls: Type[BaseStorageService] = StockStorageService
) -> BaseStorageService:
    """获取存储服务实例"""
    return service_cls(session)
