"""
计划任务管理服务

提供任务的管理、监控和状态查询功能。
"""

from app.core import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

from app.core.scheduler import scheduler
from apscheduler.job import Job
from apscheduler.triggers.cron import CronTrigger

logger = logging.getLogger(__name__)


class TaskManager:
    """任务管理器"""
    
    @staticmethod
    def list_jobs() -> List[Dict[str, Any]]:
        """
        列出所有计划任务
        
        Returns:
            List[Dict[str, Any]]: 任务信息列表
        """
        jobs = []
        for job in scheduler.scheduler.get_jobs():
            jobs.append({
                'id': job.id,
                'name': str(job.name) if job.name else job.id,
                'func': f"{job.func.__module__}.{job.func.__name__}",
                'schedule': job.trigger.fields[0].expression,
                'next_run': job.next_run_time.isoformat() if job.next_run_time else None,
                'active': job.next_run_time is not None
            })
        return jobs

    @staticmethod
    def get_job_info(job_id: str) -> Optional[Dict[str, Any]]:
        """
        获取指定任务的详细信息
        
        Args:
            job_id: 任务ID
            
        Returns:
            Optional[Dict[str, Any]]: 任务详细信息
        """
        job = scheduler.scheduler.get_job(job_id)
        if not job:
            return None
            
        return {
            'id': job.id,
            'name': str(job.name) if job.name else job.id,
            'func': f"{job.func.__module__}.{job.func.__name__}",
            'schedule': job.trigger.fields[0].expression,
            'next_run': job.next_run_time.isoformat() if job.next_run_time else None,
            'active': job.next_run_time is not None,
            'args': job.args,
            'kwargs': job.kwargs
        }

    @staticmethod
    def pause_job(job_id: str) -> bool:
        """
        暂停指定任务
        
        Args:
            job_id: 任务ID
            
        Returns:
            bool: 是否成功暂停
        """
        job = scheduler.scheduler.get_job(job_id)
        if not job:
            return False
            
        job.pause()
        logger.info(f"已暂停任务: {job_id}")
        return True

    @staticmethod
    def resume_job(job_id: str) -> bool:
        """
        恢复指定任务
        
        Args:
            job_id: 任务ID
            
        Returns:
            bool: 是否成功恢复
        """
        job = scheduler.scheduler.get_job(job_id)
        if not job:
            return False
            
        job.resume()
        logger.info(f"已恢复任务: {job_id}")
        return True

    @staticmethod
    def modify_job_schedule(job_id: str, cron: str) -> bool:
        """
        修改任务调度时间
        
        Args:
            job_id: 任务ID
            cron: 新的cron表达式
            
        Returns:
            bool: 是否成功修改
        """
        try:
            job = scheduler.scheduler.get_job(job_id)
            if not job:
                return False
                
            trigger = CronTrigger.from_crontab(cron)
            job.reschedule(trigger=trigger)
            
            logger.info(f"已修改任务 {job_id} 的调度时间为: {cron}")
            return True
        except Exception as e:
            logger.error(f"修改任务调度时间失败: {str(e)}")
            return False

    @staticmethod
    def get_job_stats() -> Dict[str, Any]:
        """
        获取任务统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        jobs = scheduler.scheduler.get_jobs()
        active_jobs = [job for job in jobs if job.next_run_time is not None]
        paused_jobs = [job for job in jobs if job.next_run_time is None]
        
        return {
            'total_jobs': len(jobs),
            'active_jobs': len(active_jobs),
            'paused_jobs': len(paused_jobs),
            'next_run_jobs': sorted(
                [(job.id, job.next_run_time) for job in active_jobs],
                key=lambda x: x[1]
            )[:5]  # 最近5个要执行的任务
        }


# 创建全局任务管理器实例
task_manager = TaskManager()
