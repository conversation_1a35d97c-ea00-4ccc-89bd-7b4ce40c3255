"""
缓存清理服务

定期清理过期的缓存项，避免内存占用过大
"""
from app.core import logging
from app.core.cache import clean_expired_cache
from app.core.scheduler import ScheduledTask
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.core.database import get_db
from app.services.storage.stock_storage import StockStorageService

logger = logging.getLogger(__name__)


class CacheCleanService:
    """缓存清理服务"""

    def __init__(self):
        self._storage_services: list[StockStorageService] = []

    async def _get_storage_service(self) -> StockStorageService:
        """获取存储服务实例"""
        db: AsyncSession = await get_db()
        storage = StockStorageService(db)
        self._storage_services.append(storage)
        return storage

    @ScheduledTask(cron="0 0 * * *")  # 每天零点执行
    async def clean_expired_cache_task(self, *args):
        """定时清理过期缓存"""
        try:
            logger.info("开始清理过期缓存...")
            cleaned_count = await clean_expired_cache()
            logger.info(f"缓存清理完成，共清理 {cleaned_count} 项缓存")
        except Exception as e:
            logger.error(f"缓存清理任务失败: {str(e)}")

    @ScheduledTask(cron="0 1 * * 0")  # 每周日凌晨1点执行
    async def clean_expired_data(self, *args):
        """清理过期数据"""
        try:
            logger.info("开始清理过期数据...")
            
            # 获取存储服务实例
            storage = await self._get_storage_service()
            
            # 计算过期时间（例如：保留近3个月的数据）
            expire_date = datetime.now() - timedelta(days=90)
            
            # TODO: 实现过期数据的清理逻辑
            # 例如：删除过期的分区表、归档旧数据等
            # 这部分逻辑需要根据具体的业务需求来实现
            
            logger.info("过期数据清理完成")
            
        except Exception as e:
            logger.error(f"清理过期数据失败: {str(e)}")
            raise


# Initialize the service
cache_clean_service = CacheCleanService()
