"""
K线数据分析API接口

提供日K/周K/月K数据获取和统计功能，支持同时获取相关技术指标
"""
from typing import List, Optional, Dict, Any  # Added Dict, Any
from fastapi import APIRouter, Depends, Query, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import date, timedelta, datetime

from app.core.database import get_db
from app.services.storage.stock_storage import StockStorageService
from app.services.analytics.kline_service import KlineAnalysisService
from app.core.cache import cache_result, get_cached_result
from app.schemas.common import CommonResponse  # Added import

router = APIRouter(
    prefix="/kline",  # Keep the prefix here as it's specific to kline endpoints
    # Remove tags here, they are defined when including the router in app/api/__init__.py
    responses={404: {"description": "数据不存在"}}
)

# 依赖注入：创建K线分析服务
async def get_kline_service(db: AsyncSession = Depends(get_db)):
    """获取K线分析服务实例"""
    storage = StockStorageService(db)
    return KlineAnalysisService(storage)

@router.get("/{stock_code}", response_model=CommonResponse[Dict[str, Any]])  # Updated response_model
async def get_kline_data(
    stock_code: str,
    freq: str = Query("D", regex="^[DWM]$", description="数据周期：D(日线)、W(周线)、M(月线)"),
    start_date: Optional[str] = Query(None, description="开始日期，格式：YYYY-MM-DD"),
    end_date: Optional[str] = Query(None, description="结束日期，格式：YYYY-MM-DD"),
    with_indicators: bool = Query(False, description="是否包含技术指标"),
    indicators: List[str] = Query(None, description="技术指标列表，例如：macd,kdj,rsi,arbr,volume"),
    kline_service: KlineAnalysisService = Depends(get_kline_service)
):
    """
    获取股票K线数据
    
    支持日K/周K/月K数据，可选是否包含技术指标
    """
    # 设置默认日期
    if not end_date:
        end_date = date.today().isoformat()
    if not start_date:
        # 根据周期设置不同的默认时间范围
        days_back = 100  # 日线默认100天
        if freq == 'W':
            days_back = 52 * 7  # 周线默认52周
        elif freq == 'M':
            days_back = 24 * 30  # 月线默认24个月
        start_date = (date.fromisoformat(end_date) - timedelta(days=days_back)).isoformat()
    
    # 处理指标列表
    indicator_list = []
    if with_indicators and indicators:
        indicator_list = [i.strip().lower() for i in indicators]
    
    # 尝试从缓存获取
    cache_key = f"kline:{stock_code}:{freq}:{start_date}:{end_date}:{with_indicators}:{'-'.join(indicator_list) if indicator_list else ''}"
    cached_data = await get_cached_result(cache_key)
    if cached_data:
        return CommonResponse(data=cached_data)  # Wrapped cached data
    
    try:
        # 获取数据
        result = await kline_service.get_kline_data(
            stock_code, freq, start_date, end_date, with_indicators, indicator_list
        )
        
        # 缓存结果
        await cache_result(cache_key, result, expire=1800)  # 缓存30分钟
        
        return CommonResponse(data=result)  # Wrapped successful result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取K线数据失败: {str(e)}")
