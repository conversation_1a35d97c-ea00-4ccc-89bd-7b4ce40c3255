"""
股票模块API入口
"""
from fastapi import APIRouter

# 创建股票API路由器
router = APIRouter()

# 导入子模块
from app.api.endpoints.stocks.stock_list import router as stock_list_router
from app.api.endpoints.stocks.stock_data import router as stock_data_router
from app.api.endpoints.stocks.stock_search import router as stock_search_router

# 注册子路由
router.include_router(stock_list_router, prefix="/list")
router.include_router(stock_data_router, prefix="/data")
router.include_router(stock_search_router, prefix="/search")