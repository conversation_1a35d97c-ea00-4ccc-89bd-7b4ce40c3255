"""
技术指标分析REST API接口
提供各种技术指标（MACD/KDJ/ARBR/RSI）的计算和查询接口
以及日K/周K/月K数据的转换和查询
"""
from typing import List, Optional, Dict, Any
from datetime import date, timedelta, datetime
from fastapi import APIRouter, Depends, Query, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel, Field

from app.core.database import get_db
from app.services.storage.stock_storage import StockStorageService
from app.services.indicators.indicator_service import IndicatorService
from app.core.cache import cache_result, get_cached_result
from app.schemas.common import CommonResponse

# Remove the redundant prefix, it's handled in app/api/__init__.py
router = APIRouter(
    tags=["Indicators"], # Use consistent capitalized tag
    responses={404: {"description": "指标数据不存在"}}
)

# --- Pydantic Response Models ---

class IndicatorDataBase(BaseModel):
    date: List[str]

class MACDResponse(IndicatorDataBase):
    diff: List[float]
    dea: List[float]
    macd: List[float]

class KDJResponse(IndicatorDataBase):
    k: List[float]
    d: List[float]
    j: List[float]

class RSIResponse(IndicatorDataBase):
    rsi1: List[float]
    rsi2: List[float]
    rsi3: List[float]

class ARBRResponse(IndicatorDataBase):
    ar: List[float]
    br: List[float]

class VolumeAnalysisResponse(IndicatorDataBase):
    volume: List[float]
    ma5: List[float]
    ma10: List[float]

# 依赖注入：创建指标计算服务
async def get_indicator_service(db: AsyncSession = Depends(get_db)):
    """获取指标计算服务实例"""
    storage = StockStorageService(db)
    return IndicatorService(storage)

# MACD指标API
@router.get("/macd/{stock_code}", response_model=CommonResponse[MACDResponse])
async def get_macd(
    stock_code: str,
    start_date: Optional[str] = Query(None, description="开始日期，格式：YYYY-MM-DD"),
    end_date: Optional[str] = Query(None, description="结束日期，格式：YYYY-MM-DD"),
    fast_period: int = Query(12, ge=1, le=100, description="快线周期"),
    slow_period: int = Query(26, ge=1, le=200, description="慢线周期"),
    signal_period: int = Query(9, ge=1, le=100, description="信号线周期"),
    freq: str = Query("D", regex="^[DWM]$", description="数据周期：D(日)、W(周)、M(月)"),
    indicator_service: IndicatorService = Depends(get_indicator_service)
):
    """
    计算MACD指标
    
    MACD指标由三部分组成：差离值（DIF或DIFF）、信号线（DEA或MACD）和柱状图（BAR或HIST）。
    
    计算公式：
    - DIF = EMA(CLOSE, fast_period) - EMA(CLOSE, slow_period)
    - DEA = EMA(DIF, signal_period)
    - HIST = (DIF - DEA) * 2
    """
    # 设置默认日期
    if not end_date:
        end_date = date.today().isoformat()
    if not start_date:
        # 默认获取最近100天的数据，但指标计算服务会根据需要获取更多历史数据
        start_date = (date.fromisoformat(end_date) - timedelta(days=365)).isoformat() 
    
    # 尝试从缓存获取
    cache_key = f"macd:{stock_code}:{start_date}:{end_date}:{fast_period}:{slow_period}:{signal_period}:{freq}"
    cached_data = await get_cached_result(cache_key)
    if cached_data:
        # 确保缓存数据符合Pydantic模型
        if isinstance(cached_data, dict) and all(k in cached_data for k in ['date', 'diff', 'dea', 'macd']):
            return CommonResponse(data=MACDResponse(**cached_data))
        # 如果缓存的是旧格式，则重新计算
        
    try:
        # 计算指标
        raw_result = await indicator_service.calculate_macd(
            stock_code, start_date, end_date, 
            fast_period, slow_period, signal_period, freq
        )
        
        if raw_result.get("error"):
            raise HTTPException(status_code=404, detail=raw_result.get("error"))

        # 转换数据结构以匹配 MACDResponse
        # raw_result['data'] 是一个 list of dicts
        # 例如: [{"date": "2023-01-01", "MACD_12_26_9": 0.1, "MACDs_12_26_9": 0.05, "MACDh_12_26_9": 0.05}, ...]
        # 或者 pandas_ta 的列名: MACD_12_26_9, MACDs_12_26_9, MACDh_12_26_9
        
        data_points = raw_result.get("data", [])
        if not data_points:
             return CommonResponse(data=MACDResponse(date=[], diff=[], dea=[], macd=[]))

        dates: List[str] = []
        diffs: List[float] = []
        deas: List[float] = []
        macds: List[float] = []

        for item in data_points:
            # 处理日期: 确保是字符串
            current_date = item.get("date")
            if isinstance(current_date, (datetime, date)):
                dates.append(current_date.isoformat())
            elif isinstance(current_date, str):
                dates.append(current_date)
            else:
                dates.append(str(current_date)) # Fallback, though ideally should be proper date string

            # 提取指标值，兼容不同可能的列名
            # pandas_ta 默认列名: MACD_{fast}_{slow}_{signal}, MACDs_{fast}_{slow}_{signal}, MACDh_{fast}_{slow}_{signal}
            # 手动计算的列名可能也是类似的，或者直接是 'diff', 'dea', 'macd'
            # 我们在 indicator_service._calculate_macd_manually 中使用的是 MACD_12_26_9, MACDs_12_26_9, MACDh_12_26_9
            
            # 优先使用 Pydantic 模型字段名，如果不存在，则尝试 pandas_ta 风格的列名
            diff_val = item.get("diff")
            if diff_val is None:
                diff_val = item.get(f"MACD_{fast_period}_{slow_period}_{signal_period}") # DIF
            
            dea_val = item.get("dea")
            if dea_val is None:
                dea_val = item.get(f"MACDs_{fast_period}_{slow_period}_{signal_period}") # DEA/Signal
            
            macd_val = item.get("macd") # MACD Histogram
            if macd_val is None:
                macd_val = item.get(f"MACDh_{fast_period}_{slow_period}_{signal_period}") # MACD Histogram

            # 如果以上都不存在，尝试通用名称
            if diff_val is None:
                diff_val = item.get("MACD") # 有些库可能直接叫 MACD
            if dea_val is None:
                dea_val = item.get("signal")
            if macd_val is None: # 柱状图
                macd_val = item.get("histogram")

            diffs.append(float(diff_val) if diff_val is not None else 0.0)
            deas.append(float(dea_val) if dea_val is not None else 0.0)
            macds.append(float(macd_val) if macd_val is not None else 0.0)
            
        transformed_data = MACDResponse(date=dates, diff=diffs, dea=deas, macd=macds)
        
        # 缓存结果
        await cache_result(cache_key, transformed_data.model_dump(), expire=3600)  # 缓存1小时
        
        return CommonResponse(data=transformed_data)
    except HTTPException as http_exc: # 重新抛出已知的HTTP异常
        raise http_exc
    except Exception as e:
        # logger.error(f"计算MACD指标失败 for {stock_code}: {str(e)}", exc_info=True) # 建议添加日志
        raise HTTPException(status_code=500, detail=f"计算MACD指标失败: {str(e)}")

# KDJ指标API
@router.get("/kdj/{stock_code}", response_model=CommonResponse[KDJResponse])
async def get_kdj(
    stock_code: str,
    start_date: Optional[str] = Query(None, description="开始日期，格式：YYYY-MM-DD"),
    end_date: Optional[str] = Query(None, description="结束日期，格式：YYYY-MM-DD"),
    window: int = Query(9, ge=1, le=100, description="计算窗口"),
    signal_period: int = Query(3, ge=1, le=50, description="信号线周期"),
    freq: str = Query("D", regex="^[DWM]$", description="数据周期：D(日)、W(周)、M(月)"),
    indicator_service: IndicatorService = Depends(get_indicator_service)
):
    """
    计算KDJ指标
    
    KDJ指标是一种超买超卖指标，由K线、D线和J线组成。
    
    计算公式：
    - K = 2/3 * 前一日K + 1/3 * RSV
    - D = 2/3 * 前一日D + 1/3 * K
    - J = 3 * K - 2 * D
    
    其中RSV = (C - L_n) / (H_n - L_n) * 100
    C为收盘价，L_n为n日内最低价，H_n为n日内最高价
    """
    # 设置默认日期
    if not end_date:
        end_date = date.today().isoformat()
    if not start_date:
        start_date = (date.fromisoformat(end_date) - timedelta(days=100)).isoformat()
    
    # 尝试从缓存获取
    cache_key = f"kdj:{stock_code}:{start_date}:{end_date}:{window}:{signal_period}:{freq}"
    cached_data = await get_cached_result(cache_key)
    if cached_data:
        return CommonResponse(data=cached_data)
    
    try:
        # 计算指标
        result = await indicator_service.calculate_kdj(
            stock_code, start_date, end_date, window, signal_period, freq
        )
        
        # 缓存结果
        await cache_result(cache_key, result, expire=3600)  # 缓存1小时
        
        return CommonResponse(data=result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"计算KDJ指标失败: {str(e)}")

# RSI指标API
@router.get("/rsi/{stock_code}", response_model=CommonResponse[RSIResponse])
async def get_rsi(
    stock_code: str,
    start_date: Optional[str] = Query(None, description="开始日期，格式：YYYY-MM-DD"),
    end_date: Optional[str] = Query(None, description="结束日期，格式：YYYY-MM-DD"),
    periods: List[int] = Query([6, 12, 24], description="RSI周期列表"),
    freq: str = Query("D", regex="^[DWM]$", description="数据周期：D(日)、W(周)、M(月)"),
    indicator_service: IndicatorService = Depends(get_indicator_service)
):
    """
    计算RSI指标
    
    RSI(相对强弱指标)是一种动量指标，测量价格变动的速度和变化。
    
    计算公式：
    RSI = 100 - (100 / (1 + RS))
    其中RS = 平均上涨点数 / 平均下跌点数
    """
    # 设置默认日期
    if not end_date:
        end_date = date.today().isoformat()
    if not start_date:
        start_date = (date.fromisoformat(end_date) - timedelta(days=100)).isoformat()
    
    # 尝试从缓存获取
    cache_key = f"rsi:{stock_code}:{start_date}:{end_date}:{'-'.join(map(str, periods))}:{freq}"
    cached_data = await get_cached_result(cache_key)
    if cached_data:
        return CommonResponse(data=cached_data)
    
    try:
        # 计算指标
        result = await indicator_service.calculate_rsi(
            stock_code, start_date, end_date, periods, freq
        )
        
        # 缓存结果
        await cache_result(cache_key, result, expire=3600)  # 缓存1小时
        
        return CommonResponse(data=result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"计算RSI指标失败: {str(e)}")

# ARBR指标API
@router.get("/arbr/{stock_code}", response_model=CommonResponse[ARBRResponse])
async def get_arbr(
    stock_code: str,
    start_date: Optional[str] = Query(None, description="开始日期，格式：YYYY-MM-DD"),
    end_date: Optional[str] = Query(None, description="结束日期，格式：YYYY-MM-DD"),
    window: int = Query(26, ge=1, le=100, description="计算窗口"),
    freq: str = Query("D", regex="^[DWM]$", description="数据周期：D(日)、W(周)、M(月)"),
    indicator_service: IndicatorService = Depends(get_indicator_service)
):
    """
    计算ARBR指标
    
    AR指标和BR指标是反映市场人气和买卖力量对比的指标。
    
    计算公式：
    - AR = ∑(H - O) / ∑(O - L) × 100
    - BR = ∑(H - PC) / ∑(PC - L) × 100
    
    其中H为最高价，O为开盘价，L为最低价，PC为前收盘价
    """
    # 设置默认日期
    if not end_date:
        end_date = date.today().isoformat()
    if not start_date:
        start_date = (date.fromisoformat(end_date) - timedelta(days=100)).isoformat()
    
    # 尝试从缓存获取
    cache_key = f"arbr:{stock_code}:{start_date}:{end_date}:{window}:{freq}"
    cached_data = await get_cached_result(cache_key)
    if cached_data:
        return CommonResponse(data=cached_data)
    
    try:
        # 计算指标
        result = await indicator_service.calculate_arbr(
            stock_code, start_date, end_date, window, freq
        )
        
        # 缓存结果
        await cache_result(cache_key, result, expire=3600)  # 缓存1小时
        
        return CommonResponse(data=result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"计算ARBR指标失败: {str(e)}")

# 成交量分析API
@router.get("/volume/{stock_code}", response_model=CommonResponse[VolumeAnalysisResponse])
async def get_volume_analysis(
    stock_code: str,
    start_date: Optional[str] = Query(None, description="开始日期，格式：YYYY-MM-DD"),
    end_date: Optional[str] = Query(None, description="结束日期，格式：YYYY-MM-DD"),
    ma_periods: List[int] = Query([5, 10, 20], description="移动平均周期列表"),
    freq: str = Query("D", regex="^[DWM]$", description="数据周期：D(日)、W(周)、M(月)"),
    indicator_service: IndicatorService = Depends(get_indicator_service)
):
    """
    获取成交量分析数据
    
    提供成交量及其移动平均线、量比等信息。
    """
    # 设置默认日期
    if not end_date:
        end_date = date.today().isoformat()
    if not start_date:
        start_date = (date.fromisoformat(end_date) - timedelta(days=100)).isoformat()
    
    # 尝试从缓存获取
    cache_key = f"volume:{stock_code}:{start_date}:{end_date}:{'-'.join(map(str, ma_periods))}:{freq}"
    cached_data = await get_cached_result(cache_key)
    if cached_data:
        return CommonResponse(data=cached_data)
    
    try:
        # 获取分析数据 - 传递计算窗口（使用最大的移动平均周期作为窗口）
        window = max(ma_periods) if ma_periods else 20
        result = await indicator_service.get_volume_analysis(
            stock_code, start_date, end_date, window, freq
        )
        
        # 缓存结果
        await cache_result(cache_key, result, expire=3600)  # 缓存1小时
        
        return CommonResponse(data=result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取成交量分析数据失败: {str(e)}")
