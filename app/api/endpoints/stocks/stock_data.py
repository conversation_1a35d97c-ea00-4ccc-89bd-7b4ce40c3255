"""
股票数据REST API接口
提供股票日线数据的CRUD操作
"""
from typing import List, Optional, Dict, Any, Union
from datetime import date, datetime, timedelta
import pandas as pd
from fastapi import APIRouter, Depends, Query, HTTPException, Path, Body, status
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel, Field

from app.core.database import get_db
from app.services.storage.stock_storage import StockStorageService
from app.core.exceptions import DatabaseException
from app.core.cache import cache_result, get_cached_result
from app.utils.data_fetcher import StockDataFetcher

# 创建路由器
router = APIRouter(
    tags=["StockData"],
    responses={
        404: {"description": "数据不存在"},
        500: {"description": "数据库操作失败"}
    }
)

# 定义请求和响应模型
class StockDailyData(BaseModel):
    """股票日线数据模型"""
    stock_code: str = Field(..., description="股票代码")
    trade_date: date = Field(..., description="交易日期")
    open: float = Field(..., description="开盘价")
    high: float = Field(..., description="最高价")
    low: float = Field(..., description="最低价")
    close: float = Field(..., description="收盘价")
    volume: int = Field(..., description="成交量(股)")
    amount: Optional[int] = Field(None, description="成交额(元)")
    change_pct: Optional[float] = Field(None, description="涨跌幅(%)")
    turnover_rate: Optional[float] = Field(None, description="换手率(%)")
    limit_status: Optional[str] = Field(None, description="涨跌停状态(U:涨停，D:跌停，N:正常)")
    is_st: bool = Field(False, description="是否ST股")

    class Config:
        orm_mode = True

class StockDailyCreate(StockDailyData):
    """创建股票日线数据模型"""
    pass

class StockDailyUpdate(BaseModel):
    """更新股票日线数据模型"""
    open: Optional[float] = Field(None, description="开盘价")
    high: Optional[float] = Field(None, description="最高价")
    low: Optional[float] = Field(None, description="最低价")
    close: Optional[float] = Field(None, description="收盘价")
    volume: Optional[int] = Field(None, description="成交量(股)")
    amount: Optional[int] = Field(None, description="成交额(元)")
    change_pct: Optional[float] = Field(None, description="涨跌幅(%)")
    turnover_rate: Optional[float] = Field(None, description="换手率(%)")
    limit_status: Optional[str] = Field(None, description="涨跌停状态")
    is_st: Optional[bool] = Field(None, description="是否ST股")

class StockDailyBatchCreate(BaseModel):
    """批量创建股票日线数据模型"""
    data: List[StockDailyCreate] = Field(..., description="股票日线数据列表")

class StatusResponse(BaseModel):
    """状态响应模型"""
    status: str
    message: str
    count: Optional[int] = None

# 依赖注入：创建股票存储服务
async def get_stock_storage(db: AsyncSession = Depends(get_db)):
    """获取股票存储服务实例"""
    return StockStorageService(db)

# 依赖注入：创建股票数据获取工具
async def get_data_fetcher(db: AsyncSession = Depends(get_db)):
    """获取股票数据获取工具实例"""
    storage = StockStorageService(db)
    return StockDataFetcher(storage)

# 获取股票日线数据
@router.get("/{stock_code}", response_model=List[StockDailyData])
async def get_stock_daily(
    stock_code: str = Path(..., description="股票代码"),
    start_date: Optional[str] = Query(None, description="开始日期，格式：YYYY-MM-DD"),
    end_date: Optional[str] = Query(None, description="结束日期，格式：YYYY-MM-DD"),
    from_provider: bool = Query(False, description="是否从数据提供者获取最新数据"),
    stock_storage: StockStorageService = Depends(get_stock_storage),
    data_fetcher: StockDataFetcher = Depends(get_data_fetcher)
):
    """
    获取指定股票的日线数据，支持日期范围查询
    """
    try:
        # 设置默认日期
        if not end_date:
            end_date = date.today().isoformat()
        if not start_date:
            start_date = (date.fromisoformat(end_date) - timedelta(days=30)).isoformat()
        
        # 尝试从缓存获取
        cache_key = f"daily:{stock_code}:{start_date}:{end_date}:{from_provider}"
        cached_data = await get_cached_result(cache_key)
        if cached_data and not from_provider:
            return cached_data
        
        if from_provider:
            # 使用数据获取工具从提供者获取数据
            df = await data_fetcher.fetch_stock_data(
                stock_code, start_date, end_date, 
                use_provider_fallback=True
            )
            
            # 将DataFrame转换为列表
            if not df.empty:
                daily_data = []
                for idx, row in df.iterrows():
                    daily_data.append({
                        "stock_code": stock_code,
                        "trade_date": idx.date(),
                        "open": float(row["open"]),
                        "high": float(row["high"]),
                        "low": float(row["low"]),
                        "close": float(row["close"]),
                        "volume": int(row["volume"]),
                        "amount": int(row["amount"]) if not pd.isna(row.get("amount")) else None,
                        "change_pct": float(row["change_pct"]) if not pd.isna(row.get("change_pct")) else None,
                        "turnover_rate": float(row["turnover_rate"]) if not pd.isna(row.get("turnover_rate")) else None,
                        "limit_status": None,  # 从提供者获取的数据可能没有这个字段
                        "is_st": False  # 从提供者获取的数据可能没有这个字段
                    })
                
                # 缓存结果
                await cache_result(cache_key, daily_data, expire=3600)  # 缓存1小时
                
                return daily_data
        else:
            # 从数据库获取
            daily_data = await stock_storage.get_stock_daily(stock_code, start_date, end_date)
            
            # 缓存结果
            await cache_result(cache_key, daily_data, expire=3600)  # 缓存1小时
            
            return daily_data
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取股票日线数据失败: {str(e)}")

# 创建单条股票日线数据
@router.post("/", response_model=StockDailyData, status_code=status.HTTP_201_CREATED)
async def create_stock_daily(
    data: StockDailyCreate,
    stock_storage: StockStorageService = Depends(get_stock_storage)
):
    """
    创建单条股票日线数据
    """
    try:
        # 检查股票是否存在
        stock_info = await stock_storage.get_stock_info(data.stock_code)
        if not stock_info:
            raise HTTPException(status_code=404, detail=f"股票代码 {data.stock_code} 不存在")
        
        # 保存到数据库
        daily_data = data.dict()
        new_daily = await stock_storage.save_stock_daily(
            data.stock_code, 
            daily_data, 
            data.trade_date
        )
        
        # 清除相关缓存
        # TODO: 实现缓存清理
        
        return new_daily
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建股票日线数据失败: {str(e)}")

# 批量创建股票日线数据
@router.post("/batch", response_model=StatusResponse)
async def batch_create_stock_daily(
    batch_data: StockDailyBatchCreate,
    stock_storage: StockStorageService = Depends(get_stock_storage)
):
    """
    批量创建股票日线数据
    """
    try:
        # 按股票代码和日期分组
        grouped_data = {}
        for item in batch_data.data:
            stock_code = item.stock_code
            trade_date = item.trade_date
            
            if stock_code not in grouped_data:
                grouped_data[stock_code] = {}
            
            if trade_date not in grouped_data[stock_code]:
                grouped_data[stock_code][trade_date] = []
            
            grouped_data[stock_code][trade_date].append(item.dict())
        
        # 批量保存到数据库
        total_saved = 0
        for stock_code, date_groups in grouped_data.items():
            for trade_date, items in date_groups.items():
                try:
                    saved_items = await stock_storage.batch_save_stock_daily(
                        stock_code, items, trade_date
                    )
                    total_saved += len(saved_items)
                except Exception as e:
                    # 记录错误但继续处理其他组
                    pass
        
        # 清除相关缓存
        # TODO: 实现缓存清理
        
        return {
            "status": "success",
            "message": f"成功保存{total_saved}条股票日线数据",
            "count": total_saved
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量创建股票日线数据失败: {str(e)}")

# 更新股票日线数据
@router.put("/{stock_code}/{date}", response_model=StockDailyData)
async def update_stock_daily(
    stock_code: str = Path(..., description="股票代码"),
    date_str: str = Path(..., description="交易日期，格式：YYYY-MM-DD"),
    data: StockDailyUpdate = Body(...),
    stock_storage: StockStorageService = Depends(get_stock_storage)
):
    """
    更新指定股票在指定日期的日线数据
    """
    try:
        # 解析日期
        try:
            trade_date = datetime.strptime(date_str, "%Y-%m-%d").date()
        except ValueError:
            raise HTTPException(status_code=400, detail="日期格式无效，应为YYYY-MM-DD")
        
        # 检查数据是否存在
        daily_data = await stock_storage.get_stock_daily(stock_code, trade_date, trade_date)
        if not daily_data:
            raise HTTPException(
                status_code=404, 
                detail=f"未找到股票 {stock_code} 在 {date_str} 的日线数据"
            )
        
        # 更新数据库
        update_data = {k: v for k, v in data.dict().items() if v is not None}
        updated_daily = await stock_storage.update_stock_daily(
            stock_code, trade_date, update_data
        )
        
        # 清除相关缓存
        # TODO: 实现缓存清理
        
        return updated_daily
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新股票日线数据失败: {str(e)}")

# 删除股票日线数据
@router.delete("/{stock_code}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_stock_daily(
    stock_code: str = Path(..., description="股票代码"),
    start_date: Optional[str] = Query(None, description="开始日期，格式：YYYY-MM-DD"),
    end_date: Optional[str] = Query(None, description="结束日期，格式：YYYY-MM-DD"),
    stock_storage: StockStorageService = Depends(get_stock_storage)
):
    """
    删除指定股票在日期范围内的日线数据
    """
    try:
        # 设置默认日期
        if not end_date and not start_date:
            raise HTTPException(
                status_code=400, 
                detail="必须至少提供开始日期或结束日期中的一个"
            )
        
        # 删除数据
        success = await stock_storage.delete_stock_daily(stock_code, start_date, end_date)
        if not success:
            raise HTTPException(
                status_code=404, 
                detail=f"未找到股票 {stock_code} 在指定日期范围内的数据"
            )
        
        # 清除相关缓存
        # TODO: 实现缓存清理
        
        return None
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除股票日线数据失败: {str(e)}")