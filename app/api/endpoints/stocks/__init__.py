"""
股票数据和列表API
提供股票基本信息和日线数据的CRUD操作
"""
from fastapi import APIRouter

from app.api.endpoints.stocks.stock_list import router as stock_list_router
from app.api.endpoints.stocks.stock_data import router as stock_data_router
from app.api.endpoints.stocks.stock_search import router as stock_search_router

# 创建股票API路由器
router = APIRouter()

# 注册子路由
router.include_router(stock_list_router, prefix="/list")
router.include_router(stock_data_router, prefix="/data")
router.include_router(stock_search_router, prefix="/search")