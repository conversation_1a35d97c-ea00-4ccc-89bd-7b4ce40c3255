"""
股票列表REST API接口
提供股票基本信息的CRUD操作
"""
from typing import List, Optional, Dict, Any
from datetime import date
from fastapi import APIRouter, Depends, Query, HTTPException, Path, Body, status
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel, Field

from app.core.database import get_db
from app.services.storage.stock_storage import StockStorageService
from app.core.exceptions import DatabaseException
from app.core.cache import cache_result, get_cached_result

# 创建路由器
router = APIRouter(
    tags=["Stocks"],
    responses={
        404: {"description": "股票不存在"},
        500: {"description": "数据库操作失败"}
    }
)

# 定义请求和响应模型
class StockInfoBase(BaseModel):
    """股票基本信息基础模型"""
    code: str = Field(..., description="股票代码，如：601398")
    name: str = Field(..., description="股票名称，如：工商银行")
    exchange: str = Field(..., description="交易所代码，如：SH, SZ")
    industry: Optional[str] = Field(None, description="行业分类")
    sector: Optional[str] = Field(None, description="股票所属板块")
    listing_date: Optional[date] = Field(None, description="上市日期")
    total_shares: Optional[int] = Field(None, description="总股本(股)")
    circulating_shares: Optional[int] = Field(None, description="流通股本(股)")
    company_profile: Optional[str] = Field(None, description="公司简介")
    is_active: bool = Field(True, description="是否在列表中显示")
    market_cap: Optional[int] = Field(None, description="市值(元)")

class StockInfoCreate(StockInfoBase):
    """创建股票信息模型"""
    pass

class StockInfoUpdate(BaseModel):
    """更新股票信息模型"""
    name: Optional[str] = Field(None, description="股票名称")
    exchange: Optional[str] = Field(None, description="交易所代码")
    industry: Optional[str] = Field(None, description="行业分类")
    sector: Optional[str] = Field(None, description="股票所属板块")
    listing_date: Optional[date] = Field(None, description="上市日期")
    total_shares: Optional[int] = Field(None, description="总股本(股)")
    circulating_shares: Optional[int] = Field(None, description="流通股本(股)")
    company_profile: Optional[str] = Field(None, description="公司简介")
    is_active: Optional[bool] = Field(None, description="是否在列表中显示")
    market_cap: Optional[int] = Field(None, description="市值(元)")

class StockInfoResponse(StockInfoBase):
    """股票信息响应模型"""
    full_code: str = Field(..., description="完整股票代码，如：SH601398")

    class Config:
        orm_mode = True

# 依赖注入：创建股票存储服务
async def get_stock_storage(db: AsyncSession = Depends(get_db)):
    """获取股票存储服务实例"""
    return StockStorageService(db)

# 获取股票列表
@router.get("/", response_model=List[StockInfoResponse])
async def get_stocks(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的最大记录数"),
    industry: Optional[str] = Query(None, description="按行业筛选"),
    is_active: Optional[bool] = Query(None, description="按是否活跃筛选"),
    stock_storage: StockStorageService = Depends(get_stock_storage)
):
    """
    获取股票列表，支持分页和筛选
    """
    try:
        # 尝试从缓存获取
        cache_key = f"stocks:list:{skip}:{limit}:{industry}:{is_active}"
        cached_data = await get_cached_result(cache_key)
        if cached_data:
            return cached_data
        
        # 从数据库获取
        stocks = await stock_storage.get_stock_list(skip, limit, industry, is_active)
        
        # 缓存结果
        await cache_result(cache_key, stocks, expire=3600)  # 缓存1小时
        
        return stocks
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取股票列表失败: {str(e)}")

# 获取单个股票信息
@router.get("/{stock_code}", response_model=StockInfoResponse)
async def get_stock(
    stock_code: str = Path(..., description="股票代码"),
    stock_storage: StockStorageService = Depends(get_stock_storage)
):
    """
    根据股票代码获取单个股票信息
    """
    try:
        # 尝试从缓存获取
        cache_key = f"stocks:info:{stock_code}"
        cached_data = await get_cached_result(cache_key)
        if cached_data:
            return cached_data
        
        # 从数据库获取
        stock = await stock_storage.get_stock_info(stock_code)
        if not stock:
            raise HTTPException(status_code=404, detail=f"股票 {stock_code} 不存在")
        
        # 缓存结果
        await cache_result(cache_key, stock, expire=3600)  # 缓存1小时
        
        return stock
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取股票 {stock_code} 信息失败: {str(e)}")

# 创建股票信息
@router.post("/", response_model=StockInfoResponse, status_code=status.HTTP_201_CREATED)
async def create_stock(
    stock_info: StockInfoCreate,
    stock_storage: StockStorageService = Depends(get_stock_storage)
):
    """
    创建新的股票信息
    """
    try:
        # 检查是否已存在
        existing_stock = await stock_storage.get_stock_info(stock_info.code)
        if existing_stock:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail=f"股票代码 {stock_info.code} 已存在"
            )
        
        # 计算完整股票代码
        full_code = f"{stock_info.exchange}{stock_info.code}"
        stock_data = stock_info.dict()
        stock_data["full_code"] = full_code
        
        # 保存到数据库
        new_stock = await stock_storage.save_stock_info(stock_data)
        
        # 清除相关缓存
        # TODO: 实现缓存清理
        
        return new_stock
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建股票信息失败: {str(e)}")

# 更新股票信息
@router.put("/{stock_code}", response_model=StockInfoResponse)
async def update_stock(
    stock_code: str = Path(..., description="股票代码"),
    stock_update: StockInfoUpdate = Body(...),
    stock_storage: StockStorageService = Depends(get_stock_storage)
):
    """
    更新股票信息
    """
    try:
        # 检查是否存在
        existing_stock = await stock_storage.get_stock_info(stock_code)
        if not existing_stock:
            raise HTTPException(status_code=404, detail=f"股票代码 {stock_code} 不存在")
        
        # 更新数据库
        update_data = {k: v for k, v in stock_update.dict().items() if v is not None}
        updated_stock = await stock_storage.update_stock_info(stock_code, update_data)
        
        # 清除相关缓存
        cache_key = f"stocks:info:{stock_code}"
        await cache_result(cache_key, None, expire=0)  # 删除缓存
        
        return updated_stock
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新股票信息失败: {str(e)}")

# 删除股票信息
@router.delete("/{stock_code}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_stock(
    stock_code: str = Path(..., description="股票代码"),
    stock_storage: StockStorageService = Depends(get_stock_storage)
):
    """
    删除股票信息
    """
    try:
        # 检查是否存在
        existing_stock = await stock_storage.get_stock_info(stock_code)
        if not existing_stock:
            raise HTTPException(status_code=404, detail=f"股票代码 {stock_code} 不存在")
        
        # 从数据库中删除
        success = await stock_storage.delete_stock_info(stock_code)
        if not success:
            raise HTTPException(status_code=500, detail=f"删除股票 {stock_code} 失败")
        
        # 清除相关缓存
        cache_key = f"stocks:info:{stock_code}"
        await cache_result(cache_key, None, expire=0)  # 删除缓存
        
        return None
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除股票信息失败: {str(e)}")