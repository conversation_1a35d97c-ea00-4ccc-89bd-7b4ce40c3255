"""
缓存模块

提供内存缓存和Redis缓存功能，用于存储临时计算结果，
减少重复计算，提高API性能。
"""
import json
from typing import Any, Optional, Dict, Union
from datetime import datetime, timedelta
import asyncio
import time
from app.core import logging
from app.core.config import settings

logger = logging.getLogger(__name__)

# 简单的内存缓存实现
class MemoryCache:
    """简单的内存缓存实现"""
    
    _cache: Dict[str, Dict[str, Any]] = {}
    
    @classmethod
    async def get(cls, key: str) -> Optional[Any]:
        """获取缓存值
        
        Args:
            key: 缓存键
            
        Returns:
            缓存的值，如果不存在或已过期则返回None
        """
        if key not in cls._cache:
            return None
        
        cache_item = cls._cache[key]
        # 检查是否已过期
        if cache_item['expires_at'] < time.time():
            # 已过期，删除并返回None
            del cls._cache[key]
            return None
            
        return cache_item['value']
    
    @classmethod
    async def set(cls, key: str, value: Any, expire: int = 3600) -> None:
        """设置缓存值
        
        Args:
            key: 缓存键
            value: 要缓存的值
            expire: 过期时间（秒），默认1小时
        """
        cls._cache[key] = {
            'value': value,
            'expires_at': time.time() + expire
        }
    
    @classmethod
    async def delete(cls, key: str) -> None:
        """删除缓存值
        
        Args:
            key: 缓存键
        """
        if key in cls._cache:
            del cls._cache[key]
    
    @classmethod
    async def clear(cls) -> None:
        """清空所有缓存"""
        cls._cache.clear()
    
    @classmethod
    async def clean_expired(cls) -> int:
        """清理过期的缓存项
        
        Returns:
            int: 清理的缓存项数量
        """
        now = time.time()
        expired_keys = [
            key for key, item in cls._cache.items() 
            if item['expires_at'] < now
        ]
        
        for key in expired_keys:
            del cls._cache[key]
        
        return len(expired_keys)

    @classmethod
    async def clear_by_prefix(cls, prefix: str) -> int:
        """根据前缀删除缓存项
        
        Args:
            prefix: 缓存键前缀
            
        Returns:
            int: 删除的缓存项数量
        """
        keys_to_delete = [key for key in cls._cache if key.startswith(prefix)]
        for key in keys_to_delete:
            del cls._cache[key]
        return len(keys_to_delete)

# 选择使用的缓存实现
cache_client = MemoryCache

# TODO: 如果配置了Redis，则使用Redis缓存
# if settings.REDIS_URL:
#     import aioredis
#     # 实现Redis缓存类...

# 公共缓存API
async def get_cached_result(key: str) -> Optional[Any]:
    """获取缓存结果
    
    Args:
        key: 缓存键
        
    Returns:
        缓存的值，如果不存在则返回None
    """
    try:
        return await cache_client.get(key)
    except Exception as e:
        logger.error(f"获取缓存失败: {str(e)}")
        return None

async def cache_result(key: str, value: Any, expire: int = 3600) -> None:
    """缓存结果
    
    Args:
        key: 缓存键
        value: 要缓存的值
        expire: 过期时间（秒），默认1小时
    """
    try:
        await cache_client.set(key, value, expire)
    except Exception as e:
        logger.error(f"设置缓存失败: {str(e)}")

async def delete_cache(key: str) -> None:
    """删除缓存
    
    Args:
        key: 缓存键
    """
    try:
        await cache_client.delete(key)
    except Exception as e:
        logger.error(f"删除缓存失败: {str(e)}")

async def clear_cache() -> None:
    """清空所有缓存"""
    try:
        await cache_client.clear()
    except Exception as e:
        logger.error(f"清空缓存失败: {str(e)}")

async def clear_cache_by_prefix(prefix: str) -> int:
    """根据前缀删除缓存
    
    Args:
        prefix: 缓存键前缀
        
    Returns:
        int: 删除的缓存项数量
    """
    try:
        deleted_count = await cache_client.clear_by_prefix(prefix)
        logger.info(f"缓存已根据前缀 '{prefix}' 清理, {deleted_count} 个条目已删除。")
        return deleted_count
    except Exception as e:
        logger.error(f"根据前缀删除缓存失败: {str(e)}")
        return 0

async def clean_expired_cache() -> int:
    """清理过期的缓存项
    
    Returns:
        int: 清理的缓存项数量
    """
    try:
        return await cache_client.clean_expired()
    except Exception as e:
        logger.error(f"清理过期缓存失败: {str(e)}")
        return 0