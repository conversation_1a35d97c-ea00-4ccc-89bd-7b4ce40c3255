"""
异常处理模块

定义应用中的自定义异常类和异常处理器。
"""

from fastapi import HTTPException, Request, status
from fastapi.responses import JSONResponse
from typing import Any, Dict, Optional, Union


class BaseAppException(Exception):
    """应用基础异常类"""
    
    def __init__(
        self,
        message: str = "发生内部错误",
        code: int = 500,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.code = code
        self.details = details or {}
        super().__init__(self.message)


class NotFoundException(BaseAppException):
    """资源未找到异常"""
    
    def __init__(
        self,
        message: str = "请求的资源不存在",
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message=message, code=404, details=details)


class ValidationException(BaseAppException):
    """数据验证异常"""
    
    def __init__(
        self,
        message: str = "数据验证失败",
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message=message, code=422, details=details)


class DatabaseException(BaseAppException):
    """数据库操作异常"""
    
    def __init__(
        self,
        message: str = "数据库操作失败",
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message=message, code=500, details=details)


class APIException(BaseAppException):
    """API调用异常"""
    
    def __init__(
        self,
        message: str = "外部API调用失败",
        code: int = 502,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message=message, code=code, details=details)


class AuthException(BaseAppException):
    """认证授权异常"""
    
    def __init__(
        self,
        message: str = "认证失败或权限不足",
        code: int = 401,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message=message, code=code, details=details)


# 异常处理器
async def app_exception_handler(request: Request, exc: BaseAppException) -> JSONResponse:
    """处理应用自定义异常"""
    content = {
        "message": exc.message,
        "code": exc.code
    }
    
    if exc.details:
        content["details"] = exc.details
    
    return JSONResponse(
        status_code=exc.code,
        content=content
    )


# 注册异常处理器
def register_exception_handlers(app):
    """向FastAPI应用注册异常处理器"""
    app.add_exception_handler(BaseAppException, app_exception_handler)
