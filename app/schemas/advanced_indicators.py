"""
高级技术指标相关的Pydantic模型和Schema定义

用于API请求验证和响应格式化。
"""
from typing import List, Optional, Dict, Any
from datetime import date, datetime
from pydantic import BaseModel, Field, validator
from enum import Enum


class PeriodTypeEnum(str, Enum):
    """数据周期枚举"""
    DAILY = "dh"
    WEEKLY = "wh"
    MONTHLY = "mh"
    MIN_15 = "15m"
    MIN_30 = "30m"
    MIN_60 = "60m"


class PeriodInfo(BaseModel):
    """数据周期信息"""
    index: int = Field(..., description="周期索引")
    code: str = Field(..., description="周期代码")
    name: str = Field(..., description="周期名称")
    description: str = Field(..., description="周期描述")


class BollingerBandsRequest(BaseModel):
    """Bollinger Bands请求模型"""
    start_date: Optional[str] = Field(None, description="开始日期 (YYYY-MM-DD)")
    end_date: Optional[str] = Field(None, description="结束日期 (YYYY-MM-DD)")
    window: int = Field(20, ge=5, le=100, description="布林带窗口大小")
    std_dev: float = Field(2.0, ge=1.0, le=5.0, description="标准差倍数")
    
    @validator('start_date', 'end_date')
    def validate_date_format(cls, v):
        if v is not None:
            try:
                datetime.strptime(v, '%Y-%m-%d')
            except ValueError:
                raise ValueError('日期格式必须为 YYYY-MM-DD')
        return v


class VolumeAnalysisRequest(BaseModel):
    """成交量分析请求模型"""
    start_date: Optional[str] = Field(None, description="开始日期 (YYYY-MM-DD)")
    end_date: Optional[str] = Field(None, description="结束日期 (YYYY-MM-DD)")
    
    @validator('start_date', 'end_date')
    def validate_date_format(cls, v):
        if v is not None:
            try:
                datetime.strptime(v, '%Y-%m-%d')
            except ValueError:
                raise ValueError('日期格式必须为 YYYY-MM-DD')
        return v


class VolumeBollingerRequest(BaseModel):
    """成交量Bollinger请求模型"""
    start_date: Optional[str] = Field(None, description="开始日期 (YYYY-MM-DD)")
    end_date: Optional[str] = Field(None, description="结束日期 (YYYY-MM-DD)")
    window: int = Field(20, ge=5, le=100, description="布林带窗口大小")
    lag: int = Field(2, ge=1, le=10, description="移动平均滞后期")
    
    @validator('start_date', 'end_date')
    def validate_date_format(cls, v):
        if v is not None:
            try:
                datetime.strptime(v, '%Y-%m-%d')
            except ValueError:
                raise ValueError('日期格式必须为 YYYY-MM-DD')
        return v


class KDJEnhancedRequest(BaseModel):
    """增强版KDJ请求模型"""
    start_date: Optional[str] = Field(None, description="开始日期 (YYYY-MM-DD)")
    end_date: Optional[str] = Field(None, description="结束日期 (YYYY-MM-DD)")
    k_period: int = Field(9, ge=3, le=30, description="K值计算周期")
    d_period: int = Field(3, ge=2, le=10, description="D值平滑周期")
    j_period: int = Field(3, ge=2, le=10, description="J值计算周期")
    
    @validator('start_date', 'end_date')
    def validate_date_format(cls, v):
        if v is not None:
            try:
                datetime.strptime(v, '%Y-%m-%d')
            except ValueError:
                raise ValueError('日期格式必须为 YYYY-MM-DD')
        return v


class TradingSignalsRequest(BaseModel):
    """交易信号请求模型"""
    start_date: Optional[str] = Field(None, description="开始日期 (YYYY-MM-DD)")
    end_date: Optional[str] = Field(None, description="结束日期 (YYYY-MM-DD)")
    bollinger_window: int = Field(20, ge=5, le=100, description="布林带窗口大小")
    lag: int = Field(2, ge=1, le=10, description="移动平均滞后期")
    
    @validator('start_date', 'end_date')
    def validate_date_format(cls, v):
        if v is not None:
            try:
                datetime.strptime(v, '%Y-%m-%d')
            except ValueError:
                raise ValueError('日期格式必须为 YYYY-MM-DD')
        return v


class CompleteIndicatorsRequest(BaseModel):
    """完整技术指标请求模型"""
    start_date: Optional[str] = Field(None, description="开始日期 (YYYY-MM-DD)")
    end_date: Optional[str] = Field(None, description="结束日期 (YYYY-MM-DD)")
    period_index: int = Field(1, ge=1, le=6, description="周期索引")
    bollinger_window: int = Field(20, ge=5, le=100, description="布林带窗口大小")
    lag: int = Field(2, ge=1, le=10, description="移动平均滞后期")
    
    @validator('start_date', 'end_date')
    def validate_date_format(cls, v):
        if v is not None:
            try:
                datetime.strptime(v, '%Y-%m-%d')
            except ValueError:
                raise ValueError('日期格式必须为 YYYY-MM-DD')
        return v


# 响应模型已在 advanced_indicators.py 中定义
class BollingerBandsResponse(BaseModel):
    """Bollinger Bands响应模型"""
    middle: List[float] = Field(..., description="中轨数据")
    upper: List[float] = Field(..., description="上轨数据")
    lower: List[float] = Field(..., description="下轨数据")


class VolumeAnalysisResponse(BaseModel):
    """成交量分析响应模型"""
    in_volume: List[float] = Field(..., description="内盘成交量")
    ex_volume: List[float] = Field(..., description="外盘成交量")
    volume_difference: List[float] = Field(..., description="成交量差异")


class VolumeBollingerResponse(BaseModel):
    """成交量Bollinger响应模型"""
    middle: List[float] = Field(..., description="成交量中轨")
    upper: List[float] = Field(..., description="成交量上轨")
    lower: List[float] = Field(..., description="成交量下轨")
    smoothed_volume: List[float] = Field(..., description="平滑后的成交量")


class KDJResponse(BaseModel):
    """KDJ指标响应模型"""
    K: List[float] = Field(..., description="K值")
    D: List[float] = Field(..., description="D值")
    J: List[float] = Field(..., description="J值")


class TradingSignalsResponse(BaseModel):
    """交易信号响应模型"""
    # 原有信号（保留兼容性）
    buy_signals: List[int] = Field(default_factory=list, description="买入信号位置")
    sell_signals: List[int] = Field(default_factory=list, description="卖出信号位置")
    
    # 新的VPKM策略信号
    buy_indices_VPKM: List[int] = Field(default_factory=list, description="VPKM买入信号位置")
    sell_indices_VPKM_tp: List[int] = Field(default_factory=list, description="VPKM止盈信号位置")
    sell_indices_VPKM_sl: List[int] = Field(default_factory=list, description="VPKM止损信号位置")


class ParametersResponse(BaseModel):
    """参数响应模型"""
    bollinger_window: int = Field(..., description="布林带窗口大小")
    lag: int = Field(..., description="移动平均滞后期")
    period_index: int = Field(..., description="周期索引")


class DateRangeResponse(BaseModel):
    """日期范围响应模型"""
    start: Optional[str] = Field(None, description="开始日期")
    end: Optional[str] = Field(None, description="结束日期")


class CompleteIndicatorsResponse(BaseModel):
    """完整技术指标响应模型"""
    period_type: str = Field(..., description="数据周期类型")
    data_points: int = Field(..., description="数据点数量")
    date_range: DateRangeResponse = Field(..., description="数据日期范围")
    price_bollinger: BollingerBandsResponse = Field(..., description="价格布林带")
    volume_analysis: VolumeAnalysisResponse = Field(..., description="成交量分析")
    volume_bollinger: VolumeBollingerResponse = Field(..., description="成交量布林带")
    kdj_indicators: KDJResponse = Field(..., description="KDJ指标")
    trading_signals: TradingSignalsResponse = Field(..., description="交易信号")
    parameters: ParametersResponse = Field(..., description="计算参数")
    
    # 新增VPKM策略相关字段
    vpkm_volume_bollinger_on_log_vol: Optional[Dict[str, List[float]]] = Field(None, description="VPKM成交量布林带（基于log_vol）")
    vpkm_log_vol: Optional[List[float]] = Field(None, description="VPKM标准化并平滑的成交量差异")
    original_volume_bollinger_on_raw_diff: Optional[Dict[str, List[float]]] = Field(None, description="原始成交量布林带（基于原始差异）")


class PeriodsListResponse(BaseModel):
    """数据周期列表响应模型"""
    periods: List[PeriodInfo] = Field(..., description="支持的数据周期列表")


class ErrorResponse(BaseModel):
    """错误响应模型"""
    detail: str = Field(..., description="错误详情")
    error_code: Optional[str] = Field(None, description="错误代码")
    timestamp: datetime = Field(default_factory=datetime.now, description="错误时间")


class SuccessResponse(BaseModel):
    """成功响应模型"""
    status: str = Field("success", description="响应状态")
    message: str = Field(..., description="响应消息")
    data: Optional[Dict[str, Any]] = Field(None, description="响应数据")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间")
